// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		00E356F31AD99517003FC87E /* jcpJiomartTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* jcpJiomartTests.m */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		1E8490B92B182D9FB8FD2DF1 /* Pods_JioMart.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 36A39566ADADDAC4432E95A8 /* Pods_JioMart.framework */; };
		48FEAABE4262A5E3786A9B21 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = EC2490CF464E66F1274E18D9 /* PrivacyInfo.xcprivacy */; };
		6238A8292DBFBA6C0063A7D1 /* JMRN.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6238A8282DBFBA6C0063A7D1 /* JMRN.swift */; };
		62B7AB622E17F372001E8F7D /* JMRNBundleDownLoad.swift in Sources */ = {isa = PBXBuildFile; fileRef = 62B7AB5E2E17F372001E8F7D /* JMRNBundleDownLoad.swift */; };
		62B7AB632E17F372001E8F7D /* JMRNViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 62B7AB5F2E17F372001E8F7D /* JMRNViewController.swift */; };
		62B7AB6D2E17F496001E8F7D /* rnBundle.zip in Resources */ = {isa = PBXBuildFile; fileRef = 62B7AB6C2E17F496001E8F7D /* rnBundle.zip */; };
		62D9055D2DBB8AC200D57CD9 /* JMRNAddressModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 62D905582DBB8AC200D57CD9 /* JMRNAddressModule.m */; };
		62D9055E2DBB8AC200D57CD9 /* JMRNAddressModule.swift in Sources */ = {isa = PBXBuildFile; fileRef = 62D905592DBB8AC200D57CD9 /* JMRNAddressModule.swift */; };
		6BF04C622E251B2900C6C5F1 /* JMNativeCacheModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 6BF04C5F2E251B2900C6C5F1 /* JMNativeCacheModule.m */; };
		6BF04C632E251B2900C6C5F1 /* JMNativeCacheModule.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6BF04C602E251B2900C6C5F1 /* JMNativeCacheModule.swift */; };
		6BF04C662E251BD000C6C5F1 /* JioMart.xcdatamodeld in Sources */ = {isa = PBXBuildFile; fileRef = 6BF04C642E251BD000C6C5F1 /* JioMart.xcdatamodeld */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		AD710F3A533FE4820AD09E29 /* (null) in Frameworks */ = {isa = PBXBuildFile; };
		B315E3B82E3221FB001FC53E /* JMCoreDataAPIResponseModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = B315E3B72E3221F9001FC53E /* JMCoreDataAPIResponseModel.swift */; };
		B315E4592E32412B001FC53E /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = B315E4582E32412B001FC53E /* GoogleService-Info.plist */; };
		B32BCEFB2E096BC500EBDBE4 /* JMEventEmitter.m in Sources */ = {isa = PBXBuildFile; fileRef = B32BCEFA2E096BC500EBDBE4 /* JMEventEmitter.m */; };
		B3637B042E0C284900BDD69A /* JMConstant.swift in Sources */ = {isa = PBXBuildFile; fileRef = B3637B032E0C284200BDD69A /* JMConstant.swift */; };
		B36B31222E339E8400463C96 /* StringUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = B36B31212E339E8400463C96 /* StringUtil.swift */; };
		B3D36CC22E0AAF4B002F8C44 /* NSDictionary+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = B3D36CC12E0AAF46002F8C44 /* NSDictionary+Extension.m */; };
		B3F137D12E3277F10057CA7B /* JMCacheModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = B3F137D02E3277F10057CA7B /* JMCacheModel.swift */; };
		C1BF399D1FB94B9C92B5E96F /* JioType-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0147000A057C479D92FB5E43 /* JioType-Medium.ttf */; };
		C8B5FDAEF1B8480A9BACFA1B /* JioType-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 5214130F71AD4BC984A35427 /* JioType-Black.ttf */; };
		CB3F853EF780C9AE91CDBEFA /* Pods_JioMart_JioMartTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2FDF63C3B645AB0F06E3E3D6 /* Pods_JioMart_JioMartTests.framework */; };
		EFF085D82806471A837B6231 /* JioType-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CFA2AB4462E14424B8CC8B4C /* JioType-Bold.ttf */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = JioMart;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		62B7A9792E15758E001E8F7D /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		00E356EE1AD99517003FC87E /* JioMartTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = JioMartTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* jcpJiomartTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = jcpJiomartTests.m; sourceTree = "<group>"; };
		0147000A057C479D92FB5E43 /* JioType-Medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "JioType-Medium.ttf"; path = "../node_modules/@jio/rn_components/assets/fonts/JioType-Medium.ttf"; sourceTree = "<group>"; };
		0B205ED3D50DADA8836B144E /* Pods-JioMart-JioMartTests.stagingdebug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JioMart-JioMartTests.stagingdebug.xcconfig"; path = "Target Support Files/Pods-JioMart-JioMartTests/Pods-JioMart-JioMartTests.stagingdebug.xcconfig"; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* JioMart.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = JioMart.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = JioMart/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = JioMart/AppDelegate.mm; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = JioMart/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = JioMart/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = JioMart/main.m; sourceTree = "<group>"; };
		19B0491DCD8FCDE442E2A300 /* Pods-JioMart.productionrelease.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JioMart.productionrelease.xcconfig"; path = "Target Support Files/Pods-JioMart/Pods-JioMart.productionrelease.xcconfig"; sourceTree = "<group>"; };
		1E64187AC8E207597D33A337 /* Pods-JioMart-JioMartTests.stagingrelease.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JioMart-JioMartTests.stagingrelease.xcconfig"; path = "Target Support Files/Pods-JioMart-JioMartTests/Pods-JioMart-JioMartTests.stagingrelease.xcconfig"; sourceTree = "<group>"; };
		2FDF63C3B645AB0F06E3E3D6 /* Pods_JioMart_JioMartTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_JioMart_JioMartTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		36A39566ADADDAC4432E95A8 /* Pods_JioMart.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_JioMart.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		3DBFB4A8F322D7B59EF84061 /* Pods-JioMart.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JioMart.debug.xcconfig"; path = "Target Support Files/Pods-JioMart/Pods-JioMart.debug.xcconfig"; sourceTree = "<group>"; };
		5214130F71AD4BC984A35427 /* JioType-Black.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "JioType-Black.ttf"; path = "../node_modules/@jio/rn_components/assets/fonts/JioType-Black.ttf"; sourceTree = "<group>"; };
		6238A8272DBFBA4F0063A7D1 /* JioMart-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "JioMart-Bridging-Header.h"; sourceTree = "<group>"; };
		6238A8282DBFBA6C0063A7D1 /* JMRN.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = JMRN.swift; sourceTree = "<group>"; };
		62B7AB5E2E17F372001E8F7D /* JMRNBundleDownLoad.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = JMRNBundleDownLoad.swift; sourceTree = "<group>"; };
		62B7AB5F2E17F372001E8F7D /* JMRNViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = JMRNViewController.swift; sourceTree = "<group>"; };
		62B7AB6C2E17F496001E8F7D /* rnBundle.zip */ = {isa = PBXFileReference; lastKnownFileType = archive.zip; path = rnBundle.zip; sourceTree = "<group>"; };
		62D905582DBB8AC200D57CD9 /* JMRNAddressModule.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = JMRNAddressModule.m; sourceTree = "<group>"; };
		62D905592DBB8AC200D57CD9 /* JMRNAddressModule.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = JMRNAddressModule.swift; sourceTree = "<group>"; };
		6BF04C5F2E251B2900C6C5F1 /* JMNativeCacheModule.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = JMNativeCacheModule.m; sourceTree = "<group>"; };
		6BF04C602E251B2900C6C5F1 /* JMNativeCacheModule.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = JMNativeCacheModule.swift; sourceTree = "<group>"; };
		6BF04C652E251BD000C6C5F1 /* JioMart.xcdatamodel */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcdatamodel; path = JioMart.xcdatamodel; sourceTree = "<group>"; };
		71D795D597937881F83AFDB3 /* Pods-JioMart.productiondebug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JioMart.productiondebug.xcconfig"; path = "Target Support Files/Pods-JioMart/Pods-JioMart.productiondebug.xcconfig"; sourceTree = "<group>"; };
		81A53D0925DEC3EB9C902812 /* Pods-JioMart.stagingdebug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JioMart.stagingdebug.xcconfig"; path = "Target Support Files/Pods-JioMart/Pods-JioMart.stagingdebug.xcconfig"; sourceTree = "<group>"; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = JioMart/LaunchScreen.storyboard; sourceTree = "<group>"; };
		B315E3B72E3221F9001FC53E /* JMCoreDataAPIResponseModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = JMCoreDataAPIResponseModel.swift; sourceTree = "<group>"; };
		B315E4562E324122001FC53E /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		B315E4582E32412B001FC53E /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		B32BCEFA2E096BC500EBDBE4 /* JMEventEmitter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = JMEventEmitter.m; sourceTree = "<group>"; };
		B32BCEFE2E096F7500EBDBE4 /* JMEventEmitter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = JMEventEmitter.h; sourceTree = "<group>"; };
		B32BCEFF2E09741600EBDBE4 /* JioMart.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = JioMart.entitlements; path = JioMart/JioMart.entitlements; sourceTree = "<group>"; };
		B36379E92E0AC23300BDD69A /* NSDictionary+Extension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSDictionary+Extension.h"; sourceTree = "<group>"; };
		B3637B032E0C284200BDD69A /* JMConstant.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = JMConstant.swift; sourceTree = "<group>"; };
		B36B31212E339E8400463C96 /* StringUtil.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StringUtil.swift; sourceTree = "<group>"; };
		B3D36CC12E0AAF46002F8C44 /* NSDictionary+Extension.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSDictionary+Extension.m"; sourceTree = "<group>"; };
		B3F137D02E3277F10057CA7B /* JMCacheModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = JMCacheModel.swift; sourceTree = "<group>"; };
		BD52DA79212C48FDF7AEEFAA /* Pods-JioMart-JioMartTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JioMart-JioMartTests.debug.xcconfig"; path = "Target Support Files/Pods-JioMart-JioMartTests/Pods-JioMart-JioMartTests.debug.xcconfig"; sourceTree = "<group>"; };
		C091B1866F8B2479F9650658 /* Pods-JioMart-JioMartTests.productiondebug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JioMart-JioMartTests.productiondebug.xcconfig"; path = "Target Support Files/Pods-JioMart-JioMartTests/Pods-JioMart-JioMartTests.productiondebug.xcconfig"; sourceTree = "<group>"; };
		CDD98265CE77E59F7576C6FE /* Pods-JioMart-JioMartTests.productionrelease.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JioMart-JioMartTests.productionrelease.xcconfig"; path = "Target Support Files/Pods-JioMart-JioMartTests/Pods-JioMart-JioMartTests.productionrelease.xcconfig"; sourceTree = "<group>"; };
		CFA2AB4462E14424B8CC8B4C /* JioType-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "JioType-Bold.ttf"; path = "../node_modules/@jio/rn_components/assets/fonts/JioType-Bold.ttf"; sourceTree = "<group>"; };
		EC2490CF464E66F1274E18D9 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xml; name = PrivacyInfo.xcprivacy; path = JioMart/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		F8D7C82820653B38BA90B968 /* Pods-JioMart.stagingrelease.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JioMart.stagingrelease.xcconfig"; path = "Target Support Files/Pods-JioMart/Pods-JioMart.stagingrelease.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedGroupBuildPhaseMembershipExceptionSet section */
		62B7A97A2E15758E001E8F7D /* PBXFileSystemSynchronizedGroupBuildPhaseMembershipExceptionSet */ = {
			isa = PBXFileSystemSynchronizedGroupBuildPhaseMembershipExceptionSet;
			attributesByRelativePath = {
				JioAdsFramework.xcframework = (
					CodeSignOnCopy,
					RemoveHeadersOnCopy,
				);
			};
			buildPhase = 62B7A9792E15758E001E8F7D /* Embed Frameworks */;
			membershipExceptions = (
				JioAdsFramework.xcframework,
			);
		};
/* End PBXFileSystemSynchronizedGroupBuildPhaseMembershipExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		62B7A9732E153FC3001E8F7D /* JMJioAds */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = JMJioAds; sourceTree = "<group>"; };
		62B7A9742E1543E3001E8F7D /* Frameworks */ = {isa = PBXFileSystemSynchronizedRootGroup; exceptions = (62B7A97A2E15758E001E8F7D /* PBXFileSystemSynchronizedGroupBuildPhaseMembershipExceptionSet */, ); explicitFileTypes = {}; explicitFolders = (); path = Frameworks; sourceTree = "<group>"; };
		62D9055F2DBB983600D57CD9 /* JMHelper */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = JMHelper; sourceTree = "<group>"; };
		62D905642DBB9E2A00D57CD9 /* JMModel */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = JMModel; sourceTree = "<group>"; };
		62D905672DBBA1ED00D57CD9 /* JMAddress */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = JMAddress; sourceTree = "<group>"; };
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				AD710F3A533FE4820AD09E29 /* (null) in Frameworks */,
				CB3F853EF780C9AE91CDBEFA /* Pods_JioMart_JioMartTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				1E8490B92B182D9FB8FD2DF1 /* Pods_JioMart.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* jcpJiomartTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* jcpJiomartTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = jcpJiomartTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* JioMart */ = {
			isa = PBXGroup;
			children = (
				B315E4532E3240E4001FC53E /* FirebaseConfiguration */,
				B315E3B62E3221D3001FC53E /* JMDatabase */,
				62B7AB6C2E17F496001E8F7D /* rnBundle.zip */,
				62B7AB612E17F372001E8F7D /* ViewController */,
				62B7A9742E1543E3001E8F7D /* Frameworks */,
				62B7A9732E153FC3001E8F7D /* JMJioAds */,
				B3D36CBF2E0AAF2C002F8C44 /* Utils */,
				B32BCEFF2E09741600EBDBE4 /* JioMart.entitlements */,
				62D905672DBBA1ED00D57CD9 /* JMAddress */,
				62D905642DBB9E2A00D57CD9 /* JMModel */,
				62D9055F2DBB983600D57CD9 /* JMHelper */,
				62D9055C2DBB8AC200D57CD9 /* JMRNBridge */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.mm */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				13B07FB71A68108700A75B9A /* main.m */,
				6238A8272DBFBA4F0063A7D1 /* JioMart-Bridging-Header.h */,
				6238A8282DBFBA6C0063A7D1 /* JMRN.swift */,
				EC2490CF464E66F1274E18D9 /* PrivacyInfo.xcprivacy */,
			);
			name = JioMart;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				36A39566ADADDAC4432E95A8 /* Pods_JioMart.framework */,
				2FDF63C3B645AB0F06E3E3D6 /* Pods_JioMart_JioMartTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		56E48EBAC2ED4481B9A0F9B1 /* Resources */ = {
			isa = PBXGroup;
			children = (
				5214130F71AD4BC984A35427 /* JioType-Black.ttf */,
				CFA2AB4462E14424B8CC8B4C /* JioType-Bold.ttf */,
				0147000A057C479D92FB5E43 /* JioType-Medium.ttf */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		62B7AB602E17F372001E8F7D /* ReactNativeViewController */ = {
			isa = PBXGroup;
			children = (
				62B7AB5E2E17F372001E8F7D /* JMRNBundleDownLoad.swift */,
				62B7AB5F2E17F372001E8F7D /* JMRNViewController.swift */,
			);
			path = ReactNativeViewController;
			sourceTree = "<group>";
		};
		62B7AB612E17F372001E8F7D /* ViewController */ = {
			isa = PBXGroup;
			children = (
				62B7AB602E17F372001E8F7D /* ReactNativeViewController */,
			);
			path = ViewController;
			sourceTree = "<group>";
		};
		62D9055A2DBB8AC200D57CD9 /* JMAddress */ = {
			isa = PBXGroup;
			children = (
				62D905582DBB8AC200D57CD9 /* JMRNAddressModule.m */,
				62D905592DBB8AC200D57CD9 /* JMRNAddressModule.swift */,
			);
			path = JMAddress;
			sourceTree = "<group>";
		};
		62D9055B2DBB8AC200D57CD9 /* NativeModules */ = {
			isa = PBXGroup;
			children = (
				6BF04C612E251B2900C6C5F1 /* JMNativeCacheModule */,
				62D9055A2DBB8AC200D57CD9 /* JMAddress */,
			);
			path = NativeModules;
			sourceTree = "<group>";
		};
		62D9055C2DBB8AC200D57CD9 /* JMRNBridge */ = {
			isa = PBXGroup;
			children = (
				B3C9A7012E05962500B98F66 /* EventEmitter */,
				62D9055B2DBB8AC200D57CD9 /* NativeModules */,
			);
			path = JMRNBridge;
			sourceTree = "<group>";
		};
		6BF04C612E251B2900C6C5F1 /* JMNativeCacheModule */ = {
			isa = PBXGroup;
			children = (
				6BF04C5F2E251B2900C6C5F1 /* JMNativeCacheModule.m */,
				6BF04C602E251B2900C6C5F1 /* JMNativeCacheModule.swift */,
			);
			path = JMNativeCacheModule;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				13B07FAE1A68108700A75B9A /* JioMart */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* jcpJiomartTests */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
				56E48EBAC2ED4481B9A0F9B1 /* Resources */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* JioMart.app */,
				00E356EE1AD99517003FC87E /* JioMartTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		B315E3B62E3221D3001FC53E /* JMDatabase */ = {
			isa = PBXGroup;
			children = (
				B3F137D02E3277F10057CA7B /* JMCacheModel.swift */,
				B315E3B72E3221F9001FC53E /* JMCoreDataAPIResponseModel.swift */,
				6BF04C642E251BD000C6C5F1 /* JioMart.xcdatamodeld */,
			);
			path = JMDatabase;
			sourceTree = "<group>";
		};
		B315E4532E3240E4001FC53E /* FirebaseConfiguration */ = {
			isa = PBXGroup;
			children = (
				B315E4552E3240F7001FC53E /* JioMartSIT */,
				B315E4542E3240EF001FC53E /* JioMart */,
			);
			path = FirebaseConfiguration;
			sourceTree = "<group>";
		};
		B315E4542E3240EF001FC53E /* JioMart */ = {
			isa = PBXGroup;
			children = (
				B315E4562E324122001FC53E /* GoogleService-Info.plist */,
			);
			path = JioMart;
			sourceTree = "<group>";
		};
		B315E4552E3240F7001FC53E /* JioMartSIT */ = {
			isa = PBXGroup;
			children = (
				B315E4582E32412B001FC53E /* GoogleService-Info.plist */,
			);
			path = JioMartSIT;
			sourceTree = "<group>";
		};
		B3637B022E0C283C00BDD69A /* JMConstant */ = {
			isa = PBXGroup;
			children = (
				B3637B032E0C284200BDD69A /* JMConstant.swift */,
			);
			path = JMConstant;
			sourceTree = "<group>";
		};
		B3C9A7012E05962500B98F66 /* EventEmitter */ = {
			isa = PBXGroup;
			children = (
				B32BCEFE2E096F7500EBDBE4 /* JMEventEmitter.h */,
				B32BCEFA2E096BC500EBDBE4 /* JMEventEmitter.m */,
			);
			path = EventEmitter;
			sourceTree = "<group>";
		};
		B3D36CBF2E0AAF2C002F8C44 /* Utils */ = {
			isa = PBXGroup;
			children = (
				B36B31212E339E8400463C96 /* StringUtil.swift */,
				B3637B022E0C283C00BDD69A /* JMConstant */,
				B3D36CC02E0AAF32002F8C44 /* JMExtensions */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		B3D36CC02E0AAF32002F8C44 /* JMExtensions */ = {
			isa = PBXGroup;
			children = (
				B36379E92E0AC23300BDD69A /* NSDictionary+Extension.h */,
				B3D36CC12E0AAF46002F8C44 /* NSDictionary+Extension.m */,
			);
			path = JMExtensions;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				71D795D597937881F83AFDB3 /* Pods-JioMart.productiondebug.xcconfig */,
				81A53D0925DEC3EB9C902812 /* Pods-JioMart.stagingdebug.xcconfig */,
				3DBFB4A8F322D7B59EF84061 /* Pods-JioMart.debug.xcconfig */,
				F8D7C82820653B38BA90B968 /* Pods-JioMart.stagingrelease.xcconfig */,
				19B0491DCD8FCDE442E2A300 /* Pods-JioMart.productionrelease.xcconfig */,
				C091B1866F8B2479F9650658 /* Pods-JioMart-JioMartTests.productiondebug.xcconfig */,
				0B205ED3D50DADA8836B144E /* Pods-JioMart-JioMartTests.stagingdebug.xcconfig */,
				BD52DA79212C48FDF7AEEFAA /* Pods-JioMart-JioMartTests.debug.xcconfig */,
				1E64187AC8E207597D33A337 /* Pods-JioMart-JioMartTests.stagingrelease.xcconfig */,
				CDD98265CE77E59F7576C6FE /* Pods-JioMart-JioMartTests.productionrelease.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* JioMartTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "JioMartTests" */;
			buildPhases = (
				DDE7A5F4DF94DD5A59610EFE /* [CP] Check Pods Manifest.lock */,
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
				5B52901DB520D7AD57722BF3 /* [CP] Embed Pods Frameworks */,
				8811A5BE0A3BE9FA2CD7BBBC /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = JioMartTests;
			productName = jcpJiomartTests;
			productReference = 00E356EE1AD99517003FC87E /* JioMartTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		13B07F861A680F5B00A75B9A /* JioMart */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "JioMart" */;
			buildPhases = (
				05EE50D861F66FF22011D6FA /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				62B7A9792E15758E001E8F7D /* Embed Frameworks */,
				E4632A92009B0F52E8489E04 /* [CP] Embed Pods Frameworks */,
				9C1C69A426E269938A78B1BE /* [CP] Copy Pods Resources */,
				36D756839D74FA9231066650 /* [CP-User] [RNFB] Core Configuration */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				62B7A9732E153FC3001E8F7D /* JMJioAds */,
				62B7A9742E1543E3001E8F7D /* Frameworks */,
				62D9055F2DBB983600D57CD9 /* JMHelper */,
				62D905642DBB9E2A00D57CD9 /* JMModel */,
				62D905672DBBA1ED00D57CD9 /* JMAddress */,
			);
			name = JioMart;
			productName = JioMart;
			productReference = 13B07F961A680F5B00A75B9A /* JioMart.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "JioMart" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			preferredProjectObjectVersion = 54;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* JioMart */,
				00E356ED1AD99517003FC87E /* JioMartTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				48FEAABE4262A5E3786A9B21 /* PrivacyInfo.xcprivacy in Resources */,
				C8B5FDAEF1B8480A9BACFA1B /* JioType-Black.ttf in Resources */,
				EFF085D82806471A837B6231 /* JioType-Bold.ttf in Resources */,
				B315E4592E32412B001FC53E /* GoogleService-Info.plist in Resources */,
				C1BF399D1FB94B9C92B5E96F /* JioType-Medium.ttf in Resources */,
				62B7AB6D2E17F496001E8F7D /* rnBundle.zip in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../node_modules/react-native/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		05EE50D861F66FF22011D6FA /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-JioMart-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		36D756839D74FA9231066650 /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\n\n##########################################################################\n##########################################################################\n#\n#  NOTE THAT IF YOU CHANGE THIS FILE YOU MUST RUN pod install AFTERWARDS\n#\n#  This file is installed as an Xcode build script in the project file\n#  by cocoapods, and you will not see your changes until you pod install\n#\n##########################################################################\n##########################################################################\n\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, firebase.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_analytics_storage\n  _ANALYTICS_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_analytics_storage\")\n  if [[ $_ANALYTICS_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_ANALYTICS_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_storage\n  _ANALYTICS_AD_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_storage\")\n  if [[ $_ANALYTICS_AD_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_user_data\n  _ANALYTICS_AD_USER_DATA=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_user_data\")\n  if [[ $_ANALYTICS_AD_USER_DATA ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_USER_DATA\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_USER_DATA\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.analytics_registration_with_ad_network_enabled\n  _ANALYTICS_REGISTRATION_WITH_AD_NETWORK=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_registration_with_ad_network_enabled\")\n  if [[ $_ANALYTICS_REGISTRATION_WITH_AD_NETWORK ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_REGISTRATION_WITH_AD_NETWORK_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_REGISTRATION_WITH_AD_NETWORK\")\")\n  fi\n\n  # config.google_analytics_automatic_screen_reporting_enabled\n  _ANALYTICS_AUTO_SCREEN_REPORTING=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_automatic_screen_reporting_enabled\")\n  if [[ $_ANALYTICS_AUTO_SCREEN_REPORTING ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAutomaticScreenReportingEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_SCREEN_REPORTING\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		5B52901DB520D7AD57722BF3 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-JioMart-JioMartTests/Pods-JioMart-JioMartTests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-JioMart-JioMartTests/Pods-JioMart-JioMartTests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-JioMart-JioMartTests/Pods-JioMart-JioMartTests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		8811A5BE0A3BE9FA2CD7BBBC /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-JioMart-JioMartTests/Pods-JioMart-JioMartTests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-JioMart-JioMartTests/Pods-JioMart-JioMartTests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-JioMart-JioMartTests/Pods-JioMart-JioMartTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		9C1C69A426E269938A78B1BE /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-JioMart/Pods-JioMart-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-JioMart/Pods-JioMart-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-JioMart/Pods-JioMart-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		DDE7A5F4DF94DD5A59610EFE /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-JioMart-JioMartTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		E4632A92009B0F52E8489E04 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-JioMart/Pods-JioMart-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-JioMart/Pods-JioMart-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-JioMart/Pods-JioMart-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				00E356F31AD99517003FC87E /* jcpJiomartTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				6BF04C662E251BD000C6C5F1 /* JioMart.xcdatamodeld in Sources */,
				13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */,
				B36B31222E339E8400463C96 /* StringUtil.swift in Sources */,
				B3D36CC22E0AAF4B002F8C44 /* NSDictionary+Extension.m in Sources */,
				B32BCEFB2E096BC500EBDBE4 /* JMEventEmitter.m in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
				62D9055D2DBB8AC200D57CD9 /* JMRNAddressModule.m in Sources */,
				6238A8292DBFBA6C0063A7D1 /* JMRN.swift in Sources */,
				B3637B042E0C284900BDD69A /* JMConstant.swift in Sources */,
				62B7AB622E17F372001E8F7D /* JMRNBundleDownLoad.swift in Sources */,
				6BF04C622E251B2900C6C5F1 /* JMNativeCacheModule.m in Sources */,
				6BF04C632E251B2900C6C5F1 /* JMNativeCacheModule.swift in Sources */,
				62B7AB632E17F372001E8F7D /* JMRNViewController.swift in Sources */,
				B3F137D12E3277F10057CA7B /* JMCacheModel.swift in Sources */,
				B315E3B82E3221FB001FC53E /* JMCoreDataAPIResponseModel.swift in Sources */,
				62D9055E2DBB8AC200D57CD9 /* JMRNAddressModule.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* JioMart */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		00E356F71AD99517003FC87E /* StagingRelease */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1E64187AC8E207597D33A337 /* Pods-JioMart-JioMartTests.stagingrelease.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				INFOPLIST_FILE = jcpJiomartTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/JioMart.app/JioMart";
			};
			name = StagingRelease;
		};
		13B07F951A680F5B00A75B9A /* StagingRelease */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F8D7C82820653B38BA90B968 /* Pods-JioMart.stagingrelease.xcconfig */;
			buildSettings = {
				APPSFLYER_APPLE_ID = **********;
				APPSFLYER_DEV_ID = jGgetr8Ui6Vyyzdjmb5QkM;
				APPSFLYER_DOMAIN = jiomart.onelink.me;
				APP_IDENTIFIER = com.jio.jiomart;
				APP_NAME = JioMart;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CLEVERTAP_ACCOUNT_ID = "TEST-98R-W4Z-495Z";
				CLEVERTAP_ACCOUNT_TOKEN = "TEST-140-2bc";
				CODE_SIGN_ENTITLEMENTS = JioMart/JioMart.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution: JIO PLATFORMS LIMITED (VSQ9KLSSUY)";
				CONFIG_ENVIRONMENT = SIT;
				CURRENT_PROJECT_VERSION = 2;
				DEFINES_MODULE = YES;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = VSQ9KLSSUY;
				DISPLAY_NAME = StageJioMart;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
					"$(inherited)",
					"SD_WEBP=1",
					"$(inherited)",
					"PB_FIELD_32BIT=1",
					"PB_NO_PACKED_STRUCTS=1",
					"PB_ENABLE_MALLOC=1",
					"HAVE_GOOGLE_MAPS=1",
				);
				GOOGLE_MAPS_API_KEY = AIzaSyAM2K57_P5ZKGazNvnggfuEfWxv6ZaLWtw;
				INFOPLIST_FILE = JioMart/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.0.51;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -Xcc -fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-google-places-sdk/react_native_google_places_sdk.modulemap\" -Xcc -fmodule-map-file=\"${PODS_ROOT}/Headers/Public/RNReanimated/RNReanimated.modulemap\" -DRELEASE_MODE";
				PRODUCT_BUNDLE_IDENTIFIER = com.jio.staging.jiomart;
				PRODUCT_NAME = JioMart;
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = JIoMartStagingDistribution;
				SWIFT_OBJC_BRIDGING_HEADER = "JioMart-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = StagingRelease;
		};
		83CBBA211A601CBA00E9B192 /* StagingRelease */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
				);
				OTHER_LDFLAGS = "$(inherited)  ";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = StagingRelease;
		};
		B36379EA2E0AD47500BDD69A /* StagingDebug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
				);
				OTHER_LDFLAGS = "$(inherited)  ";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				USE_HERMES = true;
			};
			name = StagingDebug;
		};
		B36379EB2E0AD47500BDD69A /* StagingDebug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 81A53D0925DEC3EB9C902812 /* Pods-JioMart.stagingdebug.xcconfig */;
			buildSettings = {
				APPSFLYER_APPLE_ID = **********;
				APPSFLYER_DEV_ID = jGgetr8Ui6Vyyzdjmb5QkM;
				APPSFLYER_DOMAIN = jiomart.onelink.me;
				APP_IDENTIFIER = com.jio.jiomart;
				APP_NAME = StageJioMart;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CLEVERTAP_ACCOUNT_ID = "TEST-98R-W4Z-495Z";
				CLEVERTAP_ACCOUNT_TOKEN = "TEST-140-2bc";
				CODE_SIGN_ENTITLEMENTS = JioMart/JioMartStagingDebug.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development: Ankit Jayswal (TXGT6777W5)";
				CODE_SIGN_STYLE = Manual;
				CONFIG_ENVIRONMENT = SIT;
				CURRENT_PROJECT_VERSION = 2;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = VSQ9KLSSUY;
				DISPLAY_NAME = StageJioMart;
				ENABLE_BITCODE = NO;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
					"$(inherited)",
					"SD_WEBP=1",
					"$(inherited)",
					"PB_FIELD_32BIT=1",
					"PB_NO_PACKED_STRUCTS=1",
					"PB_ENABLE_MALLOC=1",
					"HAVE_GOOGLE_MAPS=1",
				);
				GOOGLE_MAPS_API_KEY = AIzaSyAM2K57_P5ZKGazNvnggfuEfWxv6ZaLWtw;
				INFOPLIST_FILE = JioMart/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.0.51;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -Xcc -fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-google-places-sdk/react_native_google_places_sdk.modulemap\" -Xcc -fmodule-map-file=\"${PODS_ROOT}/Headers/Public/RNReanimated/RNReanimated.modulemap\" -DDEBUG_MODE";
				PRODUCT_BUNDLE_IDENTIFIER = com.jio.staging.jiomart;
				PRODUCT_NAME = JioMart;
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = JIoMartStagingProvisioningProfile;
				SWIFT_OBJC_BRIDGING_HEADER = "JioMart-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = StagingDebug;
		};
		B36379EC2E0AD47500BDD69A /* StagingDebug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0B205ED3D50DADA8836B144E /* Pods-JioMart-JioMartTests.stagingdebug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = jcpJiomartTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/JioMart.app/JioMart";
			};
			name = StagingDebug;
		};
		B36379ED2E0AD4A200BDD69A /* ProductionDebug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
				);
				OTHER_LDFLAGS = "$(inherited)  ";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				USE_HERMES = true;
			};
			name = ProductionDebug;
		};
		B36379EE2E0AD4A200BDD69A /* ProductionDebug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 71D795D597937881F83AFDB3 /* Pods-JioMart.productiondebug.xcconfig */;
			buildSettings = {
				APPSFLYER_APPLE_ID = **********;
				APPSFLYER_DEV_ID = jGgetr8Ui6Vyyzdjmb5QkM;
				APPSFLYER_DOMAIN = jiomart.onelink.me;
				APP_IDENTIFIER = com.jio.jiomart;
				APP_NAME = StageJioMart;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CLEVERTAP_ACCOUNT_ID = "88R-W4Z-495Z";
				CLEVERTAP_ACCOUNT_TOKEN = "140-2bb";
				CODE_SIGN_ENTITLEMENTS = JioMart/JioMart.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development: Ankit Jayswal (TXGT6777W5)";
				CODE_SIGN_STYLE = Manual;
				CONFIG_ENVIRONMENT = PROD;
				CURRENT_PROJECT_VERSION = 2;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = VSQ9KLSSUY;
				DISPLAY_NAME = JioMart;
				ENABLE_BITCODE = NO;
				GOOGLE_MAPS_API_KEY = AIzaSyCdP5Kx1XxidSUsy4vTArUFrfqGKsmPbh0;
				INFOPLIST_FILE = JioMart/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.0.51;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -Xcc -fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-google-places-sdk/react_native_google_places_sdk.modulemap\" -Xcc -fmodule-map-file=\"${PODS_ROOT}/Headers/Public/RNReanimated/RNReanimated.modulemap\" -DDEBUG_MODE";
				PRODUCT_BUNDLE_IDENTIFIER = com.jio.jiomart;
				PRODUCT_NAME = JioMart;
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = JioMartDevelopmentProvisioningProfile;
				SWIFT_OBJC_BRIDGING_HEADER = "JioMart-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = ProductionDebug;
		};
		B36379EF2E0AD4A200BDD69A /* ProductionDebug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C091B1866F8B2479F9650658 /* Pods-JioMart-JioMartTests.productiondebug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = jcpJiomartTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/JioMart.app/JioMart";
			};
			name = ProductionDebug;
		};
		B36379F02E0AD4BC00BDD69A /* ProductionRelease */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
				);
				OTHER_LDFLAGS = "$(inherited)  ";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = ProductionRelease;
		};
		B36379F12E0AD4BC00BDD69A /* ProductionRelease */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 19B0491DCD8FCDE442E2A300 /* Pods-JioMart.productionrelease.xcconfig */;
			buildSettings = {
				APPSFLYER_APPLE_ID = **********;
				APPSFLYER_DEV_ID = jGgetr8Ui6Vyyzdjmb5QkM;
				APPSFLYER_DOMAIN = jiomart.onelink.me;
				APP_IDENTIFIER = com.jio.jiomart;
				APP_NAME = JioMart;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CLEVERTAP_ACCOUNT_ID = "88R-W4Z-495Z";
				CLEVERTAP_ACCOUNT_TOKEN = "140-2bb";
				CODE_SIGN_ENTITLEMENTS = JioMart/JioMart.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Distribution: JIO PLATFORMS LIMITED (VSQ9KLSSUY)";
				CONFIG_ENVIRONMENT = PROD;
				CURRENT_PROJECT_VERSION = 2;
				DEFINES_MODULE = YES;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = VSQ9KLSSUY;
				DISPLAY_NAME = JioMart;
				GOOGLE_MAPS_API_KEY = AIzaSyCdP5Kx1XxidSUsy4vTArUFrfqGKsmPbh0;
				INFOPLIST_FILE = JioMart/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.0.51;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -Xcc -fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-google-places-sdk/react_native_google_places_sdk.modulemap\" -Xcc -fmodule-map-file=\"${PODS_ROOT}/Headers/Public/RNReanimated/RNReanimated.modulemap\" -DRELEASE_MODE";
				PRODUCT_BUNDLE_IDENTIFIER = com.jio.jiomart;
				PRODUCT_NAME = JioMart;
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = JioMartDistributionProfile;
				SWIFT_OBJC_BRIDGING_HEADER = "JioMart-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = ProductionRelease;
		};
		B36379F22E0AD4BC00BDD69A /* ProductionRelease */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CDD98265CE77E59F7576C6FE /* Pods-JioMart-JioMartTests.productionrelease.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				INFOPLIST_FILE = jcpJiomartTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/JioMart.app/JioMart";
			};
			name = ProductionRelease;
		};
		B36B31062E337F2100463C96 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
				);
				OTHER_LDFLAGS = "$(inherited)  ";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				USE_HERMES = true;
			};
			name = Debug;
		};
		B36B31072E337F2100463C96 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 81A53D0925DEC3EB9C902812 /* Pods-JioMart.stagingdebug.xcconfig */;
			buildSettings = {
				APPSFLYER_APPLE_ID = **********;
				APPSFLYER_DEV_ID = jGgetr8Ui6Vyyzdjmb5QkM;
				APPSFLYER_DOMAIN = jiomart.onelink.me;
				APP_IDENTIFIER = com.jio.jiomart;
				APP_NAME = StageJioMart;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CLEVERTAP_ACCOUNT_ID = "TEST-98R-W4Z-495Z";
				CLEVERTAP_ACCOUNT_TOKEN = "TEST-140-2bc";
				CODE_SIGN_ENTITLEMENTS = JioMart/JioMartStagingDebug.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development: Ankit Jayswal (TXGT6777W5)";
				CODE_SIGN_STYLE = Manual;
				CONFIG_ENVIRONMENT = SIT;
				CURRENT_PROJECT_VERSION = 2;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = VSQ9KLSSUY;
				DISPLAY_NAME = StageJioMart;
				ENABLE_BITCODE = NO;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
					"$(inherited)",
					"SD_WEBP=1",
					"$(inherited)",
					"PB_FIELD_32BIT=1",
					"PB_NO_PACKED_STRUCTS=1",
					"PB_ENABLE_MALLOC=1",
					"HAVE_GOOGLE_MAPS=1",
				);
				GOOGLE_MAPS_API_KEY = AIzaSyAM2K57_P5ZKGazNvnggfuEfWxv6ZaLWtw;
				INFOPLIST_FILE = JioMart/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.0.51;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -Xcc -fmodule-map-file=\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-google-places-sdk/react_native_google_places_sdk.modulemap\" -Xcc -fmodule-map-file=\"${PODS_ROOT}/Headers/Public/RNReanimated/RNReanimated.modulemap\" -DDEBUG_MODE";
				PRODUCT_BUNDLE_IDENTIFIER = com.jio.staging.jiomart;
				PRODUCT_NAME = JioMart;
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = JIoMartStagingProvisioningProfile;
				SWIFT_OBJC_BRIDGING_HEADER = "JioMart-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		B36B31082E337F2100463C96 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0B205ED3D50DADA8836B144E /* Pods-JioMart-JioMartTests.stagingdebug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = jcpJiomartTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/JioMart.app/JioMart";
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "JioMartTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B36379EF2E0AD4A200BDD69A /* ProductionDebug */,
				B36379EC2E0AD47500BDD69A /* StagingDebug */,
				B36B31082E337F2100463C96 /* Debug */,
				00E356F71AD99517003FC87E /* StagingRelease */,
				B36379F22E0AD4BC00BDD69A /* ProductionRelease */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = ProductionRelease;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "JioMart" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B36379EE2E0AD4A200BDD69A /* ProductionDebug */,
				B36379EB2E0AD47500BDD69A /* StagingDebug */,
				B36B31072E337F2100463C96 /* Debug */,
				13B07F951A680F5B00A75B9A /* StagingRelease */,
				B36379F12E0AD4BC00BDD69A /* ProductionRelease */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = ProductionRelease;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "JioMart" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B36379ED2E0AD4A200BDD69A /* ProductionDebug */,
				B36379EA2E0AD47500BDD69A /* StagingDebug */,
				B36B31062E337F2100463C96 /* Debug */,
				83CBBA211A601CBA00E9B192 /* StagingRelease */,
				B36379F02E0AD4BC00BDD69A /* ProductionRelease */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = ProductionRelease;
		};
/* End XCConfigurationList section */

/* Begin XCVersionGroup section */
		6BF04C642E251BD000C6C5F1 /* JioMart.xcdatamodeld */ = {
			isa = XCVersionGroup;
			children = (
				6BF04C652E251BD000C6C5F1 /* JioMart.xcdatamodel */,
			);
			currentVersion = 6BF04C652E251BD000C6C5F1 /* JioMart.xcdatamodel */;
			path = JioMart.xcdatamodeld;
			sourceTree = "<group>";
			versionGroupType = wrapper.xcdatamodel;
		};
/* End XCVersionGroup section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
