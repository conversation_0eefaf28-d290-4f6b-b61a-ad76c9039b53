<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
<key>NSAppTransportSecurity</key>
<dict>
  <key>NSExceptionDomains</key>
  <dict>
    <key>facebook.com</key>
    <dict>
      <key>NSIncludesSubdomains</key>
      <true/>
      <key>NSExceptionRequiresForwardSecrecy</key>
      <false/>
      <key>NSExceptionAllowsInsecureHTTPLoads</key>
      <true/>
    </dict>
    <key>fbcdn.net</key>
    <dict>
      <key>NSIncludesSubdomains</key>
      <true/>
      <key>NSExceptionRequiresForwardSecrecy</key>
      <false/>
      <key>NSExceptionAllowsInsecureHTTPLoads</key>
      <true/>
    </dict>
    <key>akamaihd.net</key>
    <dict>
      <key>NSIncludesSubdomains</key>
      <true/>
      <key>NSExceptionRequiresForwardSecrecy</key>
      <false/>
      <key>NSExceptionAllowsInsecureHTTPLoads</key>
      <false/>
    </dict>
  </dict>
  <key>NSAllowsArbitraryLoads</key>
  <true/>
  <key>NSAllowsLocalNetworking</key>
  <true/>
</dict>

	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>$(DISPLAY_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIcons</key>
	<dict>
		<key>CFBundleAlternateIcons</key>
		<dict/>
	</dict>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.jio.jiomart</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>jiomart</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb318862225974457</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.jio.enterprise.jiomart</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>jiomart</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>AppsFlyerAppleId</key>
	<string>$(APPSFLYER_APPLE_ID)</string>
	<key>AppsFlyerDevId</key>
	<string>$(APPSFLYER_DEV_ID)</string>
	<key>AppsFlyerDomainUrl</key>
	<string>$(APPSFLYER_DOMAIN)</string>
	<key>CleverTapAccountID</key>
	<string>$(CLEVERTAP_ACCOUNT_ID)</string>
	<key>CleverTapRegion</key>
	<string>in1</string>
	<key>CleverTapToken</key>
	<string>$(CLEVERTAP_ACCOUNT_TOKEN)</string>
	<key>Environment</key>
	<string>$(CONFIG_ENVIRONMENT)</string>
	<key>AppIdentifier</key>
	<string>$(APP_IDENTIFIER)</string>
	<key>GOOGLE_MAPS_API_KEY</key>
	<string>$(GOOGLE_MAPS_API_KEY)</string>
	<key>LSApplicationCategoryType</key>
	<dict/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppleMusicUsageDescription</key>
	<string>$(APP_NAME) media library use</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>$(APP_NAME) bluetooth peripheral use</string>
	<key>NSCameraUsageDescription</key>
	<string>We use your camera to scan QR codes</string>
	<key>NSContactsUsageDescription</key>
	<string>Sync your contacts to send SMS. No charges apply.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>We need your location to find the right products deliverable to you</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>We need your location to find the right products deliverable to you</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>We require access of your microphone to enable voice based search for products within JioMart</string>
	<key>NSMotionUsageDescription</key>
	<string>$(APP_NAME) motion sensor use</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>We need permission to access the scanned QR code from your library</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>We need permission to access the scanned QR code from your library</string>
	<key>NSSiriUsageDescription</key>
	<string>$(APP_NAME) siri use</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>Speech recognition will be used to determine which words you speak into this device&apos;s microphone.</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>Allow tracking to help us provide you a better personalized ad experience tailored to your interests across app and website.</string>
	<key>UIAppFonts</key>
	<array>
		<string>JioType-Black.ttf</string>
		<string>JioType-Bold.ttf</string>
		<string>JioType-Medium.ttf</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>FacebookAdvertiserIDCollectionEnabled</key>
	<true/>
	<key>FacebookAppID</key>
	<string>318862225974457</string>
	<key>FacebookAutoInitEnabled</key>
	<string>YES</string>
	<key>FacebookAutoLogAppEventsEnabled</key>
	<string>YES</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookDisplayName</key>
	<string>JioMart</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>phonepe</string>
		<string>gpay</string>
		<string>paytmmp</string>
        <string>credpay</string>
        <string>bhim</string>
        <string>jfspay</string>
		<string>fbapi</string>
		<string>fb-messenger-share-api</string>
		<string>fbauth2</string>
		<string>fbshareextension</string>
	</array>
</dict>
</plist>
