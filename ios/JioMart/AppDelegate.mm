#import <React/RCTRootView.h>
#import <React/RCTBundleURLProvider.h>
#import <React/RCTBridge.h>
#import "AppDelegate.h"
#import <GoogleMaps/GoogleMaps.h>
#import "JMEventEmitter.h"
#import "NSDictionary+Extension.h"
#import <Clevertap-react-native/CleverTapReactManager.h>
#import "RNShortcuts.h"
#import "JioMart-Swift.h"
#import <AppsFlyerLib/AppsFlyerLib.h>
#import <RNAppsFlyer.h>
#import <Firebase.h>
#import <FBSDKCoreKit/FBSDKCoreKit.h>
#import <CoreData/CoreData.h>

@implementation AppDelegate

typedef void (^RCTPromiseResolveBlock)(id result);
typedef void (^RCTPromiseRejectBlock)(NSString *code, NSString *message, NSError *error);

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
  NSString *appId = [JMInfoPlist appsFlyerAppId];
  NSString *devKey = [JMInfoPlist appsflyerDevId];
  [[AppsFlyerLib shared] setAppsFlyerDevKey:devKey];
  [[AppsFlyerLib shared] setAppleAppID:appId];
  [[AppsFlyerLib shared] start];
  
  if ([FIRApp defaultApp] == nil) {
    [FIRApp configure];
  }
  
  // 🔹 Initialize CleverTap
  [self cleverTapInitialisation:launchOptions];
  
  // 🔹 Setup Google Maps
  [self googleMapTapInitialisation];
  
  // 🔹 Set up window and root view controller (Swift)
  self.window = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
  JMRNViewController *vc = [JMRNViewController new];
  self.window.rootViewController = vc;
  [self.window makeKeyAndVisible];
  
  // 🔹 Initialize Facebook SDK
  [[FBSDKApplicationDelegate sharedInstance] application:application
                           didFinishLaunchingWithOptions:launchOptions];
  if ([FBSDKSettings appID] != nil && [[FBSDKSettings appID] length] > 0) {
    NSLog(@"Facebook SDK is initialized with App ID: %@", [FBSDKSettings appID]);
  } else {
    NSLog(@"Facebook SDK is NOT initialized");
  }
  
  return YES;
}

- (BOOL)application:(UIApplication *)application
            openURL:(NSURL *)url
            options:(NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options
{
  BOOL handledFB = [[FBSDKApplicationDelegate sharedInstance] application:application
                                                                  openURL:url
                                                                  options:options];
  
  BOOL handledDeepLink = [[DeepLinkManager shared] handleURL:url options:options];
  
  return handledFB || handledDeepLink;
}

// Core Data stack
@synthesize persistentContainer = _persistentContainer;

- (NSPersistentContainer *)persistentContainer {
  if (_persistentContainer != nil) {
    return _persistentContainer;
  }
  
  _persistentContainer = [[NSPersistentContainer alloc] initWithName:@"JioMart"]; // Use your .xcdatamodeld name
  NSPersistentStoreDescription *storeDescription = _persistentContainer.persistentStoreDescriptions.firstObject;
  storeDescription.shouldMigrateStoreAutomatically = YES;
  storeDescription.shouldInferMappingModelAutomatically = YES;
  
  [_persistentContainer loadPersistentStoresWithCompletionHandler:^(NSPersistentStoreDescription *description, NSError *error) {
    if (error != nil) {
      NSLog(@"❌ Core Data error %@, %@", error, error.userInfo);
      abort();
    }
  }];
  
  return _persistentContainer;
}

// Save context
- (void)saveContext {
  NSManagedObjectContext *context = self.persistentContainer.viewContext;
  if ([context hasChanges]) {
    NSError *error = nil;
    if (![context save:&error]) {
      NSLog(@"❌ Failed to save context: %@, %@", error, error.userInfo);
      abort();
    }
  }
}

- (BOOL)application:(UIApplication *)application continueUserActivity:(NSUserActivity *)userActivity
 restorationHandler:(void (^)(NSArray<id<UIUserActivityRestoring>> * _Nullable))restorationHandler {
  return [[DeepLinkManager shared] handleUserActivity:userActivity restorationHandler:restorationHandler];
}

- (void)application:(UIApplication *)application performActionForShortcutItem:(UIApplicationShortcutItem *)shortcutItem completionHandler:(void (^)(BOOL))completionHandler {
  [RNShortcuts handleShortcutItem:shortcutItem];
  completionHandler(YES);
}

- (void)cleverTapInitialisation:(NSDictionary *)launchOptions {
  [CleverTap autoIntegrate]; // integrate CleverTap SDK using the autoIntegrate option
  [[CleverTapReactManager sharedInstance] applicationDidLaunchWithOptions:launchOptions];
}

- (void)googleMapTapInitialisation {
  NSString *apiKey = [[NSBundle mainBundle] objectForInfoDictionaryKey:@"GOOGLE_MAPS_API_KEY"];
  [GMSServices provideAPIKey:apiKey];
}

@end
