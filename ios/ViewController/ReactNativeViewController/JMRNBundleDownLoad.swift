//
//  JMRNBundleDownLoad.swift
//  JioMart
//
//  Created by <PERSON><PERSON><PERSON> on 01/07/25.
//

import UIKit
import JioRNBundle

enum ENVType: String {
  case staging = "SIT"
  case production = "PROD"
}

class JMRNBundleDownLoad: NSObject {
  public static let shared = JMRNBundleDownLoad()
  let REACTNATIVE_DEBUG_URL = JMConstant.REACTNATIVE_DEBUG_URL
}

extension JMRNBundleDownLoad {
  func getLoaderViewDictionary() -> [String: Any] {
    return [
      JMConstant.DictionaryKeys.loaderView: [
        [
          JMConstant.DictionaryKeys.title: "Update in progress",
          JMConstant.DictionaryKeys.subTitle: "We're upgrading your experience...",
          JMConstant.DictionaryKeys.text: "Please do not close the app."
        ]
      ]
    ]
  }
  
  func initializeJioRNBundleSDK() {
    let loaderConfig = getLoaderViewDictionary()
    let jwtToken = ""
    let rnConfig: [String: Any] = [
      JMConstant.DictionaryKeys.envinormentType: getEnvinormentType(),
      JMConstant.DictionaryKeys.jwtToken: jwtToken,
      JMConstant.DictionaryKeys.applicationTextData: loaderConfig,
      JMConstant.DictionaryKeys.fcmId: "",
      JMConstant.DictionaryKeys.logLevel: "debug"
    ]
    #if DEBUG_MODE
      JioRNBundleDownLoad.shared.isReactNaiveDebugEnabled = true
    #else
      JioRNBundleDownLoad.shared.isReactNaiveDebugEnabled = false
    #endif
    JioRNBundleDownLoad.shared.REACTNATIVE_DEBUG_URL = REACTNATIVE_DEBUG_URL
    JioRNBundleDownLoad.shared.initializeRNSDK(rnInitialConfig: rnConfig, delegate: self)
  }
  
  func getEnvinormentType() -> String {
    var prefix = ""
    switch JMInfoPlist.appEnvironment.lowercased() {
    case ENVType.staging.rawValue.lowercased():
      prefix = JioEnvinormentType.sit.rawValue
      
    case ENVType.production.rawValue.lowercased():
      prefix = JioEnvinormentType.prod.rawValue
      
    default:
      prefix = JioEnvinormentType.prod.rawValue
    }
    return prefix
  }
}

extension JMRNBundleDownLoad: JioRNDownloadDeleagte {
  func showAlert(message: String) {
    guard JMInfoPlist.appEnvironment.lowercased() == ENVType.staging.rawValue else { return }
    DispatchQueue.main.async {
      JMCommonMethods.showAlert(title: "", message: message)
    }
  }
  
  func refreshJWTToken() { }
  
  func startPerformanceTracker() {
    print("Start tracker")
  }
  
  func stopPerformanceTracker() {
    print("Stop tracker")
  }
  
  func fireClevertapEvent(eventName: String, data: [String: Any]) {
    print("Event Name: - ", eventName)
  }
}
