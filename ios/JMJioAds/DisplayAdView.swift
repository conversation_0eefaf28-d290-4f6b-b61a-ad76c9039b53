//
//  DisplayAdView.swift
//  ReactTestApp
//
//  Created by <PERSON><PERSON> on 09/05/22.
//

import UIKit
import JioAdsFramework
import React

enum AdType: Int {
    case dynamicDisplay = 1
    case instreamVideo = 2
    case interstitial = 3
    case nativeContentStream = 4
    case customNative = 5
}

enum AdEventName: String {
    case adRecived = "adRecived"
    case adPrepared = "adPrepared"
    case adRender = "adRender"
    case adClicked = "adClicked"
    case adRefresh = "adRefresh"
    case adFailed = "adFailed"
    case adMediaEnd = "adMediaEnd"
    case adClosed = "adClosed"
    case adMediaStart = "adMediaStart"
    case adSkippable = "adSkippable"
    case adMediaExpand = "adMediaExpand"
    case adMediaCollapse="adMediaCollapse"
    case adMediaPlaybackChange = "adMediaPlaybackChange"
    case adDataPrepared = "adDataPrepared"
    case adChange = "adChange"
    case mediationRequesting = "mediationRequesting"
    case mediationAd = "mediationAd"
}

class DisplayAdView: UIView, JIOAdViewProtocol {
    
    @objc var displayadView: JioAdView?
    @objc var instreamadView: JioAdView?
    @objc var interstitaladView: JioAdView?
    @objc var nativeContenadView: JioAdView?
    @objc var customNativeadView: JioAdView?
  
    var firstTimeLoad = true
    var adMetaData: [String: Any] = [:];
    var container = UIView();
    var adCustWidth: Int = 0;
    var adCustHeight: Int = 0;
    @objc var onChange: ( ([String: Any]) -> Void)?

    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func didMoveToSuperview() {
        super.didMoveToSuperview()
        
        guard !firstTimeLoad else {
            firstTimeLoad = false
            return
        }
        self.invalidateAds()
    }
    
    override func didMoveToWindow() {
        super.didMoveToWindow()
            
        customNativeadView?.pauseAd()
        nativeContenadView?.pauseAd()
    }
  
    @objc func setData(_ val: NSDictionary) {
        self.invalidateAds()
        if let adspotKey = val["adspotKey"] as? String,
           let adHeight = val["adHeight"] as? Int,
           let adWidth = val["adWidth"] as? Int,
           let adTypeVal = val["adType"] as? Int
        {
          var adMetaVal = val["adMetaData"] as? [String: Any] ?? [:]
          self.adCustWidth = val["adCustomWidth"] as? Int ?? adWidth
          self.adCustHeight = val["adCustomHeight"] as? Int ?? adHeight
            
//            let pincode = JMUserSessionViewModel().getSelectedPincode() ?? ""
//            let city = JMAddressDataModel().getSavedCityName() ?? ""
//            let state = JMAddressDataModel().getSavedStateName() ?? ""
//            let country = DefaultAddress.country
            
//            adMetaVal["pc"] = pincode
//            adMetaVal["st"] = state
//            adMetaVal["ci"] = city
//            adMetaVal["co"] = country
//            if var metaDictionary = adMetaVal["jm_meta"] as? [String: String] {
//                if metaDictionary["pincode"] == nil {
//                    metaDictionary["pincode"] = pincode
//                    adMetaVal["jm_meta"] = metaDictionary
//                }
//            } else {
//                adMetaVal["jm_meta"] = ["pincode": pincode]
//            }
//            adMetaVal["pincode"] = pincode
//            self.adMetaData = adMetaVal
//            print(self.adMetaData)
            JioAdSdk.setPackageName(packageName: JMInfoPlist.appIdentifier)
            setupAdView(adType: adTypeVal, adspotKey: adspotKey, adHeight: adHeight, adWidth: adWidth)
        }
    }
    
    private func invalidateAds() {
        self.displayadView?.invalidateAd()
        self.instreamadView?.invalidateAd()
        self.interstitaladView?.invalidateAd()
        self.nativeContenadView?.invalidateAd()
        self.customNativeadView?.invalidateAd()
    }
    
    private func setupAdView(adType: Int, adspotKey: String, adHeight: Int, adWidth: Int) {
        
        switch adType {
        case 1:
            self.container = setupDisplayAdView(adspotKey: adspotKey, adHeight: adHeight, adWidth: adWidth)
        case 2:
            self.container = setupInstreamAdView(adspotKey: adspotKey, adHeight: adHeight, adWidth: adWidth)
        case 3:
            self.container = setupInterstitialAdView(adspotKey: adspotKey, adHeight: adHeight, adWidth: adWidth)
        case 4:
            self.container = setupNativeContentStreamAdView(adspotKey: adspotKey, adHeight: adHeight, adWidth: adWidth)
        case 5:
            self.container = setupCustomNativeAdView(adspotKey: adspotKey, adHeight: adHeight, adWidth: adWidth)
        case 9:
            DispatchQueue.main.async {
                self.displayadView?.invalidateAd()
                self.instreamadView?.invalidateAd()
                self.interstitaladView?.invalidateAd()
                self.nativeContenadView?.invalidateAd()
                self.customNativeadView?.invalidateAd()
                self.container = UIView(frame: CGRect(x: 0, y: 0, width: adWidth, height: adHeight))
            }
        default:
            self.container = setupInstreamAdView(adspotKey: adspotKey, adHeight: adHeight, adWidth: adWidth) // hlmq9pn2
        }
    }
    
    private func setupDisplayAdView(adspotKey: String, adHeight: Int, adWidth: Int) -> UIView {
        let container = UIView(frame: CGRect(x: 0, y: 0, width: adWidth, height: adHeight))
        JioAdSdk.setLogLevel(logLevel: .debug)
        //JioAdSdk.setPackageName(packageName: "com.jio.callkittest")
        setValueInUserDefaults(objValue: "Production", for: "currentEnvironment")
        displayadView = JioAdView(adSpotId: adspotKey, adType: .dynamicDisplay, delegate: self, forPresentionClass: UIViewController(), publisherContainer: container)
        displayadView?.setDisplayAdSize(displaySizes: [.size300x250])
        displayadView?.translatesAutoresizingMaskIntoConstraints = false
        displayadView?.setCustomView(container: container)
        displayadView?.cacheAd()
        
        return container
    }
    
    private func setupInstreamAdView(adspotKey: String, adHeight: Int, adWidth: Int) -> UIView {
        let container = UIView(frame: CGRect(x: 0, y: 0, width: adWidth, height: adHeight))
        JioAdSdk.setLogLevel(logLevel: .debug)
        setValueInUserDefaults(objValue: "Production", for: "currentEnvironment")
        instreamadView = JioAdView(adSpotId: adspotKey, adType: .instreamVideo, delegate: self, forPresentionClass: UIViewController(), publisherContainer: container)
        instreamadView?.translatesAutoresizingMaskIntoConstraints = false
        instreamadView?.setCustomView(container: container)
        instreamadView?.cacheAd()
        
        return container
    }
    
    private func setupInterstitialAdView(adspotKey: String, adHeight: Int, adWidth: Int) -> UIView {
        let container = UIView(frame: CGRect(x: 0, y: 0, width: adWidth, height: adHeight))
        JioAdSdk.setLogLevel(logLevel: .debug)
        setValueInUserDefaults(objValue: "Production", for: "currentEnvironment")
        interstitaladView = JioAdView(adSpotId: adspotKey, adType: .interstitial, delegate: self, forPresentionClass: UIViewController(), publisherContainer: container)
        interstitaladView?.translatesAutoresizingMaskIntoConstraints = false
        interstitaladView?.setCustomView(container: container)
        interstitaladView?.cacheAd()
        
        return container
    }
    
    private func setupCustomNativeAdView(adspotKey: String, adHeight: Int, adWidth: Int) -> UIView {
        let container = CustomNativeContentStremView(frame: CGRect(x: 0, y: 0, width: self.adCustWidth, height: self.adCustHeight))
        JioAdSdk.setLogLevel(logLevel: .debug)
        JioAdSdk.setPackageName(packageName: JMInfoPlist.appIdentifier)
//        setValueInUserDefaults(objValue: "Production", for: "currentEnvironment")
        customNativeadView = JioAdView(adSpotId: adspotKey, adType: .customNative, delegate: self, forPresentionClass: UIViewController(), publisherContainer: container)
        customNativeadView?.translatesAutoresizingMaskIntoConstraints = false
        customNativeadView?.setCustomView(container: container)
        customNativeadView?.setMetaData(metaDataDict: self.adMetaData)
        customNativeadView?.setCustomImageSize(width: adWidth, height: adHeight)
        customNativeadView?.cacheAd()
          
        return container
    }
    
    private func setupNativeContentStreamAdView(adspotKey: String, adHeight: Int, adWidth: Int) -> UIView {
        let container = CustomNativeContentStremView(frame: CGRect(x: 0, y: 0, width: adWidth, height: adHeight))
        JioAdSdk.setLogLevel(logLevel: .debug)
//        setValueInUserDefaults(objValue: "Production", for: "currentEnvironment")
        nativeContenadView = JioAdView(adSpotId: adspotKey, adType: .nativeContentStream, delegate: self, forPresentionClass: UIViewController(), publisherContainer: container)
        nativeContenadView?.translatesAutoresizingMaskIntoConstraints = false
        nativeContenadView?.setCustomView(container: container)
        nativeContenadView?.cacheAd()
        
        return container
    }
    
    func setValueInUserDefaults(objValue: String, for key: String) {
        let userDefaults = UserDefaults.standard
        userDefaults.set(objValue, forKey: key)
        userDefaults.synchronize()
    }
  
    private func sendEvent(_ eventName: String) {
      guard let onChange = onChange else {
          print("onChange callback is nil")
          return
      }
      onChange(["event": eventName])
    }
    
    // Implement JIOAdViewProtocol methods
    func onAdReceived(adView: JioAdView) {
        print("onAdReceived",adView)
        sendEvent(AdEventName.adRecived.rawValue)
    }
    
    func onAdPrepared(adView: JioAdView) {
        print( "onAdPrepared", adView)
        if adView == instreamadView {
            self.instreamadView?.loadAd()
        } else if adView == customNativeadView {
            self.customNativeadView?.loadAd()
        } else if adView == displayadView {
            self.displayadView?.loadAd()
        } else if adView == interstitaladView {
            self.interstitaladView?.loadAd()
        } else if adView == nativeContenadView {
            self.nativeContenadView?.loadAd()
        }
        addSubview(container)
        sendEvent(AdEventName.adPrepared.rawValue)
    }
    
    func onAdRender(adView: JioAdView) {
        print("onAdRender", adView)
        sendEvent(AdEventName.adRender.rawValue)
    }
    
    func onAdClicked(adView: JioAdView) {
        print("onAdClicked", adView)
        sendEvent(AdEventName.adClicked.rawValue)
    }
    
    func onAdRefresh(adView: JioAdView) {       
        print("onAdRefresh", adView)
        sendEvent(AdEventName.adRefresh.rawValue)
    }
    
    func onAdFailedToLoad(adView: JioAdView, error: JioAdError) {
//        NotificationCenter.default.post(name: .removeAdView, object: nil)
        print("onAdFailedToLoad", adView, error)
        sendEvent(AdEventName.adFailed.rawValue)
    }
    
    func onAdMediaEnd(adView: JioAdView) {
        print("onAdMediaEnd", adView)
        sendEvent(AdEventName.adMediaEnd.rawValue)
    }
    
    func onAdClosed(adView: JioAdView, isVideoCompleted: Bool, isEligibleForReward: Bool) {
        print("onAdClosed", adView)
        sendEvent(AdEventName.adClosed.rawValue)
    }
    
    func onAdMediaStart(adView: JioAdView) {
        print("onAdMediaStart", adView)
        sendEvent(AdEventName.adMediaStart.rawValue)
    }
    
    func onAdSkippable(adView: JioAdView) {
        print("onAdSkippable", adView)
        sendEvent(AdEventName.adSkippable.rawValue)
    }
    
    func onAdMediaExpand(adView: JioAdView) {
        print("onAdMediaExpand",adView)
        sendEvent(AdEventName.adMediaExpand.rawValue)
    }
    
    func onAdMediaCollapse(adView: JioAdView) {
        print("onAdMediaCollapse")
        sendEvent(AdEventName.adMediaCollapse.rawValue)
    }
    
    func onMediaPlaybackChange(adView: JioAdView, mediaPlayBack: MediaPlayBack) {
        print("onMediaPlaybackChange")
        sendEvent(AdEventName.adMediaPlaybackChange.rawValue)
    }
    
    func onAdDataPrepared(videoAd: VideoAd?, isLastAd: Bool) {
        print("onAdDataPrepared")
        sendEvent(AdEventName.adDataPrepared.rawValue)
    }
    
    func onAdDataPrepared(nativeAd: NativeAd?, isLastAd: Bool) {
        print("onAdDataPrepared")
        sendEvent(AdEventName.adDataPrepared.rawValue)
    }
    
    func onAdChange(adView: JioAdView, trackNo: Int) {
        print("onAdChange")
        sendEvent(AdEventName.adChange.rawValue)
    }
    
    func mediationRequesting() {
        print("mediationRequesting")
        sendEvent(AdEventName.mediationRequesting.rawValue)
    }
    
    func mediationLoadAd() {
        print("mediationLoadAd")
        sendEvent(AdEventName.mediationAd.rawValue)
    }
    
    
}


