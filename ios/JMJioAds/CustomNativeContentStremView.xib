<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="CustomNativeContentStremView" customModule="JioMart" customModuleProvider="target">
            <connections>
                <outlet property="buttonCTA" destination="eSX-1N-De5" id="LHJ-WK-rEf"/>
                <outlet property="contentView" destination="iN0-l3-epB" id="BZy-q7-AOr"/>
                <outlet property="customImageview" destination="ChT-lV-9iN" id="idU-rk-Mo2"/>
                <outlet property="imageViewIcon" destination="lKR-PA-BES" id="cPr-tw-GDq"/>
                <outlet property="labelDesc" destination="Kfo-p7-wbn" id="IfI-4Q-flv"/>
                <outlet property="labelTitle" destination="EeU-b0-ELf" id="1FQ-K5-x4e"/>
                <outlet property="mediaView" destination="RbP-GB-Duv" id="ima-ZM-YPy"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB">
            <rect key="frame" x="0.0" y="0.0" width="300" height="146"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="RbP-GB-Duv">
                    <rect key="frame" x="0.0" y="0.0" width="300" height="146"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="ChT-lV-9iN">
                            <rect key="frame" x="0.0" y="0.0" width="300" height="146"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        </imageView>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="ChT-lV-9iN" secondAttribute="trailing" id="FVZ-uM-0p2"/>
                        <constraint firstAttribute="bottom" secondItem="ChT-lV-9iN" secondAttribute="bottom" id="S9F-9h-Qkd"/>
                        <constraint firstItem="ChT-lV-9iN" firstAttribute="top" secondItem="RbP-GB-Duv" secondAttribute="top" id="dyT-Mo-Yo7"/>
                        <constraint firstItem="ChT-lV-9iN" firstAttribute="leading" secondItem="RbP-GB-Duv" secondAttribute="leading" id="ywu-RF-V6n"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="0Un-IE-TKb">
                    <rect key="frame" x="0.0" y="146.33333333333334" width="300" height="105.66666666666666"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="EeU-b0-ELf">
                            <rect key="frame" x="8" y="14" width="284" height="20"/>
                            <fontDescription key="fontDescription" type="boldSystem" pointSize="12"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <stackView opaque="NO" contentMode="scaleToFill" spacing="2" translatesAutoresizingMaskIntoConstraints="NO" id="6Fk-m7-19c">
                            <rect key="frame" x="0.0" y="55.666666666666657" width="300" height="50"/>
                            <subviews>
                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="lKR-PA-BES">
                                    <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="50" id="8h0-Og-XaM"/>
                                        <constraint firstAttribute="height" constant="50" id="LMs-Oh-WaZ"/>
                                    </constraints>
                                </imageView>
                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" translatesAutoresizingMaskIntoConstraints="NO" id="OVg-hu-OFw">
                                    <rect key="frame" x="52" y="0.0" width="150" height="50"/>
                                    <subviews>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Kfo-p7-wbn">
                                            <rect key="frame" x="0.0" y="0.0" width="150" height="25"/>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="11"/>
                                            <color key="textColor" red="0.4549019608" green="0.4549019608" blue="0.4549019608" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XkV-IT-21M">
                                            <rect key="frame" x="0.0" y="25" width="150" height="25"/>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="11"/>
                                            <color key="textColor" red="0.4549019608" green="0.4549019608" blue="0.4549019608" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                    </subviews>
                                    <constraints>
                                        <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="150" id="8Cx-SY-wGY"/>
                                    </constraints>
                                </stackView>
                                <button opaque="NO" clipsSubviews="YES" contentMode="scaleAspectFit" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="tailTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="eSX-1N-De5">
                                    <rect key="frame" x="204" y="0.0" width="96" height="50"/>
                                    <constraints>
                                        <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="96" id="g3N-Um-ojA"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" type="boldSystem" pointSize="12"/>
                                    <state key="normal" title="CTA">
                                        <color key="titleColor" red="0.14027024269999999" green="0.3273985667" blue="0.67732293830000001" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                    </state>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                            <integer key="value" value="20"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                </button>
                            </subviews>
                            <constraints>
                                <constraint firstAttribute="bottom" secondItem="OVg-hu-OFw" secondAttribute="bottom" id="7Eo-ED-3TL"/>
                                <constraint firstItem="OVg-hu-OFw" firstAttribute="leading" secondItem="lKR-PA-BES" secondAttribute="trailing" constant="2" id="ew9-vU-D0G"/>
                                <constraint firstItem="lKR-PA-BES" firstAttribute="leading" secondItem="6Fk-m7-19c" secondAttribute="leading" id="x2F-s5-N9Y"/>
                            </constraints>
                        </stackView>
                    </subviews>
                    <constraints>
                        <constraint firstAttribute="bottom" secondItem="6Fk-m7-19c" secondAttribute="bottom" id="3oa-CS-noi"/>
                        <constraint firstItem="EeU-b0-ELf" firstAttribute="leading" secondItem="0Un-IE-TKb" secondAttribute="leading" constant="8" id="Gna-R7-kuH"/>
                        <constraint firstItem="6Fk-m7-19c" firstAttribute="top" secondItem="EeU-b0-ELf" secondAttribute="bottom" constant="21.670000000000002" id="M2f-Ya-F7j"/>
                        <constraint firstAttribute="trailing" secondItem="6Fk-m7-19c" secondAttribute="trailing" id="WDF-7U-CvK"/>
                        <constraint firstItem="EeU-b0-ELf" firstAttribute="top" secondItem="0Un-IE-TKb" secondAttribute="top" constant="14" id="lHZ-rk-Rsy"/>
                        <constraint firstItem="6Fk-m7-19c" firstAttribute="leading" secondItem="0Un-IE-TKb" secondAttribute="leading" id="u0N-p1-c2N"/>
                        <constraint firstAttribute="trailing" secondItem="EeU-b0-ELf" secondAttribute="trailing" constant="8" id="yii-zq-UCx"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="vUN-kp-3ea"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstItem="0Un-IE-TKb" firstAttribute="leading" secondItem="vUN-kp-3ea" secondAttribute="leading" id="19K-a1-yHw"/>
                <constraint firstItem="0Un-IE-TKb" firstAttribute="trailing" secondItem="vUN-kp-3ea" secondAttribute="trailing" id="9Bj-va-4DY"/>
                <constraint firstAttribute="bottom" secondItem="RbP-GB-Duv" secondAttribute="bottom" id="Pd5-LI-fAj"/>
                <constraint firstItem="RbP-GB-Duv" firstAttribute="leading" secondItem="vUN-kp-3ea" secondAttribute="leading" id="Ptr-0V-asn"/>
                <constraint firstItem="RbP-GB-Duv" firstAttribute="trailing" secondItem="vUN-kp-3ea" secondAttribute="trailing" id="bKc-mB-qCl"/>
                <constraint firstAttribute="bottom" secondItem="0Un-IE-TKb" secondAttribute="bottom" constant="-2" id="bQc-1v-EAr"/>
                <constraint firstItem="RbP-GB-Duv" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="zd2-Tq-zkd"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <variation key="default">
                <mask key="subviews">
                    <exclude reference="0Un-IE-TKb"/>
                </mask>
            </variation>
            <point key="canvasLocation" x="-27.480916030534349" y="-66.901408450704224"/>
        </view>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
