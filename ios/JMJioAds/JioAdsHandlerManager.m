
#import <React/RCTViewManager.h>
#import <React/RCTBridgeModule.h>
#import <React/RCTEventEmitter.h>
#import <React/RCTComponent.h>
#import <React/RCTUIManager.h>
#import <React/RCTLog.h>


#import "JioMart-Swift.h"


@interface JioAdsHandlerManager : RCTViewManager
@end

@implementation JioAdsHandlerManager

RCT_EXPORT_MODULE(JioADsNativeComponent)

- (UIView *)view
{
  return [[DisplayAdView alloc] init];
}

RCT_EXPORT_VIEW_PROPERTY(data, NSDictionary);
RCT_EXPORT_VIEW_PROPERTY(onChange, RCTBubblingEventBlock);

+ (BOOL)requiresMainQueueSetup
{
    return YES;
}
@end
