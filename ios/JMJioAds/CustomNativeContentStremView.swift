//
//  CustomNativeContentStremView.swift
//  sampleApp
//
//  Created by <PERSON><PERSON> on 18/10/23.
//

import UIKit
import JioAdsFramework

class CustomNativeContentStremView: UIView {
    @IBOutlet weak var contentView: UIView?
    @IBOutlet weak var imageViewIcon: UIImageView?
    @IBOutlet weak var mediaView: UIView?
    @IBOutlet weak var labelTitle: UILabel?
    @IBOutlet weak var labelDesc: UILabel?
    @IBOutlet weak var buttonCTA: UIButton?
    @IBOutlet weak var customImageview: UIImageView?
  //  @IBOutlet weak var startRatingLbl: FloatRatingView?

    override init(frame: CGRect) {
        super.init(frame: frame)
        
        setUpNib()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setUpNib()
    }
    
    func setUpNib() {
        let bundle = Bundle(for: type(of: self)).path(forResource: "CustomNativeContentStremView", ofType: "nib")
        guard bundle != nil else {
            SDKLog("Bundle is nil")
            return
        }
        Bundle(for: type(of: self)).loadNibNamed("CustomNativeContentStremView", owner: self, options: nil)
        guard let contentView = contentView else {
            SDKLog("contentView is nil")
            return
        }
        addSubview(contentView)
        contentView.frame = self.frame
//        imageViewIcon?.tag = NativeAdTags.iconImageView.rawValue
        mediaView?.tag = NativeAdTags.mediaView.rawValue
//        labelTitle?.tag = NativeAdTags.title.rawValue
//        labelDesc?.tag = NativeAdTags.desc.rawValue
//        buttonCTA?.tag = NativeAdTags.ctaButton.rawValue
        customImageview?.tag = NativeAdTags.customImageView.rawValue
    }
}
