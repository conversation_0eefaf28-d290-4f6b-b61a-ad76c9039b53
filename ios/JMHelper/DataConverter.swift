//
//  DataTypeConversion.swift
//  JioMart
//
//  Created by Manish4 Sah on 25/04/25.
//

import Foundation

class DataConverter {
  
  static func codableToDictionary<T: Codable>(_ object: T) -> [String: Any]? {
    guard let data = codableToData(object) else { return nil }
    return dataToDictionary(data)
  }
  
  static func codableToJSONString<T: Codable>(_ object: T) -> String? {
    guard let data = codableToData(object) else { return nil }
    return dataToJSONString(data)
  }
  
  static func codableToData<T: Codable>(_ object: T) -> Data? {
    let encoder = JSONEncoder()
    encoder.keyEncodingStrategy = .convertToSnakeCase
    do {
      return try encoder.encode(object)
    } catch {
      print("Encoding failed: \(error)")
      return nil
    }
  }
  
  static func jsonStringToCodable<T: Codable>(_ type: T.Type, from jsonString: String) -> T? {
    guard let data = jsonString.data(using: .utf8) else { return nil }
    return dataToCodable(type, from: data)
  }
  
  static func dataToCodable<T: Codable>(_ type: T.Type, from data: Data) -> T? {
    let decoder = JSONDecoder()
    decoder.keyDecodingStrategy = .convertFromSnakeCase
    do {
      return try decoder.decode(T.self, from: data)
    } catch {
      print("Decoding failed: \(error)")
      return nil
    }
  }
  
  static func dataToDictionary(_ data: Data) -> [String: Any]? {
    do {
      return try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any]
    } catch {
      print("Data to Dictionary conversion failed: \(error)")
      return nil
    }
  }
  
  static func dictionaryToCodable<T: Codable>(_ type: T.Type, from dict: [String: Any]) -> T? {
    do {
      let data = try JSONSerialization.data(withJSONObject: dict, options: [])
      return dataToCodable(type, from: data)
    } catch {
      print("Dictionary to Codable conversion failed: \(error)")
      return nil
    }
  }
  
  static func dataToJSONString(_ data: Data) -> String? {
    return String(data: data, encoding: .utf8)
  }
  
  static func jsonString(dictionary: [String: Any]) -> String? {
    guard let data = try? JSONSerialization.data(withJSONObject: dictionary, options: []) else { return nil }
    return String(data: data, encoding: .utf8)
  }
}
