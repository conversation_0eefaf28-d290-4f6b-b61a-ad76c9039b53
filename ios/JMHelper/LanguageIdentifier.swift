//
//  Language.swift
//  JioMart
//
//  Created by Manish<PERSON> Sah on 25/04/25.
//


import Foundation

enum Language {
    case english
    case hindi
    case unknown
}

struct LanguageDetector {
    private static let languageCharacterSets: [Language: ClosedRange<UnicodeScalar>] = [
        .english: "\u{0000}"..."\u{007F}",
        .hindi: "\u{0900}"..."\u{097F}"
    ]

    static func identifyLanguage(of string: String) -> Language {
        var languageCounts: [Language: Int] = [:]

        for scalar in string.unicodeScalars {
            for (language, range) in languageCharacterSets {
                if range.contains(scalar) {
                    languageCounts[language, default: 0] += 1
                    break
                }
            }
        }

        if let (topLanguage, count) = languageCounts.max(by: { $0.value < $1.value }),
           count > 0 {
            return topLanguage
        }

        return .unknown
    }
}

