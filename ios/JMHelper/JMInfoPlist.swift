//
//  JMInfoPlist.swift
//  JioMart
//
//  Created by <PERSON><PERSON><PERSON> on 26/04/25.
//

import Foundation

enum InfoPlist {
  static let googleMapKey = "GOOGLE_MAPS_API_KEY"
  static let environment = "Environment"
  static let appIdentifierKey = "AppIdentifier"
  static let appsflyerAppId = "AppsFlyerAppleId"
  static let appsflyerDevId = "AppsFlyerDevId"
  static let appsflyerDomain = "AppsFlyerDomainUrl"
}

@objc public class JMInfoPlist: NSObject {
  
    public static var infoPlistDictionary: [String: Any] = {
        guard let infoPlistDictionary = Bundle.main.infoDictionary else {
            fatalError("Plist file not found")
        }
        return infoPlistDictionary
    }()
    
    // MARK: - Plist values
    static let googleMapKey: String = {
      guard let value = JMInfoPlist.infoPlistDictionary[InfoPlist.googleMapKey] as? String else {
            return "AIzaSyCdP5Kx1XxidSUsy4vTArUFrfqGKsmPbh0"
        }
        return value
    }()

  static let appEnvironment: String = {
      guard let environment = JMInfoPlist.infoPlistDictionary[InfoPlist.environment] as? String else {
          return "PROD"
      }
      return environment
  }()
  
  static let appIdentifier: String = {
    guard let identity = JMInfoPlist.infoPlistDictionary[InfoPlist.appIdentifierKey] as? String else {
      return "com.jio.jiomart"
    }
    return identity;
  }()
  
  @objc public static func appsFlyerAppId() -> String {
    guard let identity = JMInfoPlist.infoPlistDictionary[InfoPlist.appsflyerAppId] as? String else {
      return "1522085683"
    }
    return identity;
  }
  
  @objc public static func appsflyerDevId() -> String {
    guard let identity = JMInfoPlist.infoPlistDictionary[InfoPlist.appsflyerDevId] as? String else {
      return "jGgetr8Ui6Vyyzdjmb5QkM"
    }
    return identity;
  }
  
  static let appsflyerDomain: String = {
    guard let identity = JMInfoPlist.infoPlistDictionary[InfoPlist.appsflyerDomain] as? String else {
      return "jiomart.onelink.me"
    }
    return identity;
  }()
}
