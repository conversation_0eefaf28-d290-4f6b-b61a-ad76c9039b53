//
//  JMCommonMethods.swift
//  JioMart
//
//  Created by <PERSON><PERSON><PERSON> on 02/07/25.
//

import Foundation

class JMCommonMethods {
  static func showAlert(title: String, message: String) {
    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
       let window = windowScene.windows.first,
       let rootVC = window.rootViewController {
      self.showTopMostAlert(topController: rootVC, title: title, message: message)
    }
  }
  
  static private func showTopMostAlert(topController: UIViewController, title: String, message: String) {
      let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
      alert.addAction(UIAlertAction(title: "OK", style: .default, handler: nil))

      topController.present(alert, animated: true, completion: nil)
  }
}
