//
//  DeeplinkManager.swift
//  JioMart
//
//  Created by <PERSON><PERSON><PERSON> on 09/07/25.
//

import Foundation
import UIKit
import AppsFlyerLib

@objcMembers
class DeepLinkManager: NSObject {
  
  static let shared = DeepLinkManager()
  
  private override init() {}
  
  @objc(handleURL:options:)
  func handle(url: URL, options: [UIApplication.OpenURLOptionsKey: Any] = [:]) -> Bool {
    let urlString = url.absoluteString
    
    if urlString.contains(JMInfoPlist.appsflyerDomain) {
      AppsFlyerLib.shared().handleOpen(url, options: options)
      return true
    }
    
    let param: [String: Any] = [
      "mUri": urlString,
      "payload": ""
    ]
    
    let dictionary: [String: Any] = [
      "enumValue": "deeplink",
      "param": DataConverter.jsonString(dictionary: param) ?? ""
    ]
    
    if isBridgeReady {
      sendToReactNative(dictionary: dictionary)
    } else {
      UserDefaults.standard.set(urlString, forK<PERSON>: JMConstant.UserDefaultsKeys.pendingDeepLink)
    }
    
    return true
  }
  
  @objc(handleUserActivity:restorationHandler:)
  func handle(userActivity: NSUserActivity, restorationHandler: @escaping ([Any]?) -> Void) -> Bool {
    if userActivity.activityType == NSUserActivityTypeBrowsingWeb,
       let url = userActivity.webpageURL {
      if isBridgeReady {
        return AppsFlyerLib.shared().continue(userActivity, restorationHandler: restorationHandler)
      } else {
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
          AppsFlyerLib.shared().continue(userActivity, restorationHandler: restorationHandler)
        }
      }
    }
    return true
  }
  
  private var isBridgeReady: Bool {
    guard let bridge = JMRNViewController.currentBridge else {
      return false
    }
    return bridge.isValid && !bridge.isLoading
  }
  
  private func sendToReactNative(dictionary: [String: Any]) {
    if let jsonString = DataConverter.jsonString(dictionary: dictionary) {
      JMEventEmitter.sendEvent(withName: "onDataReceived", body: jsonString)
    }
  }
}
