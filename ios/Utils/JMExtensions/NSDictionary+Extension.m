//
//  NSDictionary+Extension.m
//  JioMart
//
//  Created by <PERSON><PERSON><PERSON> on 24/06/25.
//
 
#import "NSDictionary+Extension.h"
 
@implementation NSDictionary (Extension)
 
- (NSString *) toJSONString {
    NSError * err;
    NSData * jsonData = [NSJSONSerialization  dataWithJSONObject:self options:NSJSONWritingPrettyPrinted error:&err];
    NSString * jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    return jsonString;
}
 
- (NSString*) getQueryStringFromDictionary {
    NSMutableString *resultString = [NSMutableString string];
    for (NSString* key in [self allKeys]) {
        if ([resultString length]>0)
            [resultString appendString:@"&"];
        [resultString appendFormat:@"%@=%@", key, [self objectForKey:key]];
    }
    NSLog(@"QueryString: %@", resultString);
    return resultString;
}
@end
