//
//  StringUtil.swift
//  JioMart
//
//  Created by Manish4 Sah on 22/07/25.
//

import Foundation
import CommonCrypto

extension String {
    func md5() -> String {
        let data = Data(self.utf8)
        let digest = data.withUnsafeBytes { bytes in
            return [UInt8](unsafeUninitializedCapacity: Int(CC_MD5_DIGEST_LENGTH)) { buffer, length in
                CC_MD5(bytes.baseAddress, CC_LONG(data.count), buffer.baseAddress)
                length = Int(CC_MD5_DIGEST_LENGTH)
            }
        }
        return digest.map { String(format: "%02x", $0) }.joined()
    }
}
