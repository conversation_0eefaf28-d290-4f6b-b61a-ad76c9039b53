//
//  UserSessionModel.swift
//  JioMart
//
//  Created by <PERSON><PERSON><PERSON> on 24/07/25.
//

import UIKit

public struct JMSessionModel: Codable {
  var cartId: Int?
  var channel: String?
  var customerId: Int?
  var authToken: String?
  var validDate: String?
  var cartCount: Int?
  var customerDetail: JMCustomerDetailModel?
  var isValid: Int?
  
  enum CodingKeys: String, CodingKey {
    case cartId = "cart_id"
    case channel = "channel"
    case customerId = "customer_id"
    case authToken = "id"
    case validDate = "valid_till"
    case cartCount = "cartCount"
    case customerDetail = "customerDetail"
    case isValid = "is_valid"
  }
  
  init() {}
  
  public init(from decoder: Decoder) throws {
    let values = try decoder.container(keyedBy: CodingKeys.self)
    cartId = try? values.decode(Int.self, forKey: .cartId)
    channel = try? values.decode(String.self, forKey: .channel)
    customerId = try? values.decode(Int.self, forKey: .customerId)
    authToken = try? values.decode(String.self, forKey: .authToken)
    validDate = try? values.decode(String.self, forKey: .validDate)
    cartCount = try? values.decode(Int.self, forKey: .cartCount)
    customerDetail = try? values.decode(JMCustomerDetailModel.self, forKey: .customerDetail)
    isValid = try? values.decode(Int.self, forKey: .isValid)
  }
}

public struct JMCustomerDetailModel: Codable {
  var firstName: String?
  var lastName: String?
  var fullName: String?
  public var shortName: String?
  var email: String?
  var mobileNumber: String?
  var dateOfBirth: String?
  var gender: String?
  public var preferredShippingaddress: Int?
  var preferredBillingAddress: Int?
  var consentToShareProfile: String?
  
  enum CodingKeys: String, CodingKey {
    case firstName = "firstname"
    case lastName = "lastname"
    case fullName = "display_name_full"
    case shortName = "display_name_short"
    case email = "email"
    case mobileNumber = "mobile_no"
    case dateOfBirth = "date_of_birth"
    case gender = "gender"
    case preferredShippingaddress = "preferred_shipping_address"
    case preferredBillingAddress = "preferred_billing_address"
    case consentToShareProfile = "consent_to_share_profile"
  }
  
  init() { }
  
  public init(from decoder: Decoder) throws {
    let values = try decoder.container(keyedBy: CodingKeys.self)
    firstName = try? values.decode(String.self, forKey: .firstName)
    lastName = try? values.decode(String.self, forKey: .lastName)
    fullName = try? values.decode(String.self, forKey: .fullName)
    shortName = try? values.decode(String.self, forKey: .shortName)
    email = try? values.decode(String.self, forKey: .email)
    mobileNumber = try? values.decode(String.self, forKey: .mobileNumber)
    dateOfBirth = try? values.decode(String.self, forKey: .dateOfBirth)
    gender = try? values.decode(String.self, forKey: .gender)
    preferredShippingaddress = try? values.decode(Int.self, forKey: .preferredShippingaddress)
    preferredBillingAddress = try? values.decode(Int.self, forKey: .preferredBillingAddress)
    consentToShareProfile = try? values.decode(String.self, forKey: .consentToShareProfile)
  }
}

struct UserSession: Codable {
  var status: String
  var data: OneRetailSessionData
}


struct OneRetailProfileData: Codable {
  let messageId: String?
  let errors: [[String: String]]?
  let success: Bool
  let data: ProfileData?
  
  enum CodingKeys: String, CodingKey {
    case messageId = "message_id"
    case errors
    case success
    case data
  }
}

struct ProfileData: Codable {
  let firstName: String?
  let lastName: String?
  let gender: String?
  let dob: String?
  let externalId: String?
  let email: String?
  let rrId: String?
  let mobile: String?
  
  enum CodingKeys: String, CodingKey {
    case firstName = "first_name"
    case lastName = "last_name"
    case gender
    case dob
    case externalId = "external_id"
    case email
    case rrId = "rr_id"
    case mobile
  }
}

public struct OneRetailSessionData: Codable {
  var craSession: CRASession?
  var jcpSession: JCPSession?
  var mstarSession: MStarSession?
  
  init(craSession: CRASession, jcpSession: JCPSession, mstarSession: MStarSession) {
    self.craSession = craSession
    self.jcpSession = jcpSession
    self.mstarSession = mstarSession
  }
  
  private enum CodingKeys: String, CodingKey {
    case craSession = "cra_session"
    case jcpSession = "jcp_session"
    case mstarSession = "mstar_session"
  }
  
  public init(from decoder: Decoder) throws {
    let container = try decoder.container(keyedBy: CodingKeys.self)
    craSession = try container.decode(CRASession.self, forKey: .craSession)
    jcpSession = try container.decode(JCPSession.self, forKey: .jcpSession)
    mstarSession = try container.decode(MStarSession.self, forKey: .mstarSession)
  }
}

struct CRASession: Codable {
  var accessToken: String?
  var expires: Int?
  var idToken: String?
  var refreshToken: String?
  var tokenType: String?
  
  init() { }
  private enum CodingKeys: String, CodingKey {
    case accessToken = "access_token"
    case expires
    case idToken = "id_token"
    case refreshToken = "refresh_token"
    case tokenType = "token_type"
  }
  
  init(from decoder: Decoder) throws {
    let container = try decoder.container(keyedBy: CodingKeys.self)
    accessToken = try container.decodeIfPresent(String.self, forKey: .accessToken)
    expires = try container.decodeIfPresent(Int.self, forKey: .expires)
    idToken = try container.decodeIfPresent(String.self, forKey: .idToken)
    refreshToken = try container.decodeIfPresent(String.self, forKey: .refreshToken)
    tokenType = try container.decodeIfPresent(String.self, forKey: .tokenType)
  }
}

struct JCPSession: Codable {
  var cookie: [String: String]?
  var domain: String?
  var httpOnly: Int?
  var maxAge: Int?
  var secure: Int?
  
  init() { }
  private enum CodingKeys: String, CodingKey {
    case cookie
    case domain
    case httpOnly = "http_only"
    case maxAge = "max_age"
    case secure
  }
  
  init(from decoder: Decoder) throws {
    let container = try decoder.container(keyedBy: CodingKeys.self)
    cookie = try container.decodeIfPresent([String: String].self, forKey: .cookie)
    domain = try container.decode(String.self, forKey: .domain)
    httpOnly = try container.decodeIfPresent(Int.self, forKey: .httpOnly)
    maxAge = try container.decodeIfPresent(Int.self, forKey: .maxAge)
    secure = try container.decodeIfPresent(Int.self, forKey: .secure)
  }
}

struct MStarSession: Codable {
  var cartID: String?
  var channel: String
  var createdTime: String?
  var customerID: Int?
  var authToken: String?
  var isValid: Bool
  var loganSessionID: String?
  var validTill: String?
  
  init(cartID: String?, channel: String?, createdTime: String?, customerID: Int?, authToken: String?, isValid: Bool, loganSessionID: String?, validTill: String?) {
    self.cartID = cartID
    self.channel = channel ?? ""
    self.createdTime = createdTime
    self.customerID = customerID
    self.authToken = authToken
    self.isValid = isValid
    self.loganSessionID = loganSessionID
    self.validTill = validTill
  }
  
  private enum CodingKeys: String, CodingKey {
    case cartID = "cart_id"
    case channel = "channel"
    case createdTime = "created_time"
    case customerID = "customer_id"
    case authToken = "id"
    case isValid = "is_valid"
    case loganSessionID = "logan_session_id"
    case validTill = "valid_till"
  }
  
  init(from decoder: Decoder) throws {
    let container = try decoder.container(keyedBy: CodingKeys.self)
    cartID = try container.decodeIfPresent(String.self, forKey: .cartID)
    channel = try container.decode(String.self, forKey: .channel)
    createdTime = try container.decodeIfPresent(String.self, forKey: .createdTime)
    customerID = try container.decodeIfPresent(Int.self, forKey: .customerID)
    authToken = try container.decodeIfPresent(String.self, forKey: .authToken)
    isValid = try container.decodeIfPresent(Bool.self, forKey: .isValid) ?? false
    loganSessionID = try container.decodeIfPresent(String.self, forKey: .loganSessionID)
    validTill = try container.decodeIfPresent(String.self, forKey: .validTill)
  }
}
