//
//  JMAddressModel.swift
//  JioMart
//
//  Created by <PERSON><PERSON><PERSON> on 25/04/25.
//


import Foundation
import GoogleMaps

class JMAddress: NSObject {
  var address: JMAddressModel?;
  
  override init() {
    self.address = JMAddressModel.init(city: "", state: "", pin: "")
  }
  
  init?(from address: GMSAddress){
    guard let country = address.country,
             let city = address.locality,
             let state = address.administrativeArea,
             let pin = address.postalCode else {
           return nil
       }
    self.address = JMAddressModel(city: city, state: state, pin: pin, country: country)
    self.address?.coordinate = CLLocationCoordinate2D(latitude: address.coordinate.latitude, longitude: address.coordinate.longitude)

    let fullAddress = address.lines?.first ?? ""
    
    var components = fullAddress.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespaces) }
    
    if let first = components.first, first.range(of: #"^[23456789CFGHJMPQRVWX]{4}\+[23456789CFGHJMPQRVWX]{2,}$"#, options: .regularExpression) != nil {
      components.removeFirst()
      print("first ---", first)

    }
    
    let cleanedAddress = components.joined(separator: ", ")
    
    let finalAddress = cleanedAddress.isEmpty ? "400020" : cleanedAddress
    
    let addressComponents = finalAddress.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespaces) }

    let subStringIndex: Int
    if addressComponents.count <= 3 {
        subStringIndex = 1
    } else if addressComponents.count > 5 {
        subStringIndex = 3
    } else {
        subStringIndex = 2
    }

    // Calculate the end index for slicing, handling potential negative results gracefully
    let endIndex = max(0, addressComponents.count - subStringIndex)

    // Ensure the end index does not exceed the array's bounds
    let finalEndIndex = min(endIndex, addressComponents.count)
    
    let addressText = addressComponents[0..<finalEndIndex].joined(separator: ",")
    
    self.address?.formattedAddress = finalAddress
    self.address?.address = addressText
    self.address?.area = address.subLocality ?? ""
    self.address?.lat = address.coordinate.latitude
    self.address?.lon = address.coordinate.longitude
  }
  
  func reverseGeocode(latitude: CLLocationDegrees, longitude: CLLocationDegrees, completion: @escaping (JMAddressModel?) -> Void) {
    let geocoder = GMSGeocoder();
    let coordinate = CLLocationCoordinate2D(latitude: latitude, longitude: longitude)
    geocoder.reverseGeocodeCoordinate(coordinate){response, _ in
        guard let address = response?.firstResult() else {
          completion(nil)
          return
        }
        if let results = response?.results(){
          var foundValidAddress = false
          for result in results {
            if let _ = result.postalCode {
              let locality = result.locality ?? ""
              let administrativeArea = result.administrativeArea ?? ""
              if (LanguageDetector.identifyLanguage(of: locality) == .english && LanguageDetector.identifyLanguage(of: administrativeArea) == .english) {
                if let jmAddress = JMAddress.init(from: result)?.address {
                  completion(jmAddress)
                  foundValidAddress = true
                  return
                }
              }
            }
          }
          if !foundValidAddress {
            completion(nil)
          }
        }
      
    }
  }
}
