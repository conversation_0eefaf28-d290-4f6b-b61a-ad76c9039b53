//
//  JMPaymentServiceProviderModule.swift
//  JioMart
//
//  Created by <PERSON><PERSON><PERSON> on 22/07/25.
//
import Foundation
import UIKit
import CoreData

@objc(JMPaymentServiceProviderModule)
class JMPaymentServiceProviderModule: NSObject {
  @objc
  func getPspAppList(_ params: NSDictionary, resolver resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
    let pspApps = params["upiIntentAppsArray"] as? [[String: Any]] ?? []
    let installedPSPApps = fetchInstalledPSPApps(from: pspApps)
    
    if installedPSPApps.isEmpty {
      let error = NSError(domain: "com.jfs.jbinvest", code: 404, userInfo: [NSLocalizedDescriptionKey: "No installed PSP apps found"])
      reject("NO_PSP_APPS", "No installed PSP apps found", error)
    } else {
      resolve(installedPSPApps)
      
    }
  }
  
  private func fetchInstalledPSPApps(from pspApps: [[String: Any]]) -> [[String: Any]] {
    var installedPSPApps = [[String: Any]]()
    for app in pspApps {
      if let urlScheme = app["AppURLPrefix"], let url = URL(string: urlScheme as? String ?? ""), UIApplication.shared.canOpenURL(url) {
        installedPSPApps.append(app)
      }
    }
    return installedPSPApps
  }
}
