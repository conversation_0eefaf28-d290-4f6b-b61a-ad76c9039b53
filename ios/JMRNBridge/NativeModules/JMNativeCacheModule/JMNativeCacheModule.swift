//
//  JMNativeCacheModule.swift
//  JioMart
//
//
import Foundation
import UIKit
import CoreData

@objc(JMNativeCacheModule)
class JMNativeCacheModule: NSObject {
  
  @objc
  func setUserDefaultItem(_ key: String, value: Any, resolver resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
    switch value {
    case let v as String:
      UserDefaults.standard.set(v, forKey: key)
    case let v as Bool:
      UserDefaults.standard.set(v, forKey: key)
    case let v as Int:
      UserDefaults.standard.set(v, forKey: key)
    case let v as Double:
      UserDefaults.standard.set(v, forKey: key)
    case let v as [Any]:
      UserDefaults.standard.set(v, forKey: key)
    case let v as [String: Any]:
      UserDefaults.standard.set(v, forKey: key)
    default:
      reject("invalid_type", "Unsupported UserDefault type for key: \(key)", nil)
      return
    }
    resolve(nil)
  }
  
  @objc
  func getUserDefaultItem(_ key: String, resolver resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
    let defaults = UserDefaults.standard
    if let obj = defaults.object(forKey: key) {
      resolve(obj)
    } else {
      resolve(nil)
    }
  }
  
  @objc
  func removeUserDefaultItem(_ key: String, resolver resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
    UserDefaults.standard.removeObject(forKey: key)
    resolve(nil)
  }
  
  @objc
  func clearAllUserDefaultItem(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
    if let appDomain = Bundle.main.bundleIdentifier {
        UserDefaults.standard.removePersistentDomain(forName: appDomain)
        UserDefaults.standard.synchronize()
    }
  }
  
  @objc
  func getAllUserDefaultKeys(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
    let keys = Array(UserDefaults.standard.dictionaryRepresentation().keys)
    resolve(keys)
  }
  
  @objc
  public func getCoreDataItem(_ key: String, resolver resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
    // For userSession key : UserSession is creating in mergeUserSession
    if key == JMConstant.StorageKeys.userSession {
      if let userSession = mergeUserSession(), let jsonString = DataConverter.dataToJSONString(userSession) {
        resolve(jsonString)
      } else {
        resolve(nil)
      }
      // For other keys
    } else {
      if let data = JMCoreDataAPIResponseModel.getData(key: key) {
        if let jsonString = DataConverter.dataToJSONString(data) {
          resolve(jsonString)
        } else if let string = try? NSKeyedUnarchiver.unarchivedObject(ofClass: NSString.self, from: data) {
          resolve(string)
        } else {
          resolve(nil)
        }
      } else {
        resolve(nil)
      }
    }
  }
  
  @objc
  public func deleteCoreDataItem(_ key: String, resolver resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
    JMCoreDataAPIResponseModel.deleteData(key: key)
  }
  
  @objc
  public func clearAllCoreData(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
    JMCoreDataAPIResponseModel.deleteAll()
  }
  
  func mergeUserSession() -> Data? {
    let userSession = JMCacheModel<JMSessionModel>.getItemsFromCache(key: JMConstant.StorageKeys.userSession)
    let craSession: CRASession? = retrieveSessionData(key: JMConstant.StorageKeys.craUserSessionData)
    let jcpSession: JCPSession? = retrieveSessionData(key: JMConstant.StorageKeys.jcpUserSessionData)
    
    if let craSession, let jcpSession, let userSession {
      let mStarSession = MStarSession(cartID: String(userSession.cartId ?? 0), channel: userSession.channel, createdTime: "", customerID: userSession.customerId, authToken: userSession.authToken, isValid: (userSession.isValid ?? 0) == 1, loganSessionID: userSession.authToken, validTill: userSession.validDate)
      let userSessionModel = UserSession(
        status: "success",
        data:
          OneRetailSessionData(craSession: craSession, jcpSession: jcpSession, mstarSession: mStarSession)
      )
      do {
        let data = try JSONEncoder().encode(userSessionModel)
        return data
      } catch {
        return nil
      }
    }
    return nil
  }
  
  @objc
  static func requiresMainQueueSetup() -> Bool {
    return false
  }
}

extension JMNativeCacheModule {
  func retrieveSessionData<T: Codable>(key: String) -> T? {
    if let jsonData = UserDefaults.standard.data(forKey: key) {
      do {
        let session = try JSONDecoder().decode(T.self, from: jsonData)
        return session
      } catch {
        print("Error decoding session data: \(error)")
      }
    }
    return nil
  }
}
