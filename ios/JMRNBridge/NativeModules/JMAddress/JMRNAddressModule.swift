//
//  JMRNAddressModule.swift
//  JioMart
//
//  Created by Manish<PERSON> Sah on 25/04/25.
//


import Foundation
import GoogleMaps

@objc(JMRNAddressModule)
class JMRNAddressModule: NSObject {
  var jmAddress = JMAddress()
  
  @objc
  func getReverseGeoCodeFromLatLong(_ latitude: NSNumber, longitude: NSNumber, resolver: @escaping RCTPromiseResolveBlock, rejecter: @escaping RCTPromiseRejectBlock) {
    DispatchQueue.main.async  {
      self.jmAddress.reverseGeocode(latitude: latitude.doubleValue, longitude: longitude.doubleValue){ response in
        if let result = response {
          if let address = DataConverter.codableToJSONString(result) {
            resolver(address)
          }else{
            rejecter("JSON_ERROR", "failed to convert address to json", nil)
          }
        }else{
          rejecter("REVERSE_GEOCODE_FAILED", "Could not fetch address from coordinates", nil)
        }
      }
    }
  }
}
