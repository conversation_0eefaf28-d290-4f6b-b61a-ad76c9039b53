//
//  JMEventEmitter.m
//
//
//

#import "JMEventEmitter.h"

@implementation JMEventEmitter
RCT_EXPORT_MODULE();

static JMEventEmitter *_sharedInstance = nil;

+ (id)allocWithZone:(NSZone *)zone {
  static dispatch_once_t onceToken;
  dispatch_once(&onceToken, ^{
    _sharedInstance = [super allocWithZone:zone];
  });
  return _sharedInstance;
}

+ (void)sendEventWithName:(NSString *)name body:(id)body {
  if (_sharedInstance != nil) {
    [_sharedInstance sendEventWithName:name body:body];
  }
}

- (NSArray<NSString *> *)supportedEvents {
  return @[@"onDataReceived"];
}

+ (BOOL)requiresMainQueueSetup {
  return YES;
}
@end
