<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="23605" systemVersion="24D81" minimumToolsVersion="Automatic" sourceLanguage="Swift" usedWithSwiftData="YES" userDefinedModelVersionIdentifier="">
    <entity name="JMCoreDataAPIResponseModel" representedClassName="JMCoreDataAPIResponseModel" syncable="YES">
        <attribute name="key" optional="YES" attributeType="String"/>
        <attribute name="responseData" optional="YES" attributeType="Binary"/>
    </entity>
</model>