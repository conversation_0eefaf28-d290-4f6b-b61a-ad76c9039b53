//
//  JMCoreDataAPIResponseModel.swift
//  JioMart
//
//  Created by <PERSON><PERSON><PERSON> on 24/07/25.
//

import Foundation
import CoreData
import UIKit

@objc(JMCoreDataAPIResponseModel)
public class JMCoreDataAPIResponseModel: NSManagedObject {
    @nonobjc
    public class func fetchRequest() -> NSFetchRequest<JMCoreDataAPIResponseModel> {
        return NSFetchRequest<JMCoreDataAPIResponseModel>(entityName: "JMCoreDataAPIResponseModel")
    }

    @NSManaged public var responseData: Data?
    @NSManaged public var key: String?

    static func save(data: Data, forKey key: String) {
        guard let appDelegate = UIApplication.shared.delegate as? AppDelegate else { return }
        let managedContext = appDelegate.persistentContainer.viewContext

        let fetchRequest: NSFetchRequest<JMCoreDataAPIResponseModel> = JMCoreDataAPIResponseModel.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "key == %@", key)

        if   let responseModelArray = try? managedContext.fetch(fetchRequest), let  responseModel = responseModelArray.last {
            responseModel.responseData = data
            appDelegate.saveContext()
            return
        }
        
        let responseModel = JMCoreDataAPIResponseModel(context: managedContext)
        responseModel.key = key
        responseModel.responseData = data
        appDelegate.saveContext()
    }

    static func getData(key: String) -> Data? {
        var fetchedData: Data?
        if Thread.isMainThread {
                fetchedData = fetchDataFromCoreData(key: key)
            } else {
                DispatchQueue.main.sync {
                    fetchedData = fetchDataFromCoreData(key: key)
                }
            }
        return fetchedData
    }
    
    private static func fetchDataFromCoreData(key: String) -> Data? {
        guard let appDelegate = UIApplication.shared.delegate as? AppDelegate else {
            print("❌ Error: Unable to access AppDelegate")
            return nil
        }

        let managedContext = appDelegate.persistentContainer.viewContext
        let fetchRequest: NSFetchRequest<JMCoreDataAPIResponseModel> = JMCoreDataAPIResponseModel.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "key == %@", key)

        do {
            let responseModelArray = try managedContext.fetch(fetchRequest)

            if responseModelArray.isEmpty {
                return nil
            }

            guard let responseModel = responseModelArray.last else { return nil }

            // Ensure responseData is not nil
            if let data = responseModel.responseData {
                return data
            } else {
                return nil
            }
        } catch let error as NSError {
            return nil
        }
    }

    static func deleteData(key: String) {
        guard let appDelegate = UIApplication.shared.delegate as? AppDelegate else { return }

        let managedContext = appDelegate.persistentContainer.viewContext

        let fetchRequest: NSFetchRequest<JMCoreDataAPIResponseModel> = JMCoreDataAPIResponseModel.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "key == %@", key)

        if   let responseModelArray = try? managedContext.fetch(fetchRequest), let  responseModel = responseModelArray.last {
            managedContext.delete(responseModel)
            appDelegate.saveContext()
        }
    }
    
    static func deleteAll() {
        guard let appDelegate = UIApplication.shared.delegate as? AppDelegate else { return }

        let managedContext = appDelegate.persistentContainer.viewContext
        
        let fetchRequest: NSFetchRequest<JMCoreDataAPIResponseModel> = JMCoreDataAPIResponseModel.fetchRequest()

        do {
            let deleteResultRequests = try managedContext.fetch(fetchRequest)
            for deleteResultRequest in deleteResultRequests {
                managedContext.delete(deleteResultRequest)
            }
            appDelegate.saveContext()
        } catch {}
    }
}
