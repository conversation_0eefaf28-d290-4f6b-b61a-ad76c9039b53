//
//  JMCacheModel.swift
//  JioMart
//
//  Created by <PERSON><PERSON><PERSON> on 24/07/25.
//

import Foundation

class JMCacheModel<T: Codable> {
    class func getItemsFromCache(key: String) -> T? {
        guard let apiResponseData = JMCoreDataAPIResponseModel.getData(key: key), let discoverMoreItems = try? JSONDecoder().decode(T.self, from: apiResponseData)  else { return nil }
        return  discoverMoreItems
    }
  
  class func getDataFromCache(key: String) -> Data? {
      guard let apiResponseData = JMCoreDataAPIResponseModel.getData(key: key) else { return nil }
      return apiResponseData
  }
    
    class func saveItemsToCache(item: T?, key: String) {
        guard let data = try? JSONEncoder().encode(item) else { return }
        JMCoreDataAPIResponseModel.save(data: data, forKey: key)
    }
    
    static func removeAllItemsFromCache(key: String) {
        JMCoreDataAPIResponseModel.deleteData(key: key)
    }
}
