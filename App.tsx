import React from 'react';
import {NavigationContainer} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import networkService from './modules/jiomart-common/src/JMNetworkConnectionUtility';
import {JMSharedViewModel} from './modules/jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from './modules/jiomart-common/src/SourceType';
import {JioMartMainUI, PropData, readSplashFileData, setAppSource, setExternalDeeplinkData, setReactNativeEnv} from './modules/jiomart-main/src';
import {AppScreens} from './modules/jiomart-common/src/JMAppScreenEntry';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import { Platform } from 'react-native';

const Stack = createNativeStackNavigator<RootStackNavigatorParamsList>();

type RootStackNavigatorParamsList = {
  JioMartMainUI: {
    params: {
      directDeeplink: string;
    };
  };
};



function getFirstRoute(): string {
  return 'jiomart://com.jpl.jiomart/JMSplashScreen';
}

function setPropDataInApp(props: PropData){
  console.log("props here", props)
  setReactNativeEnv(props)
  setExternalDeeplinkData(props)
  setAppSource(props?.source ?? "")
  readSplashFileData()
}

const App = ({props} : {props: PropData}) => {
  networkService.startMonitoring();
  setPropDataInApp(props)
  return (
    <SafeAreaProvider>
      <NavigationContainer ref={JMSharedViewModel.Instance.navigationRef}>
        <Stack.Navigator
          initialRouteName={AppScreens.JIOMART_MAIN_UI}
          screenOptions={{
            headerShown: false,
            gestureEnabled: Platform.OS==="ios" ? false : true,
          }}>
          <Stack.Screen
            name={AppScreens.JIOMART_MAIN_UI}
            component={JioMartMainUI}
            initialParams={{
              params: {
                directDeeplink: getFirstRoute(),
              },
            }}
          />
        </Stack.Navigator>
      </NavigationContainer>
    </SafeAreaProvider>
  );
};
export default App;
