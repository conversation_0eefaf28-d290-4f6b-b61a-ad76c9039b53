apply plugin: "com.android.application"
apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"
apply plugin: 'com.google.gms.google-services'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-parcelize'

/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */
react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '../..'
    // root = file("../../")
    //   The folder where the react-native NPM package is. Default is ../../node_modules/react-native
    // reactNativeDir = file("../../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../../node_modules/@react-native/codegen
    // codegenDir = file("../../node_modules/@react-native/codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../../node_modules/react-native/cli.js
    // cliFile = file("../../node_modules/react-native/cli.js")

    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]

    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []

    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    // hermesFlags = ["-O", "-output-source-map"]

    /* Autolinking */
    autolinkLibrariesWithApp()
}

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = false

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = 'org.webkit:android-jsc-intl:+'`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'org.webkit:android-jsc:+'

android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdk rootProject.ext.compileSdkVersion

    namespace "com.jcpjiomart"
    defaultConfig {
        applicationId "com.jpl.jiomart"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 2063
        versionName "2.0.63"
    }
    signingConfigs {
//        debug {
//             keyAlias MYAPP_UPLOAD_KEY_ALIAS
//             keyPassword MYAPP_UPLOAD_KEY_PASSWORD
//             storeFile file(System.getenv("KEYSTORE") as String ?: MYAPP_UPLOAD_STORE_FILE)
//             storePassword MYAPP_UPLOAD_STORE_PASSWORD
//        }
        release {
            keyAlias MYAPP_UPLOAD_KEY_ALIAS
            keyPassword MYAPP_UPLOAD_KEY_PASSWORD
            storeFile file(System.getenv("KEYSTORE") as String ?: MYAPP_UPLOAD_STORE_FILE)
            storePassword MYAPP_UPLOAD_STORE_PASSWORD
        }
    }

    buildTypes {
        debug {
            shrinkResources false
            minifyEnabled false
            debuggable true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
//              signingConfig signingConfigs.debug
        }
        release {
            debuggable false
            shrinkResources true
            minifyEnabled true
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
            signingConfig signingConfigs.release
        }
    }

    compileOptions {
        targetCompatibility JavaVersion.VERSION_19
        sourceCompatibility JavaVersion.VERSION_19
    }
    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_19.toString()
    }

    flavorDimensions "default"
    productFlavors {
        sit {
            dimension "default"
            buildConfigField "String", 'MY_PIN_ONE', "\"sha256/hcKtItmwr2n/y+0FJlVziq8rqpTWBk1F/tIJ69/G9yw=\""
            buildConfigField "String", 'MY_PIN_TWO', "\"sha256/K7rZOrXHknnsEhUH8nLL4MZkejquUuIvOIr6tCa0rbo=\""
            buildConfigField "String", 'MY_PIN_THREE', "\"sha256/C5+lpZ7tcVwmwQIMcRtPbsQtWLABXhQzejna0wHFr8M=\""
            buildConfigField "String", 'MY_PIN_FOUR', "\"sha256/VlBMjxXkF0JrrhK+xp6Gt9gIWINq9IfAet7yIkG9bPI=\""
            buildConfigField "String", 'MY_PIN_SERVER_URL', "\"jiomart-sit.jio.com\""
            manifestPlaceholders = [
                    cleverTapAccountId: "TEST-98R-W4Z-495Z",
                    cleverTapToken: "TEST-140-2bc"
            ]
        }
        production {
            dimension "default"
            buildConfigField "String", 'MY_PIN_ONE', "\"sha256/VlBMjxXkF0JrrhK+xp6Gt9gIWINq9IfAet7yIkG9bPI=\""
            buildConfigField "String", 'MY_PIN_TWO', "\"sha256/I3B6Hzs++ttB7kUwqMCkkE1cXBu8W38aO461qniARbk=\""
            buildConfigField "String", 'MY_PIN_THREE', "\"sha256/glDak60Euna4vp0WBWSa2zLXWmcz3Ip2s9i6j12AQvM=\""
            buildConfigField "String", 'MY_PIN_FOUR', "\"sha256/udG/KSE/IpA24g0vMgCzCA1igIZowR9lcKQBAh4Yy0w=\""
            buildConfigField "String", 'MY_PIN_SERVER_URL', "\"www.jiomart.com\""
            manifestPlaceholders = [
                    cleverTapAccountId: "88R-W4Z-495Z",
                    cleverTapToken: "140-2bb"
            ]
        }
    }
    buildFeatures {
        viewBinding = true
        compose = true
    }
    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.15"
    }
}

repositories {
    flatDir{
        dirs 'libs'
    }
}

dependencies {
    // The version of react-native is set by the React Native Gradle Plugin
    implementation("com.facebook.react:react-android")
    implementation "com.google.android.gms:play-services-location:21.0.1"
    implementation(name:'JioAdsSdk_v2.1.18_Media3', ext:'aar')

    implementation 'com.google.code.gson:gson:2.13.1'
    implementation 'com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1'
    implementation ("com.google.android.gms:play-services-base:17.6.0")
    implementation ("com.google.android.gms:play-services-maps:17.0.1")
    implementation(platform("androidx.compose:compose-bom:2023.10.01"))
    implementation("androidx.compose.ui:ui")
    implementation("androidx.compose.runtime:runtime")
    implementation 'com.facebook.android:facebook-android-sdk:16.3.0'
    implementation 'com.clevertap.android:clevertap-android-sdk:7.0.2'
    implementation("com.google.android.play:integrity:1.4.0")

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
}
