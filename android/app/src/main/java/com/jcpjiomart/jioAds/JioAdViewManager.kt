package com.jcpjiomart.jioAds

import com.facebook.react.bridge.Callback
import com.facebook.react.bridge.ReadableMap
import com.facebook.react.bridge.WritableMap
import com.facebook.react.uimanager.SimpleViewManager
import com.facebook.react.uimanager.ThemedReactContext
import com.facebook.react.uimanager.ViewGroupManager
import com.facebook.react.uimanager.annotations.ReactProp
import com.facebook.react.uimanager.events.RCTEventEmitter


class JioAdViewManager : ViewGroupManager<JioAdViewGroup>() {


    companion object {
        const val REACT_CLASS = "JioADsNativeComponent"
    }
    override fun getName(): String {
        return REACT_CLASS
    }
    override fun needsCustomLayoutForChildren(): Boolean {
        return false
    }

    @ReactProp(name = "adType")
    fun setAdspotKey(view: JioAdViewGroup, adType: Int) {
        //view.setAdType(adType)
    }

    @ReactProp(name = "adspotKey")
    fun setAdspotKey(view: JioAdViewGroup, adspot: String) {
        //view.setAdspotId(adspot)77
    }

    @ReactProp(name = "adHeight")
    fun setHeight(view: JioAdViewGroup, adHeight: Int) {
        //view.setAdHeight(adHeight)
    }

    @ReactProp(name = "adWidth")
    fun setWidth(view: JioAdViewGroup, adWidth: Int) {
        //view.setAdWidth(adWidth)
    }

    @ReactProp(name = "adCustomHeight")
    fun setCustomHeight(view: JioAdViewGroup, adHeight: Int) {
        //view.setAdCustomHeight(adHeight)
    }

    @ReactProp(name = "adCustomWidth")
    fun setCustomWidth(view: JioAdViewGroup, adWidth: Int) {
        //view.setAdCustomWidth(adWidth)
    }

    @ReactProp(name = "adMetaData")
    fun setAdMetaData(view: JioAdViewGroup,params: ReadableMap) {
        //Console.debug("setAdMetaData", "JioAdViewManager || params:>> $params")
        //view.setAdMetaData(params)
    }

    @ReactProp(name = "data")
    fun setAdData(view: JioAdViewGroup,params: ReadableMap) {
        view.setAdData(params)
    }

    override fun getExportedCustomBubblingEventTypeConstants(): Map<String, Any> {
        return mapOf(
            "onChange" to mapOf(
                "phasedRegistrationNames" to mapOf(
                    "bubbled" to "onChange"
                )
            )
        )
    }

    private fun sendEvent(reactContext: ThemedReactContext, viewTag: Int, eventName: String, params: WritableMap) {
        reactContext
            .getJSModule(RCTEventEmitter::class.java)
            .receiveEvent(viewTag, eventName, params)
    }

    override fun createViewInstance(reactContext: ThemedReactContext): JioAdViewGroup {
        return JioAdViewGroup(reactContext)
    }

}