package com.jcpjiomart.jioAds

import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.bridge.ReadableMap
import com.jcpjiomart.R
import com.jio.jioads.adinterfaces.JioAdError
import com.jio.jioads.adinterfaces.JioAdListener
import com.jio.jioads.adinterfaces.JioAdView
import com.jio.jioads.adinterfaces.JioAdView.AD_TYPE
import java.security.MessageDigest

class JioAdModule internal constructor(private var reactContext: ReactApplicationContext) :
    ReactContextBaseJavaModule(
        reactContext
    ) {
        companion object {
            private  val adViewMap: MutableMap<String, JioAdView> = HashMap()

            fun getAdView(key: String): JioAdView? {
                return adViewMap[key]
            }
            fun setAdView(key: String, value: JioAdView) {
                adViewMap[key] = value
            }
            fun clearAdView(key: String) {
                adViewMap.remove(key)
            }
            fun createUniqueAdViewKey(params: ReadableMap): String {
                val adType = if (params.hasKey("adType")) params.getInt("adType") else 5
                val adSpotId = if (params.hasKey("adspotKey")) params.getString("adspotKey") ?: "" else ""
                val adHeight = if (params.hasKey("adHeight")) params.getInt("adHeight") else 0
                val adWidth = if (params.hasKey("adWidth")) params.getInt("adWidth") else 0

                val adMetaData = if (params.hasKey("adMetaData")) params.getMap("adMetaData") else null
                val metaKey = adMetaData?.toHashMap()
                    ?.toSortedMap() // sort keys to ensure consistent hash
                    ?.entries
                    ?.joinToString("&") { "${it.key}=${it.value}" } ?: ""

                val rawKey = "$adSpotId|$adType|$adWidth|$adHeight|$metaKey"

                return md5(rawKey)
            }
            fun md5(input: String): String {
                val bytes = MessageDigest.getInstance("MD5").digest(input.toByteArray())
                return bytes.joinToString("") { "%02x".format(it) }
            }
        }

    @ReactMethod
    fun checkAdStatus(params: ReadableMap, promise: Promise) {
      try {
          val adType = if(params.hasKey("adType")) params.getInt("adType") else 5
          val adSpotId =  if(params.hasKey("adspotKey")) params.getString("adspotKey") else ""
          val adHeight = if(params.hasKey("adHeight")) params.getInt("adHeight") else 0
          val adWidth =  if(params.hasKey("adWidth")) params.getInt("adWidth") else 0
          val adMetaData = if(params.hasKey("adMetaData")) params.getMap("adMetaData") else null

          if(adSpotId == ""){
              promise.resolve(false)
              return
          }

          val mAdType = when(adType) {
              1 -> AD_TYPE.DYNAMIC_DISPLAY
              2 -> AD_TYPE.INSTREAM_VIDEO
              3 -> AD_TYPE.INTERSTITIAL
              4 -> AD_TYPE.CONTENT_STREAM
              5 -> AD_TYPE.CUSTOM_NATIVE
              6 -> AD_TYPE.INSTREAM_AUDIO
              else -> AD_TYPE.INSTREAM_VIDEO
          }

          if (!reactContext.hasCurrentActivity()) {
              promise.resolve(false)
              return
          }

          var adView = JioAdView(reactContext.currentActivity!!, adSpotId!!, mAdType)
          if (mAdType == AD_TYPE.CUSTOM_NATIVE) {
              adView.setCustomNativeAdContainer(R.layout.jio_custom_native_layout)
              adView.setCustomImageSize(adWidth, adHeight)
          }
          adView.cacheAd()
          adView.setAdListener(object : JioAdListener() {
              override fun onAdPrepared(adView: JioAdView?) {
                promise.resolve(true)
                setAdView(createUniqueAdViewKey(params), adView!!)
                  
              }

              override fun onAdRender(jioAdView: JioAdView?) {
                  promise.resolve(true)
              }

              override fun onAdFailedToLoad(adView: JioAdView?, jioAdError: JioAdError?) {
                  promise.resolve(false)
              }
          })

      } catch (e: Exception) {
          promise.resolve(false)
      }
    }

    override fun getName(): String {
        return "JioAdModule"
    }
}