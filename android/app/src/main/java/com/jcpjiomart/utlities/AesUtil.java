package com.jcpjiomart.utlities;

import java.io.Serializable;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;


public class AesUtil implements Serializable {
    public static byte[] encrypt(byte[] plainData, byte[] key, byte[] iv) {
        byte[] crypted = null;
        try {
            if (key != null) {
                SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
                Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
                IvParameterSpec ips = new IvParameterSpec(Util.getJioMapping().getBytes());
                cipher.init(Cipher.ENCRYPT_MODE, keySpec, ips);

                crypted = cipher.doFinal(plainData);
            }
        } catch (Exception e) {
        }
        return crypted;
    }

    public static byte[] decrypt(byte[] encryptedData, byte[] key, byte[] iv) {
        byte[] data = null;
        try {
            if (key != null) {
                SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
                Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
                IvParameterSpec ips = new IvParameterSpec(Util.getJioMapping().getBytes());
                cipher.init(Cipher.DECRYPT_MODE, keySpec, ips);
                if (encryptedData != null) {
                    data = cipher.doFinal(encryptedData);
                }
            }
        } catch (Exception e) {
        }
        return data;
    }
}

