package com.jcpjiomart.utlities

import android.content.Context
import android.content.pm.PackageManager
import android.content.pm.Signature
import android.os.Build
import android.util.Base64
import android.util.Log
import android.widget.Toast
import com.google.android.play.core.integrity.IntegrityManagerFactory
import com.google.android.play.core.integrity.IntegrityTokenRequest
import com.jcpjiomart.BuildConfig
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.io.BufferedReader
import java.io.File
import java.io.IOException
import java.io.InputStreamReader
import java.security.MessageDigest

object PlayIntegrityUtils {

    private const val TAG = "PlayIntegrity"
    private  fun callPlayIntegrityApi(context: Context, callBack: PlayIntegrityCallback) {
        val nonce = generateNonce()
        checkAppIntegrity(
            context = context,
            nonce = nonce,
            onResult = { isValid, payload, rawToken ->
                CoroutineScope(Dispatchers.Main).launch {
                    if (isValid) {
                        Toast.makeText(context, "App is authentic and device is safe.", Toast.LENGTH_LONG).show()
                    } else {
                        Toast.makeText(context, "Integrity check failed", Toast.LENGTH_LONG).show()
                    }
                    callBack(isValid, payload, rawToken, null)
                }
            },
            onError = { error ->
                CoroutineScope(Dispatchers.Main).launch {
                    Toast.makeText(context, "Error: ${error.localizedMessage}", Toast.LENGTH_LONG).show()
                    callBack(false, null, null, error)
                }
            }
        )
    }
    private fun generateNonce(): String {
        val nonceBytes = ByteArray(24) // 24 bytes = 192 bits
        java.security.SecureRandom().nextBytes(nonceBytes)
        val base64Nonce = android.util.Base64.encodeToString(nonceBytes, android.util.Base64.URL_SAFE or android.util.Base64.NO_WRAP)
        val timestamp = System.currentTimeMillis()
        return "$base64Nonce-$timestamp"
    }
    private fun checkAppIntegrity(
        context: Context,
        nonce: String,
        onResult: (isValid: Boolean, payload: JSONObject?, rawToken: String?) -> Unit,
        onError: (Exception) -> Unit
    ) {
        val integrityManager = IntegrityManagerFactory.create(context)
        val request = IntegrityTokenRequest.builder().setNonce(nonce).build()

        integrityManager.requestIntegrityToken(request)
            .addOnSuccessListener { response ->
                val token = response.token()
                try {
                    val payload = decodeJWT(token)
                    val isValid = isIntegritySatisfied(payload)
                    onResult(isValid, payload, token)
                } catch (e: Exception) {
                    onError(e)
                }
            }
            .addOnFailureListener { exception ->
                onError(exception)
            }
    }

    private fun decodeJWT(token: String): JSONObject {
        val parts = token.split(".")
        if (parts.size != 3) throw IllegalArgumentException("Invalid JWT format")
        val payloadEncoded = parts[1]
        val padded = payloadEncoded.padEnd((payloadEncoded.length + 3) / 4 * 4, '=')
        val decoded = Base64.decode(padded, Base64.URL_SAFE or Base64.NO_WRAP)
        return JSONObject(String(decoded, Charsets.UTF_8))
    }

    private fun isIntegritySatisfied(payload: JSONObject): Boolean {
        val appVerdict = payload
            .optJSONObject("appIntegrity")
            ?.optString("appRecognitionVerdict", "")

        val deviceVerdicts = payload
            .optJSONObject("deviceIntegrity")
            ?.optJSONArray("deviceRecognitionVerdict")

        val meetsApp = appVerdict == "PLAY_RECOGNIZED"
        val meetsDevice = deviceVerdicts?.let {
            (0 until it.length()).any { i -> it.optString(i) == "MEETS_DEVICE_INTEGRITY" }
        } ?: false

        Log.d(TAG, "App Verdict: $appVerdict, Device Verdicts: $deviceVerdicts")
        return meetsApp && meetsDevice
    }

    enum class Result {
        VALID,
        INVALID,
        UNKNOWN
    }

    private const val SD1 = "TG8M7dBvf44h4TBZmE7THj"
    private const val SD2 = "tuzdcT0EAsKkNdY4EtFVU="
    fun validate(context: Context): Result {
        context.getAppSignature()?.string()?.let { currentSignature ->
            Log.d("validate signature", "current-appsign-02 $currentSignature")
            return if (currentSignature == "$SD1$SD2") {
                Result.VALID
            } else {
                Result.INVALID
            }
        }
        return Result.UNKNOWN
    }

    private fun Context.getAppSignature(): Signature? = if (Build.VERSION.SDK_INT < 28) {
        packageManager.getPackageInfo(
            packageName,
            PackageManager.GET_SIGNATURES
        ).signatures?.firstOrNull()
    } else {
        packageManager.getPackageInfo(
            packageName,
            PackageManager.GET_SIGNING_CERTIFICATES
        ).signingInfo?.apkContentsSigners?.firstOrNull()
    }

    private fun Signature.string(): String? = try {
        val signatureBytes = toByteArray()
        val digest = MessageDigest.getInstance("SHA-256")
        val hash = digest.digest(signatureBytes)
        Base64.encodeToString(hash, Base64.NO_WRAP)
    } catch (exception: Exception) {
        null
    }

    private val isTestBuild: Boolean
        get() {
            val buildTags = Build.TAGS
            return buildTags != null && buildTags.contains("test-keys")
        }

    fun callRooted(context: Context) {
        isRootedDevice(context) { isRooted ->
            if (isRooted) {
                Toast.makeText(context, "Device may be rooted or tampered!", Toast.LENGTH_LONG).show()
            } else {
                Toast.makeText(context, "Device looks safe.", Toast.LENGTH_SHORT).show()
            }
        }
    }
    var deviceInfo = ""

    // another Android SDK emulator check
    fun isRootedDevice(context: Context, appLevelFlag: Int = 1, onResult: (Boolean) -> Unit) {
        if (BuildConfig.DEBUG) return onResult(false)

        val appSignValid = when (appLevelFlag) {
            0, 3 -> Result.VALID
            1, 2 -> validate(context)
            else -> Result.VALID
        }

        val isEmulator = when (appLevelFlag) {
            0 -> false
            else -> isEmulatorDevice()
        }

        callPlayIntegrityApi(context) { isPlayIntegrityValid, payload, rawToken, error ->
            val isTestBuild = isTestBuild
            val hasSuperuserAPK = hasSuperuserAPK()
            val hasChainfiresupersu = hasSuperAppByPackageNameInstalled(context)
            val hasSU = hasSU()

            val result = isTestBuild || isEmulator || hasSuperuserAPK || hasChainfiresupersu || hasSU || checkMemory() || appSignValid != Result.VALID || (!isPlayIntegrityValid && error!=null)

            Log.d(
                "RootChecker",
                "appLevelFlag integrityValid: $isPlayIntegrityValid $appLevelFlag appSignValid: ${appSignValid != Result.VALID} isEmulator: $isEmulator isTestBuild: $isTestBuild hasSuperuserAPK: $hasSuperuserAPK hasChainfiresupersu: $hasChainfiresupersu hasSU: $hasSU"
            )

            onResult(result)
        }
    }

    private fun isEmulatorDevice(): Boolean {
        val brand = Build.BRAND.lowercase()
        val fingerPrint = Build.FINGERPRINT.lowercase()
        val device = Build.DEVICE.lowercase()
        val product = Build.PRODUCT.lowercase()
        val board = Build.BOARD.lowercase()
        val hardware = Build.HARDWARE.lowercase()
        deviceInfo = "brand- $brand fingerPrint- $fingerPrint device- $device product- $product board- $board hardware- $hardware board- ${Build.BOARD} manufacturer- ${Build.MANUFACTURER}"
        return (brand.startsWith("generic") && device.startsWith("generic"))
                || fingerPrint.startsWith("generic")
                || fingerPrint.startsWith("unknown")
                || hardware.contains("intel")
                || hardware.contains("goldfish")
                || hardware.contains("ranchu")
                || board.contains("google_sdk")
                || board.contains("emulator")
                || board.contains("android sdk built for x86")
                || Build.MANUFACTURER.lowercase().contains("genymotion")
                || product.contains("sdk_google")
                || product.contains("google_sdk")
                || product.contains("sdk")
                || product.contains("sdk_x86")
                || product.contains("sdk_gphone64_arm64")
                || product.contains("vbox86p")
                || product.contains("emulator")
                || product.contains("simulator")
                || hardware == "vbox86"
                || ("QC_Reference_Phone" == Build.BOARD && !"Xiaomi".equals(Build.MANUFACTURER, ignoreCase = true))
                || hardware.contains("nox")
                || board.contains("droid4x")
                || product == "vbox86p"
                || product.contains("nox")
                || board.contains("nox")
    }


    private fun hasSuperuserAPK(): Boolean {
        try {
            val checkAppDir = arrayOf(
                "/system/app/Superuser.apk",
                "/system/etc/init.d/99SuperSUDaemon",
                "/dev/com.koushikdutta.superuser.daemon",
                "/system/xbin/daemonsu",
                "/system/app/SuperSu.apk"


            )
            for (suApp in checkAppDir) {
                try {
                    if (File(suApp).exists()) {
                        return true
                    }
                }catch (e:Exception){

                }
            }
            return false
        } catch (e: Exception) {

            return false
        }

    }

    private fun hasSuperAppByPackageNameInstalled(context: Context): Boolean {
        val suspiciousPackages = listOf(
            "com.devadvance.rootcloak2",
            "fi.razerman.bancontactrootbypasser",
            "net.csu333.surrogate",
            "com.devadvance.rootcloak",
            "com.devadvance.rootcloakplus",
            "com.koushikdutta.superuser",
            "com.thirdparty.superuser",
            "eu.chainfire.supersu",
            "com.noshufou.android.su",
            "com.zachspong.temprootremovejb",
            "com.ramdroid.appquarantine",
            "com.topjohnwu.magisk",
            "com.noshufou.android.su.elite",
            "com.yellowes.su",
            "com.koushikdutta.rommanager",
            "com.koushikdutta.rommanager.license",
            "com.dimonvideo.luckypatcher",
            "com.chelpus.lackypatch",
            "com.ramdroid.appquarantinepro",
            "de.robv.android.xposed.installer",
            "com.saurik.substrate",
            "com.amphoras.hidemyroot",
            "com.amphoras.hidemyrootadfree",
            "com.formyhm.hiderootPremium",
            "com.formyhm.hideroot",
            "me.phh.superuser",
            "eu.chainfire.supersu.pro",
            "com.kingouser.com",
            "com.android.vending.billing.InAppBillingService.COIN",
            "com.applisto.appcloner.premium",
            "com.applisto.appcloner",
            "com.lexa.fakegps",
            "com.blogspot.newapphorizons.fakegps",
            "com.ninja.toolkit.pulse.fake.gps.pro",
            "com.incorporateapps.fakegps.fre",
            "com.rosteam.gpsemulator",
            "com.hopefactory2021.fakegpslocation",
            "com.just4funtools.fakegpslocationprofessional",
            "com.locationchanger",
            "com.gsmartstudio.fakegps",
            "com.incorporateapps.fakegps_route",
            "ru.gavrikov.mocklocations",
            "com.fakelocator.fakegpslocationspoofer",
            "com.lexa.fakegpsdonate",
            "com.location.changer.fake.gps.spoof.emulator",
            "com.fly.gps",
            "com.marlon.floating.fake.location",
            "com.imyfone.anytoandroid",
            "com.evezzon.fakegps",
            "com.g_c.fakesnapmap",
            "com.appanchor.fakegps",
            "com.wind.gpsfaker",
            "app.steve.fakeroute",
            "fr.dvilleneuve.lockito",
            "com.theappninjas.fakegpsjoystick",
            "com.discipleskies.mock_location_spoofer",
            "fake.gps.location",
            "com.rasfar.mock.location",
            "com.foxbytecode.gpslocker",
            "com.mock.cartage",
            "project.listick.fakegps",
            "com.pengyou.cloneapp",
            "com.cloneapp.parallelspace.dualspace",
            "com.lbe.parallel.intl",
            "com.lbe.parallel.intl.arm64",
            "com.parallel.space.lite",
            "com.parallel.space.lite.arm64",
            "com.parallel.space.pro",
            "com.lbe.parallel.intl.arm32",
            "com.parallel.space.pro.arm32",
            "com.excelliance.multiaccount",
            "com.excelliance.multiaccounts",
            "com.ludashi.dualspace",
            "multi.parallel.dualspace.cloner",
            "com.excean.parallelspace",
            "com.nams.and.wkabd",
            "com.cloneapp.parallelspace.dualspace.arm32",
            "com.trendmicro.tmas",
            "com.heyy.messenger.launch",
            "tap.parallelspace.dualspace.clonewhatsapp.twowhatsapp",
            "com.dualapp.dualspace.cloneapp",
            "com.app.hider.master.dual.app",
            "com.cloneweb.app",
            "mochat.multiple.parallel.whatsclone"
        )
        for (suApp in suspiciousPackages) {
            if (isPackageInstalled(suApp, context)) {
                return true
            }
        }
        return false


    }

    private fun hasSU(): Boolean {
        return findBinary("su") || executeCommand(
            arrayOf(
                "/system/xbin/which",
                "su"
            )
        ) || executeCommand(arrayOf("which", "su"))
    }


    private  fun isPackageInstalled(packagename: String, context: Context): Boolean {
        val pm = context.packageManager
        return try {
            pm.getPackageInfo(packagename, PackageManager.GET_ACTIVITIES)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }

    }

    private fun findBinary(binaryName: String): Boolean {
        val places = arrayOf(
            "/sbin/",
            "/system/bin/",
            "/system/xbin/",
            "/data/local/xbin/",
            "/data/local/bin/",
            "/system/sd/xbin/",
            "/system/bin/failsafe/",
            "/data/local/",
            "/system/xbin/busybox"


        )
        for (where in places) {
            if (File(where + binaryName).exists()) {
                return true
            }
        }
        return false
    }


    private  fun checkMemory(): Boolean {
        var returnValue = false

        // get Process ID of the running app
        val pid = android.os.Process.myPid()

        // check if Frida is running as gadget as part of the app
        var process :  Process? = null
        try {
            process = Runtime.getRuntime().exec("cat /proc/$pid/self/maps")
            val reader =
                BufferedReader(InputStreamReader(process.inputStream))
            var read: Int
            val buffer = CharArray(4096)
            val output = StringBuffer()
            while (reader.read(buffer).also { read = it } > 0) {
                output.append(buffer, 0, read)
            }
            reader.close()

            // Waits for the command to finish.
            process.waitFor()
            Log.d("fridamemory PID: ", pid.toString())
            Log.d("fridamemory: ", output.toString())
            if (output.toString().contains("frida") || (pid == 27047)) {
                Log.d("fridaserver", "Frida Server process found!")
                returnValue = true
            }
        } catch (e: IOException) {
        } catch (e: InterruptedException) {
        } finally {
            process?.destroy()
        }


        return returnValue
    }


    private fun executeCommand(command: Array<String>): Boolean {
        var localProcess: Process? = null
        var `in`: BufferedReader? = null
        try {
            localProcess = Runtime.getRuntime().exec(command)
            `in` = BufferedReader(InputStreamReader(localProcess!!.inputStream))
            return `in`.readLine() != null
        } catch (e: Exception) {
            return false
        } finally {
            localProcess?.destroy()
            if (`in` != null) {
                try {
                    `in`.close()
                } catch (e: IOException) {
                }

            }
        }
    }
}

typealias PlayIntegrityCallback = (isValid: Boolean, payload: JSONObject?, rawToken: String?, error: Exception?) -> Unit
