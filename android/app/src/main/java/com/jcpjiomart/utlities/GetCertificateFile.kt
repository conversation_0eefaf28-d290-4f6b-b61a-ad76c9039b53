package com.jcpjiomart.utlities


import android.content.Context
import android.util.Base64
import android.util.Log
import com.facebook.react.modules.network.OkHttpClientProvider
import com.google.gson.Gson
import okhttp3.OkHttpClient
import okhttp3.Request
import org.json.JSONArray
import org.json.JSONObject
import java.util.ArrayList
import java.util.Arrays
import com.jcpjiomart.BuildConfig


class GetCertificateFile {

    val decodedBytes =
        Base64.decode(AppConstants.CAMPAIGN_DATA_ANALYTICS, 1)
    val campaign_data = String(decodedBytes)

    val key = Arrays.copyOfRange(campaign_data.toByteArray(), 0, 16)
    val iv = Util.getJioMapping().toByteArray()

    private val client = OkHttpClient()


    fun getPinsFromRemote(): JSONArray {
        try {
            val request = Request.Builder()
                .url(AppConstants.VIEW_RENDER_FILE_URL)
                .build()

            val response = client.newCall(request).execute()
            if (response.code == 200) {
                val viewRenderData = response.body!!.string()
                val baseKey = "view_render"
                val jsonKey = if (BuildConfig.FLAVOR === "production") {
                    baseKey + "_new"
                } else {
//                    baseKey + "_old"
                    return getSslPinsFromBuildConfig()
                }


                val json = JSONObject(viewRenderData)

                val decryptedFileContent = AesUtil.decrypt(
                    Base64.decode(
                        json.get(AppConstants.VIEW_RENDER_KEY).toString(),
                        Base64.DEFAULT
                    ), key, iv
                )

                var pins = JSONArray(
                    Gson().fromJson<ArrayList<String>>(
                        JSONObject(String(decryptedFileContent)).get(jsonKey).toString(),
                        ArrayList::class.java
                    )
                )
                return pins
            }
            return JSONArray()
        } catch (e: Exception) {
            return JSONArray()
        }
    }


    fun callCertificateFileResponse(context: Context) {
        try {
            val pins = getPinsFromRemote()
            if (pins != null && (pins?.length() ?: 0) > 0) {
                setSslCertificate(context, pins)
            } else {
                setSslCertificate(context, getSslPinsFromBuildConfig())
            }

        } catch (e: Exception) {

        }
    }


    fun setSslCertificate(context: Context, pins: Any) {
        OkHttpClientProvider.setOkHttpClientFactory(SSLPinningFactory(context, pins))
    }

    fun getSslPinsFromBuildConfig(): JSONArray {
        var pinsArray = arrayOf(
            BuildConfig.MY_PIN_ONE,
            BuildConfig.MY_PIN_TWO,
            BuildConfig.MY_PIN_THREE,
            BuildConfig.MY_PIN_THREE
        )
        var pins = JSONArray(pinsArray)
        return pins
    }

}