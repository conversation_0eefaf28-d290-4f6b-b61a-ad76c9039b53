package com.jcpjiomart.utlities

import android.content.Context
import android.util.Log
import com.facebook.react.modules.network.OkHttpClientFactory
import com.facebook.react.modules.network.OkHttpClientProvider
import com.jcpjiomart.BuildConfig
import okhttp3.CertificatePinner
import okhttp3.OkHttpClient
import org.json.JSONArray


class SSLPinningFactory(var context: Context, pins: Any) : OkHttpClientFactory {
    var pins = pins.toString()
    override fun createNewNetworkModuleClient(): OkHttpClient {
        val certificatePinnerBuilder = CertificatePinner.Builder()

        val pinsArray = JSONArray(pins)
        for (i in 0 until pinsArray.length()) {
            val pin = pinsArray.get(i)
            certificatePinnerBuilder.add(BuildConfig.MY_PIN_SERVER_URL, pin.toString())
        }


        val certificatePinner = certificatePinnerBuilder.build()
        val clientBuilder = OkHttpClientProvider.createClientBuilder()
        return clientBuilder.certificatePinner(certificatePinner).build()
    }
}


