package com.jcpjiomart

import com.clevertap.android.sdk.CleverTapAPI
import com.clevertap.react.CleverTapApplication
import com.facebook.react.PackageList
import com.facebook.react.ReactApplication
import com.facebook.react.ReactHost
import com.facebook.react.ReactNativeHost
import com.facebook.react.ReactPackage
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.load
import com.facebook.react.defaults.DefaultReactHost.getDefaultReactHost
import com.facebook.react.defaults.DefaultReactNativeHost
import com.facebook.react.soloader.OpenSourceMergedSoMapping
import com.facebook.soloader.SoLoader
import com.jcpjiomart.jioAds.JioAdViewPackage
import com.jcpjiomart.react_packages.AddressPackage
import com.jcpjiomart.react_packages.JMBridgeEventEmitterPackage
import com.jcpjiomart.react_packages.JMNativeCachePackage
import com.jio.codepush.BundleUpdater
import com.jio.jioads.adinterfaces.JioAds
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import com.facebook.FacebookSdk
import com.facebook.appevents.AppEventsLogger
import com.facebook.LoggingBehavior
import android.util.Log
import com.jcpjiomart.react_packages.JMPaymentServiceProviderPackage
import com.jcpjiomart.utlities.GetCertificateFile
import kotlinx.coroutines.CoroutineScope

class MainApplication : CleverTapApplication(), ReactApplication {

  override val reactNativeHost: ReactNativeHost =
      object : DefaultReactNativeHost(this) {
        override fun getPackages(): List<ReactPackage> =
            PackageList(this).packages.apply {
              // Packages that cannot be autolinked yet can be added manually here, for example:
              add(AddressPackage())
              add(JioAdViewPackage())
              add(JMBridgeEventEmitterPackage())
              add(JMNativeCachePackage())
              add(JMPaymentServiceProviderPackage())
            }

        override fun getJSMainModuleName(): String = "index"

        override fun getUseDeveloperSupport(): Boolean = BuildConfig.DEBUG

        override val isNewArchEnabled: Boolean = BuildConfig.IS_NEW_ARCHITECTURE_ENABLED
        override val isHermesEnabled: Boolean = BuildConfig.IS_HERMES_ENABLED

        override fun getJSBundleFile(): String {
          return BundleUpdater.getBundleFilePath()
        }
      }

  override val reactHost: ReactHost
    get() = getDefaultReactHost(applicationContext, reactNativeHost)

  override fun onCreate() {
    super.onCreate()

    if (com.facebook.FacebookSdk.isInitialized()) {
        Log.d("Facebook", "Facebook SDK is initialized")
    } else {
        Log.d("Facebook", "Facebook SDK is NOT initialized")
    }
    CleverTapAPI.setDebugLevel(CleverTapAPI.LogLevel.VERBOSE);
    SoLoader.init(this, OpenSourceMergedSoMapping)
    if (BuildConfig.IS_NEW_ARCHITECTURE_ENABLED) {
      // If you opted-in for the New Architecture, we load the native entry point for this app.
      load()
    }
    CoroutineScope(Dispatchers.IO).launch {
      JioAds.getInstance().setEnvironment(JioAds.Environment.PROD)
//      JioAds.getInstance().setLogLevel(JioAds.LogLevel.DEBUG)
      JioAds.getInstance().init(applicationContext)
      GetCertificateFile().callCertificateFileResponse(
        applicationContext
      )
    }
    BundleUpdater.initialize(
      context = this,
      packageName = BuildConfig.APPLICATION_ID,
      environment = Utility.getCurrentEnv(),
      appVersion = BuildConfig.VERSION_CODE.toString()
    )
  }
}
