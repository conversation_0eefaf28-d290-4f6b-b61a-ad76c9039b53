package com.jcpjiomart.react_packages

import android.location.Address
import android.location.Geocoder
import android.util.Log
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.google.gson.Gson
import java.util.Locale
import com.google.android.gms.maps.model.LatLng
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


class AddressModule internal constructor(private var reactContext: ReactApplicationContext) :
    ReactContextBaseJavaModule(
        reactContext
    ) {
    val TAG:String = this.name

    val geoCoder = Geocoder(reactContext, Locale("en", "IN"))

    val allowedForAddressRegex = Regex("^[a-zA-Z0-9_!@#\$%^&*()\\-+=|\\\\;:'\",.<>?/\\[\\]{} ]+$")

    @ReactMethod
    fun getReverseGeoCodeFromLatLong(latitude: Double, longitude: Double, promise: Promise) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val addressList = geoCoder.getFromLocation(latitude, longitude, 1)

                if (!addressList.isNullOrEmpty()) {
                    addressList.forEach {
                        it.latitude = latitude
                        it.longitude = longitude
                    }

                    val addressJson = Gson().toJson(getAddressFromGeocoderResponse(addressList))
                    withContext(Dispatchers.Main) {
                        promise.resolve(addressJson)
                    }
                } else {
                    withContext(Dispatchers.Main) {
                        promise.reject("NO_ADDRESS", "No address found")
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    promise.reject("REVERSE_GEOCODE_FAILED", e.localizedMessage ?: "Unknown error")
                }
            }
        }
    }

    private fun isAddressTextInValid(text: String): Boolean {
        Log.d("","addressResponse isAddressTextInValid--"+text+"||||"+text.matches(Regex("^[a-zA-Z][a-zA-Z, \\s]*")).not())
        return text.matches(Regex("^[a-zA-Z][a-zA-Z, \\s]*")).not()
    }

    private fun getRawAddressString(address: Address): String{
        val sb = StringBuilder()
        address.premises?.let { sb.append(it).append(", ") }
        address.subAdminArea?.let { sb.append(it).append("\n") }
        address.locality?.let { sb.append(it).append(", ") }
        address.adminArea?.let { sb.append(it).append(", ") }
        address.countryName?.let { sb.append(it).append(", ") }
        address.postalCode?.let { sb.append(it) }

        Log.d("","addressResponse getRawAddressString--"+sb)
        return sb.toString()
    }
    private fun dontContainsNonEnglishChar(address: Address): Boolean{
/*
        Console.debug("addressResponse","dontContainsHindiChar---"+address+"||"+address.getAddressLine(address.maxAddressLineIndex))
        if(allowedRegex.matches(address.getAddressLine(address.maxAddressLineIndex))){
            Console.debug("addressResponse","dontContainsHindiChar---inside if")
        }
        else{
            Console.debug("addressResponse","dontContainsHindiChar---inside else")
        }*/
        return allowedForAddressRegex.matches(address.getAddressLine(address.maxAddressLineIndex))
    }
    fun getAddressFromGeocoderResponse(
        addressResponse: List<Address>?
    ): com.jcpjiomart.models.Address? {
        return try {
            Log.d("addressResponse", "addressResponse--$addressResponse")

            val address = addressResponse?.firstOrNull {
                it.postalCode.isNullOrEmpty().not() && dontContainsNonEnglishChar(it)
            }

            Log.d("addressResponse", "address--$address")

            if (address != null) {
                if (!address.countryName.equals("India", ignoreCase = true)) {
                    return null
                }

                val sb = StringBuilder()
                if (address.maxAddressLineIndex >= 0) {
                    for (i in 0..address.maxAddressLineIndex) {
                        address.getAddressLine(i)?.let {
                            sb.append(it)
                            if (i != address.maxAddressLineIndex) sb.append("\n")
                        }
                    }
                } else {
                    address.premises?.trim()?.let { sb.append(it).append(", ") }
                    address.subAdminArea?.trim()?.let { sb.append(it).append("\n") }
                    address.locality?.trim()?.let { sb.append(it).append(", ") }
                    address.adminArea?.trim()?.let { sb.append(it).append(", ") }
                    address.countryName?.trim()?.let { sb.append(it).append(", ") }
                    address.postalCode?.trim()?.let { sb.append(it) }
                }

                val addressComponents = removePlaceIdFromAddress(sb.toString()).split(",")
                val subStringIndex = if(addressComponents.size <= 3) 1 else if(addressComponents.size > 5) 3 else 2
                val addressText =
                    addressComponents.subList(0, addressComponents.size - subStringIndex).joinToString(",")
                val city = address.locality ?: address.subAdminArea ?: ""
                val state = address.adminArea ?: ""
                val latLng = LatLng(address.latitude, address.longitude)

                return com.jcpjiomart.models.Address().apply {
                    this.coordinate = latLng
                    this.city = city
                    this.state = state
                    this.pin = address.postalCode
                    this.country = address.countryName
                    this.address = addressText.trim()
                    this.formattedAddress = removePlaceIdFromAddress(sb.toString().trim())
//                    this.area = address.subLocality
                    this.lat = address.latitude
                    this.lon = address.longitude
                }
            }

            null
        } catch (e: Exception) {
            Log.e("getAddressFromGeocoderResponse", "Error: ", e)
            null
        }
    }


    private fun removePlaceIdFromAddress(addressString: String) : String{
        val addressArray = addressString.split(",")
        val newaddressArray = arrayListOf<String>()

        val match = addressArray.filter { it.contains("+", ignoreCase = true) /*&& it.first().isUpperCase()  && it.last().isUpperCase()*/ }

        if(match.isNullOrEmpty().not())
            addressArray.filterTo(newaddressArray, { it != match[0] })

        val match1 = addressArray.filter { it.contains("\"", ignoreCase = true) /*&& it.first().isUpperCase()  && it.last().isUpperCase()*/ }

        if(match1.isNullOrEmpty().not())
            addressArray.filterTo(newaddressArray, { it != match1[0] })

        return if(newaddressArray.isNullOrEmpty().not())
            newaddressArray.joinToString(separator = ",")
        else
            addressString
    }


    @ReactMethod
    fun changeWindowBackground(){
        CoroutineScope(Dispatchers.Main).launch {
            reactContext.currentActivity?.window?.setBackgroundDrawableResource(android.R.color.white)
        }
    }

    override fun getName(): String {
        return "JMRNAddressModule"
    }
}