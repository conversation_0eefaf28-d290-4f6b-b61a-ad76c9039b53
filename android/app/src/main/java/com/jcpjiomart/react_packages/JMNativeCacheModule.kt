package com.jcpjiomart.react_packages

import android.content.Context
import android.content.SharedPreferences
import android.util.Base64
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod


class JMNativeCacheModule internal constructor(private var reactContext: ReactApplicationContext) :

    ReactContextBaseJavaModule(
        reactContext
    ) {


    companion object {
        private const val SHARED_PREF = "overlay"
    }


    private val sharedPreferences: SharedPreferences =
        reactContext.getSharedPreferences(SHARED_PREF, Context.MODE_PRIVATE)


    @ReactMethod
    fun getString(key: String, defaultValue: String?, promise: Promise) {
        try {
            val value = sharedPreferences.getString(key, defaultValue)
            promise.resolve(value)
        } catch (e: Exception) {
            promise.reject("GET_STRING_ERROR", "Failed to get string value: ${e.message}")
        }
    }


    @ReactMethod
    fun getBoolean(key: String, defaultValue: Boolean, promise: Promise) {
        try {
            val value = sharedPreferences.getBoolean(key, defaultValue)
            promise.resolve(value)
        } catch (e: Exception) {
            promise.reject("GET_BOOLEAN_ERROR", "Failed to get boolean value: ${e.message}")
        }
    }


    @ReactMethod
    fun getDecryptedString(key: String, defaultValue: String?, promise: Promise) {
        try {
            val value = sharedPreferences.getString(key, defaultValue)
            promise.resolve(decrypt(value))
        } catch (e: Exception) {
            promise.reject("GET_STRING_ERROR", "Failed to get string value: ${e.message}")
        }
    }


    @ReactMethod
    fun clearAll(promise: Promise) {
        try {
            val editor = sharedPreferences.edit()
            editor.clear()
            editor.apply()
            promise.resolve(true)
        } catch (e: Exception) {
            promise.reject("CLEAR_ALL_ERROR", "Failed to clear all preferences: ${e.message}")
        }
    }


    fun encrypt(value: String?): String {
        if (!isEmptyString(value)) {
            try {
                return String(Base64.encode(value!!.toByteArray(), Base64.DEFAULT))
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        return ""
    }

    fun decrypt(value: String?): String {
        if (!isEmptyString(value)) {
            try {
                return String(Base64.decode(value!!.toByteArray(), Base64.DEFAULT))
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        return ""
    }

    fun isEmptyString(str: String?): Boolean {
        var str = str ?: return true
        str = str.trim { it <= ' ' }
        return str.isEmpty() || str.equals("", ignoreCase = true) || str.equals(
            "null",
            ignoreCase = true
        ) || str.equals(" ", ignoreCase = true)
    }


    override fun getName(): String {
        return "JMNativeCacheModule"
    }
}