package com.jcpjiomart.react_packages

import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.module.annotations.ReactModule
import com.facebook.react.modules.core.DeviceEventManagerModule
import com.google.gson.Gson
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.json.JSONObject

@ReactModule(name = "JMBridgeEventEmitter")
class JMBridgeEventEmitterModule(private val reactContext: ReactApplicationContext) :
    ReactContextBaseJavaModule(reactContext) {
    init {
        setInstance(this)
    }

    companion object {
        private var instance: JMBridgeEventEmitterModule? = null

        fun getInstance(): JMBridgeEventEmitterModule? {
            return instance
        }

        fun setInstance(module: JMBridgeEventEmitterModule) {
            instance = module
        }
    }


    private val jsBridgeEventName = "onDataReceived"
    private fun sendEvent(reactContext: ReactContext, eventName: String, jsonResponse: Any) {
        val responseFormat = JSONObject()
        responseFormat.put("enumValue", eventName)
        responseFormat.put("param", jsonResponse)
        val params = Arguments.createMap().apply {
            putString("data", Gson().toJson(responseFormat))
        }

        CoroutineScope(Dispatchers.Main).launch {
            reactContext
                .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
                .emit(jsBridgeEventName, params)
        }
    }

    override fun getName(): String {
        return "JMBridgeEventEmitter"
    }

    fun deeplink(mUri: String, payload: String) {
        val jsonObj = JSONObject()
        jsonObj.put("mUri",mUri)
        jsonObj.put("payload",payload)
        sendEvent(reactContext, JMReactNativeEvents.Deeplink.value, jsonObj.toString())
    }
}

enum class JMReactNativeEvents(val value: String) {
    Deeplink("deeplink"),
}