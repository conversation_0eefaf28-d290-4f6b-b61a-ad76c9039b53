package com.jcpjiomart.react_packages


import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.drawable.Drawable
import android.net.Uri
import android.util.Base64
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import org.json.JSONArray
import org.json.JSONObject
import java.io.ByteArrayOutputStream


class JMPaymentServiceProviderModule internal constructor(private var reactContext: ReactApplicationContext) :
    ReactContextBaseJavaModule(
        reactContext
    ) {

    companion object {
        const val PSP_UPI_INTENT_REQUEST_CODE = 2210
    }

    @ReactMethod
    fun getPspAppList(promise: Promise) {
        if (reactContext.currentActivity != null) {
            val pspAppsList = JSONArray()
            try {

                val upiUrl = "upi://pay?"
                val intent = Intent("android.intent.action.VIEW")
                intent.data = Uri.parse(upiUrl)
                val applicationContext = reactContext.applicationContext
                val resInfo = applicationContext.packageManager.queryIntentActivities(intent, 0)

                for (info in resInfo) {
                    val pspApp = JSONObject()
                    val drawable = info.loadIcon(applicationContext.packageManager)
                    pspApp.put(
                        "appName",
                        info.loadLabel(applicationContext.packageManager).toString()
                    )
                    pspApp.put(
                        "image",
                        convertDrawableToBitmap(drawable)
                    )
                    pspApp.put("packageName", info.activityInfo.packageName)
                    if (info.activityInfo.packageName != applicationContext.packageName) {
                        pspAppsList.put(pspApp)
                    }
                }
                promise.resolve(pspAppsList.toString())
            } catch (e: Exception) {

            }
        }
    }


    @ReactMethod
    fun openPspUpiApp(upiUrl: String, promise: Promise) {
        if (reactContext.currentActivity != null) {
            try {
                val path = upiUrl.split("\\|".toRegex())
                val pkgName = path[0]
                val upiUri = path[1]
                val localIntent = Intent()
                localIntent.`package` = pkgName
                localIntent.action = "android.intent.action.VIEW"
                localIntent.flags = 0
                localIntent.data = Uri.parse(upiUri)
                reactContext.currentActivity!!.startActivityForResult(
                    localIntent,
                    PSP_UPI_INTENT_REQUEST_CODE
                )
            } catch (e: java.lang.Exception) {

            }
        }
    }


    fun convertDrawableToBitmap(drawable: Drawable): String {
        try {
            var width = drawable.intrinsicWidth
            var height = 1
            width = if (width > 0) width else 1
            val height2 = drawable.intrinsicHeight
            if (height2 > 0) {
                height = height2
            }
            val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
            val canvas = Canvas(bitmap)
            drawable.setBounds(0, 0, canvas.width, canvas.height)
            drawable.draw(canvas)
            val byteArrayOS = ByteArrayOutputStream()
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, byteArrayOS)
            return Base64.encodeToString(byteArrayOS.toByteArray(), 0)
        } catch (e: Exception) {
            return ""
        }
    }


    override fun getName(): String {
        return "JMPaymentServiceProviderModule"
    }
}