<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">


    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.VIBRATE"/>
    <!-- Android 13+ -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <!-- For backward compatibility (Android 12 and below) -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_SMS" />
    
    <application
      android:name=".MainApplication"
      android:label="@string/app_name"
      android:icon="@mipmap/ic_launcher"
      android:roundIcon="@mipmap/ic_launcher_round"
      android:allowBackup="false"
      android:theme="@style/AppTheme"
      tools:replace="android:label,android:icon,android:name,android:theme,android:allowBackup"
      android:supportsRtl="true">
      <meta-data
        android:name="com.google.android.geo.API_KEY"
        android:value="@string/google_maps_key"/>
        <meta-data
            android:name="CLEVERTAP_ACCOUNT_ID"
            android:value="${cleverTapAccountId}" />
        <meta-data
            android:name="CLEVERTAP_TOKEN"
            android:value="${cleverTapToken}"/>
        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="***************" />
        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="5e51568f53c5b099d85eebe17b96895b"/>
      <activity
        android:name=".MainActivity"
        android:label="@string/app_name"
        android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
        android:launchMode="singleTask"
        android:windowSoftInputMode="adjustPan|adjustResize"
        android:exported="true">
        <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter>

          <intent-filter android:autoVerify="true">
              <action android:name="android.intent.action.VIEW" />

              <category android:name="android.intent.category.DEFAULT" />
              <category android:name="android.intent.category.BROWSABLE" />

              <data
                  android:host="com.jpl.jiomart"
                  android:scheme="jiomart" />
          </intent-filter>

          <intent-filter android:autoVerify="true">
              <action android:name="android.intent.action.VIEW" />

              <category android:name="android.intent.category.DEFAULT" />
              <category android:name="android.intent.category.BROWSABLE" />

              <data
                  android:host="jiomart-sit.jio.com"
                  android:scheme="http" />
          </intent-filter>

          <intent-filter android:autoVerify="true">
              <action android:name="android.intent.action.VIEW" />

              <category android:name="android.intent.category.DEFAULT" />
              <category android:name="android.intent.category.BROWSABLE" />

              <data
                  android:host="jiomart-sit.jio.com"
                  android:scheme="https" />
          </intent-filter>
          <intent-filter android:autoVerify="true">
              <action android:name="android.intent.action.VIEW" />

              <category android:name="android.intent.category.DEFAULT" />
              <category android:name="android.intent.category.BROWSABLE" />
              <data android:scheme="https"  android:host="jiomart.onelink.me" />
          </intent-filter>

          <intent-filter android:autoVerify="true">
              <action android:name="android.intent.action.VIEW" />

              <category android:name="android.intent.category.DEFAULT" />
              <category android:name="android.intent.category.BROWSABLE" />
              <data android:scheme="http"  android:host="jiomart.onelink.me" />
          </intent-filter>
      </activity>

        <meta-data
            android:name="com.google.mlkit.vision.DEPENDENCIES"
            android:value="barcode" />

    </application>
</manifest>
