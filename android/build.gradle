buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 34
        ndkVersion = "26.1.10909125"
        kotlinVersion = "1.9.25"
    }
    ext.kotlin_version = '1.9.25'
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.5.1")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.25")
        classpath 'com.google.gms:google-services:4.4.2'

    }
}
allprojects {
    repositories {
        jcenter()
        google()
        mavenCentral()
        maven { url "https://plugins.gradle.org/m2/" }
        maven {url = uri("https://storage.googleapis.com/r8-releases/raw")}
        maven { url "https://jitpack.io" } // For libraries not available on Maven Central.
        maven { url "https://oss.jfrog.org/artifactory/oss-snapshot-local" } // Additional repository for snapshots.
        gradle.projectsEvaluated {
            tasks.withType(JavaCompile) {
                options.compilerArgs << "-Xlint:unchecked"
            }
        }
    }
    def REACT_NATIVE_VERSION = new File(['node', '--print',"JSON.parse(require('fs').readFileSync(require.resolve('react-native/package.json'), 'utf-8')).version"].execute(null, rootDir).text.trim())
    configurations.all {
        resolutionStrategy {
            force "com.facebook.react:react-native:" + REACT_NATIVE_VERSION
            force 'com.google.android.gms:play-services-location:21.0.1'
        }
    }
}
project.ext {
    set('react-native', [
            versions: [
                    // Overriding Build/Android SDK Versions
                    android : [
                            minSdk    : 24, // 23+ if using auth module
                            targetSdk : 34,
                            compileSdk: 35
                    ],
                    play     : [
                            "play-services-auth": "20.7.0"
                    ],
            ],
    ])
}
subprojects {
    tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
        kotlinOptions {
            // Treat all Kotlin warnings as errors (disabled by default)
            allWarningsAsErrors = project.hasProperty("warningsAsErrors") ? project.warningsAsErrors : false
            freeCompilerArgs += '-Xopt-in=kotlin.RequiresOptIn'
            // Enable experimental coroutines APIs, including Flow
            freeCompilerArgs += '-Xopt-in=kotlinx.coroutines.ExperimentalCoroutinesApi'
            freeCompilerArgs += '-Xopt-in=kotlinx.coroutines.FlowPreview'
            freeCompilerArgs += '-Xopt-in=kotlin.Experimental'
        }
    }

    afterEvaluate {
        def androidAction = { plugin ->
            project.extensions.getByType(com.android.build.api.variant.AndroidComponentsExtension.class)
                    .finalizeDsl { ext ->
                        ext.compileOptions.sourceCompatibility = JavaVersion.VERSION_19
                        ext.compileOptions.targetCompatibility = JavaVersion.VERSION_19
                        ext.composeOptions.kotlinCompilerExtensionVersion = "1.5.15"            }
        }

        pluginManager.withPlugin("com.android.application", androidAction)
        pluginManager.withPlugin("com.android.library", androidAction)
        pluginManager.withPlugin("org.jetbrains.kotlin.android") {
            kotlin {
                jvmToolchain(19)
            }
            tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
                kotlinOptions {
                    jvmTarget = 19
                }
            }
        }
        println("Java toolchains configured to use Java 19")
    }
}

apply plugin: "com.facebook.react.rootproject"
