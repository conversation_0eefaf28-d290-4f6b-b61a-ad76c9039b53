import { Linking } from 'react-native';

export function addParams(url?: string, extraParam?: string) {
  if (!url || !extraParam) {
    return url;
  }
  if (!url.includes(extraParam)) {
    url += url.includes('?') ? `&${extraParam}` : `?${extraParam}`;
  }
  return url;
}

export function appendToken(url?: string, token?: string) {
  return "";
}

export function openAnotherApp(uri: string) {
  try {
    const [pkgName, upiUri] = uri.split('|');
    if (!pkgName || !upiUri) {
      throw new Error('Invalid UPI URL format');
    }

    const url = `intent://#Intent;scheme=${upiUri};package=${pkgName};end;`;

    Linking.canOpenURL(url)
      .then(supported => {
        if (supported) {
          return Linking.openURL(url);
        } else {
          throw new Error('Unable to open UPI app');
        }
      })
      .catch(err => {
        console.error('Error occurred while opening UPI app', err);
      });
  } catch (e) {
    console.error('Exception occurred', e);
  }
}
