import { Action, ThunkAction, configureStore } from '@reduxjs/toolkit';
import webSlice from './webSlice';


const webstore = configureStore({
  reducer: {
    web : webSlice,
  },
});

export default webstore;
export type WebStoreState = ReturnType<typeof webstore.getState>
export type AppThunk = ThunkAction<void, WebStoreState, null, Action<string>>;
export type AppDispatch = typeof webstore.dispatch
