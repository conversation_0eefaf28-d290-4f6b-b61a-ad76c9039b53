import {useSelector, useDispatch} from 'react-redux';
import {WebCallBack, setWebCallback} from './webSlice';
import { WebStoreState } from './webstore';

const useWebViewModel = () => {
  const web = useSelector((state: WebStoreState) => state.web);
  const dispatch = useDispatch();

  return {
    webReduxState: web,
    setWebViewCallback: (callback: WebCallBack | null) =>
      dispatch(setWebCallback(callback)),
  };
};

export default useWebViewModel;
