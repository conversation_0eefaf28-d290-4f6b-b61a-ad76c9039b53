import {
  isNullOrUndefinedOrEmpty,
  NullableObject,
  NullableString,
} from '../../../jiomart-common/src/JMObjectUtility';
import {NavigationBean} from '../../../jiomart-common/src/JMNavGraphUtil';
import {
  extractBaseURLFromURL,
  getAllBaseURLWithSameAppSource,
  getBaseURL,
  isKnownBaseURL,
  stripBaseURL,
} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import JMWebGlobalInfo from '../WebGlobalInfo';
import JMFirebaseABTestUtility from '../../../jiomart-general/src/utils/JMFirebaseABTestUtility';

export const extractQueryIntoParams = (
  bundle: NullableString,
): NullableObject => {
  if (isNullOrUndefinedOrEmpty(bundle)) {
    return null;
  }
  let params: Record<string, any> = {};
  bundle!.split('&').forEach(str => {
    const indexOfEqual = str.indexOf('=');
    const firstPart = str.substring(0, indexOfEqual);
    const secondPart = str.substring(indexOfEqual + 1);
    params[firstPart] = secondPart;
  });
  console.log({params});
  return params;
};

export function addQueryParamsToURL(bean: NavigationBean) {
  let baseDomain: any = getBaseURL();
  const allBaseURL = getAllBaseURLWithSameAppSource();
  let webURL = bean?.actionUrl;

  if (webURL?.startsWith('https://') || webURL?.startsWith('http://')) {
    if (isKnownBaseURL(allBaseURL, webURL)) {
      const remaningPartExceptBaseUrl = stripBaseURL(
        extractBaseURLFromURL(allBaseURL, webURL),
        webURL,
      );
      webURL = baseDomain + remaningPartExceptBaseUrl;
    }
  } else {
    webURL = baseDomain + '/' + webURL;
  }
  
  if (!isNullOrUndefinedOrEmpty(bean?.deeplinkIdentifier)) {
    if (!isNullOrUndefinedOrEmpty(bean?.deeplinkParam)) {
      webURL = webURL + "/" + bean?.deeplinkParam;
    }
  }
  if (bean?.abTestingparams && bean.abTestingparams.length > 0) {
    if (addABTestingParams(bean)) {
      webURL += (webURL.includes('?') ? '&' : '?') + addABTestingParams(bean);
    }
  }
  const containsParam = webURL?.includes('?') ?? false;
  if (webURL?.startsWith(baseDomain)) {
    return webURL + addQueryParams(containsParam);
  } else if (webURL?.startsWith('https://') || webURL?.startsWith('http://')) {
    if (webURL?.includes('?tab')) {
      return webURL;
    }
    return webURL + addQueryParams(containsParam);
  } else {
    let url =
      baseDomain + webURL?.replace(/^\/+/, '') + addQueryParams(containsParam);
    return url;
  }
}

function addABTestingParams(bean: NavigationBean) {
  const abTestingParams = bean.abTestingparams || [];
  return abTestingParams
    .map(({abTestingKey, urlKey, defaultValue}) => {
      const value = JMFirebaseABTestUtility.getParam(abTestingKey);
      const finalValue = value ?? defaultValue;
      if (!isNullOrUndefinedOrEmpty(finalValue)) {
        return `${urlKey}=${finalValue}`;
      }
      return null;
    })
    .join('&');
}

function addQueryParams(containsParam: boolean) {
  const query = JMWebGlobalInfo.Instance.getQuery();
  return `${containsParam ? '&' : '?'}${query}`;
}

export const getModuleFromUrl = (url: NullableString): NullableString => {
  if (isNullOrUndefinedOrEmpty(url)) {
    return null;
  }
  const com = url!.split('.com');
  return com.length > 1 ? com[1].split('?')[0] : url;
};
