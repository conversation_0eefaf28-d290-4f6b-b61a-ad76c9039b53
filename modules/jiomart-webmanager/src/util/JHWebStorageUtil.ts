
import {JwtTokenData} from '../config/JMWebViewConfig';

import { jsonParse } from '../../../jiomart-common/src/models/JMResponseParser';
import { isNullOrUndefinedOrEmpty, NullableString } from '../../../jiomart-common/src/JMObjectUtility';
import { addStringPref, getPrefString, removeStringPref } from '../../../jiomart-common/src/JMAsyncStorageHelper';
import { Decoder } from '../../../jiomart-networkmanager/src/api/utils/Decoder';

export class JHWebStorageUtil {
  static saveJwtData = async (key: NullableString, data: JwtTokenData) => {
    if (isNullOrUndefinedOrEmpty(key)) {
      return;
    }
    const dataString = JSON.stringify(data);
    console.log('=------ saving jwt token ' + key! + ' : ' + dataString);
    await addStringPref(key!, Decoder.base64EncodeValue(dataString)!);
  };
  static getJwtData = async (
    key: NullableString,
  ): Promise<JwtTokenData | null> => {
    // return null;
    if (isNullOrUndefinedOrEmpty(key)) {
      return null;
    }
    const dataString = Decoder.base64DecodeValue(await getPrefString(key!));
    console.log('=------ fetching jwt token ' + key! + ' : ' + dataString);
    return await jsonParse<JwtTokenData>(dataString);
  };

  static clearJwtData = async (key: NullableString) => {
    if (isNullOrUndefinedOrEmpty(key)) {
      return;
    }
    await removeStringPref(key!);
  };
}
