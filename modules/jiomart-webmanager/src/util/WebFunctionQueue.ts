
export class WebFunctionQueue {
  private static instance: WebFunctionQueue;
  private webJavaScriptFunctionsMap = new Map<string, any>()


  public static getInstance(): WebFunctionQueue {
    if (!WebFunctionQueue.instance) {
      WebFunctionQueue.instance = new WebFunctionQueue();
    }
    return WebFunctionQueue.instance;
  }
  

  public setDataInWebJavaScriptFunctionsMap(functionName: string, params: any) {
    WebFunctionQueue.getInstance().webJavaScriptFunctionsMap.set(functionName, params)
    console.log('Map entries set:', Array.from(WebFunctionQueue.getInstance().webJavaScriptFunctionsMap.entries()));
  }

  public getDataInWebJavaScriptFunctionsMap() {
    console.log('Map entries get:', Array.from(WebFunctionQueue.getInstance().webJavaScriptFunctionsMap.entries()));
    return WebFunctionQueue.getInstance().webJavaScriptFunctionsMap
  }

  public removeDataInWebJavaScriptFunctionsMap(functionName: string){
    WebFunctionQueue.getInstance().webJavaScriptFunctionsMap.delete(functionName)
  }

  public removeAllDataInWebJavaScriptFunctionsMap(){
    WebFunctionQueue.getInstance().webJavaScriptFunctionsMap.clear()
  }
}
