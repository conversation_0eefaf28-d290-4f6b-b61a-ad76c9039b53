import {Pressable, View} from 'react-native';
import React from 'react';
import Animated from 'react-native-reanimated';
import {JioIcon, JioText, useColor} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {styles} from '../styles/FilterAndSortStyles';
import Divider, {
  DividerType,
} from '../../../jiomart-general/src/ui/Divider';

export interface FilterAndSortBarProps {
  config?: any;
  text: string;
  onTextPress?: () => void;
  onSortPress?: () => void;
  onFilterPress?: () => void;
  isSortApplied?: boolean;
  appliedFilters?: number;
  showShadow?: boolean;
  hideDivider?: boolean;
}

const FilterAndSortBar = (props: FilterAndSortBarProps) => {
  const {
    config,
    text,
    onTextPress,
    showShadow,
    onSortPress,
    onFilterPress,
    isSortApplied,
    appliedFilters,
    hideDivider,
  } = props;
  const sortIconColor = useColor('secondary_40');

  delete appliedFilters?.slist;

  return (
    <Animated.View style={styles.animateContainer}>
      <View style={[styles.container, showShadow ? styles.shadowBox : null]}>
        {config?.category?.isVisible ? (
          onTextPress ? (
            <Pressable onPress={onTextPress} style={styles.onText}>
              <JioText
                text={text}
                appearance={JioTypography.BODY_XXS_LINK}
                color={config?.category?.text?.color}
                maxLines={config?.category?.text?.maxLines}
              />
              {text && <JioIcon {...config?.category?.icon} />}
            </Pressable>
          ) : (
            <JioText
              text={text}
              appearance={JioTypography.BODY_XXS_LINK}
              color={config?.category?.text?.color}
            />
          )
        ) : null}
        <View style={styles.option}>
          {config?.sort?.isVisible ? (
            <Pressable
              style={styles.button}
              activeOpacity={0.65}
              onPress={onSortPress}>
              <JioIcon {...config?.sort?.icon} />
              <JioText
                text={config?.sort?.text?.title ?? ''}
                appearance={JioTypography.BODY_XXS_LINK}
                color={config?.sort?.text?.color}
              />
              {isSortApplied && (
                <View
                  style={[styles.sortBadge, {backgroundColor: sortIconColor}]}
                />
              )}
            </Pressable>
          ) : null}
          {config?.filter?.isVisible ? (
            <Pressable
              style={styles.button}
              activeOpacity={0.65}
              onPress={onFilterPress}>
              <JioIcon {...config?.filter?.icon} />
              <JioText
                text={config?.filter?.text?.title ?? ''}
                appearance={JioTypography.BODY_XXS_LINK}
                color={config?.filter?.text?.color}
              />
              {appliedFilters && appliedFilters > 0 ? (
                <View
                  style={[
                    styles.badge,
                    {
                      backgroundColor: sortIconColor,
                    },
                  ]}>
                  <JioText
                    text={`${appliedFilters}`}
                    appearance={JioTypography.BODY_XXS}
                    color={'white'}
                    style={{alignSelf: 'center'}}
                  />
                </View>
              ) : null}
            </Pressable>
          ) : null}
        </View>
      </View>
      {!showShadow && hideDivider ? <Divider type={DividerType.THIN} /> : null}
    </Animated.View>
  );
};

export default React.memo(FilterAndSortBar);
