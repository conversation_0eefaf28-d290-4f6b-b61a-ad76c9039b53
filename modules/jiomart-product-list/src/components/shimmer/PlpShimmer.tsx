import React from 'react';
import {
  View,
  StyleSheet,
  useWindowDimensions,
  StyleProp,
  ViewStyle,
  FlatList,
  ScrollView,
} from 'react-native';
import {ShimmerKind} from '@jio/rn_components/src/index.types';
import JMShimmer from '../../../../jiomart-general/src/ui/JMShimmer';
import {rh, rw} from '../../../../jiomart-common/src/JMResponsive';
import FashionItemShimmer from './FashionItemShimmer';

const ProductCardShimmer = React.memo(() => (
  <View style={styles.cardContainer}>
    <JMShimmer
      width={rw(112)}
      kind={ShimmerKind.RECTANGLE}
      style={styles.cardImage}
      height={rw(112)}
    />
    <View style={styles.cardContent}>
      <View style={styles.textContainer}>
        <JMShimmer
          width="100%"
          kind={ShimmerKind.RECTANGLE}
          style={styles.textLine}
          height={rh(16)}
        />
        <JMShimmer
          width="80%"
          kind={ShimmerKind.RECTANGLE}
          height={rh(16)}
          style={[styles.textLine, styles.marginTop4]}
        />
        <JMShimmer
          width="40%"
          kind={ShimmerKind.RECTANGLE}
          height={rh(16)}
          style={[styles.textLine, styles.marginTop4]}
        />
      </View>
      <JMShimmer
        width="30%"
        kind={ShimmerKind.RECTANGLE}
        height={rh(16)}
        style={styles.priceTag}
      />
    </View>
  </View>
));

export const PlpFilterTopShimmer = React.memo(() => (
  <View style={styles.topBar}>
    <JMShimmer
      width={rw(130)}
      height={rh(10)}
      style={styles.roundedShimmer}
      kind={ShimmerKind.RECTANGLE}
    />
    <View style={styles.filterButtons}>
      <JMShimmer
        width={rw(63)}
        height={rh(28)}
        style={styles.roundedShimmer}
        kind={ShimmerKind.RECTANGLE}
      />
      <JMShimmer
        width={rw(83)}
        height={rh(28)}
        style={styles.roundedShimmer}
        kind={ShimmerKind.RECTANGLE}
      />
    </View>
  </View>
));

export const PlpListShimmer = React.memo(
  ({style}: {style?: StyleProp<ViewStyle>}) => {
    return (
      <ScrollView
        style={[styles.container, style]}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        bounces={false}>
        {Array.from({length: 10}).map((_, index) => (
          <ProductCardShimmer key={`card-${index}`} />
        ))}
      </ScrollView>
    );
  },
);

const renderGridItemShimmer = item => {
  return <FashionItemShimmer />;
};

export const PlpGridShimmer = React.memo(
  ({style}: {style?: StyleProp<ViewStyle>}) => {
    const {height} = useWindowDimensions();
    const numCards = Math.ceil((height - rh(281)) / rh(150));

    return (
      <View style={[styles.container, style]}>
        <FlatList
          showsVerticalScrollIndicator={false}
          scrollEnabled={false}
          data={Array.from({length: numCards})}
          renderItem={renderGridItemShimmer}
          numColumns={2}
          columnWrapperStyle={{
            justifyContent: 'space-between',
            marginBottom: rh(32),
          }}
        />
      </View>
    );
  },
);
export const GroceryTopBarShimmer = () => {
  return (
    <View style={{flexDirection: 'row', columnGap: 3}}>
      <View style={styles.topBarStyle1} />
      <View style={styles.topBarStyle2} />
    </View>
  );
};
export const GrocerySponseredAdsShimmer = () => {
  return (
    <View
      style={{
        rowGap: 8,
      }}>
      <View
        style={{
          height: rh(190),
          backgroundColor: '#F5F5F5',
          borderRadius: 24,
        }}
      />
      <View
        style={{
          height: 6,
          width: rw(42),
          backgroundColor: '#F5F5F5',
          alignSelf: 'center',
          borderRadius: 24,
        }}
      />
    </View>
  );
};
export const GroceryItemCardShimmer = () => {
  return (
    <View style={{flex: 1, padding: 2}}>
      <View
        style={{
          width: rw(112),
          height: rw(112),
          borderRadius: 24,
          backgroundColor: '#F5F5F5',
        }}
      />
      <View
        style={{
          height: 12,
          marginTop: 6,
          borderRadius: 12,
          // borderWidth: 1,
          backgroundColor: '#F5F5F5',
        }}
      />
      <View
        style={{
          height: 12,
          marginTop: 4,
          borderRadius: 12,
          backgroundColor: '#F5F5F5',
        }}
      />
      <View
        style={{
          height: 12,
          width: '50%',
          marginTop: 4,
          borderRadius: 12,
          backgroundColor: '#F5F5F5',
        }}
      />
      <View
        style={{
          height: 32,
          width: '50%',
          marginTop: 30,
          marginLeft: 'auto',
          borderRadius: 24,
          backgroundColor: '#F5F5F5',
        }}
      />
      <View
        style={{
          height: 18,
          width: '100%',
          marginTop: 4,
          marginLeft: 'auto',
          borderRadius: 24,
          backgroundColor: '#F5F5F5',
        }}
      />
    </View>
  );
};
export const GroceryGridShimmer = () => {
  return (
    <View
      style={{
        backgroundColor: 'white',
        marginHorizontal: 8,
        borderRadius: 12,
        paddingHorizontal: 8,
        marginTop: 8,
      }}>
      <View style={styles.gridRowStyle}>
        <GroceryItemCardShimmer />
        <GroceryItemCardShimmer />
      </View>
      <View style={styles.gridRowStyle}>
        <GroceryItemCardShimmer />
        <GroceryItemCardShimmer />
      </View>

      <View style={styles.gridRowStyle}>
        <GroceryItemCardShimmer />
        <GroceryItemCardShimmer />
      </View>
    </View>
  );
};
export const GroceryTitleShimmer = () => {
  return <View style={styles.groceryTitleStyle} />;
};
export const GrocerySubCategoryItem = () => {
  return (
    <View
      style={{
        alignItems: 'center',
        rowGap: 8,
      }}>
      <View
        style={{
          height: 48,
          width: 48,
          borderRadius: 100,
          backgroundColor: '#F5F5F5',
        }}
      />
      <View
        style={{
          height: 12,
          width: '100%',
          borderRadius: 12,
          backgroundColor: '#F5F5F5',
        }}
      />
    </View>
  );
};
export const GrocerySubCategoryShimmer = () => {
  const items = Array.from({length: 10}, (_, t) => t);
  return (
    <View
      style={{
        width: rw(70),
      }}>
      <FlatList
        data={items}
        renderItem={({index}) => {
          return <GrocerySubCategoryItem key={index} />;
        }}
        contentContainerStyle={{
          rowGap: 12,
          paddingHorizontal: 8,
          paddingBottom: 24,
        }}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        bounces={false}
        style={{
          flex: 1,
        }}
      />
    </View>
    // <ScrollView
    //   showsHorizontalScrollIndicator={false}
    //   showsVerticalScrollIndicator={false}
    //   bounces={false}
    //   style={{flex: 1, rowGap: 12}}>
    //   {items.map((_, index) => (
    //     <GrocerySubCategoryItem key={index} />
    //   ))}
    // </ScrollView>
  );
};


const styles = StyleSheet.create({
  topBarStyle1: {
    borderWidth: 0,
    width: 116,
    height: 28,
    borderRadius: 24,
    backgroundColor: '#F5F5F5',
  },
  topBarStyle2: {
    borderWidth: 0,
    width: 69,
    height: 28,
    borderRadius: 24,
    backgroundColor: '#F5F5F5',
  },
  groceryTitleStyle: {
    width: rw(153),
    height: rh(20),
    marginTop: 9,
    marginBottom: 7,
    backgroundColor: '#F5F5F5',
    alignSelf: 'center',
    borderRadius: 24,
  },
  gridRowStyle: {
    flexDirection: 'row',
    columnGap: 8,
    marginTop: 18,
    marginRight: 8,
    borderWidth: 0,
  },
  container: {
    flex: 1,
    backgroundColor: 'white',
    paddingHorizontal: rw(16),
  },
  cardContainer: {
    flexDirection: 'row',
    marginBottom: rh(25),
    paddingBottom: rh(12),
  },
  cardImage: {
    borderRadius: rw(24),
  },
  cardContent: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    marginLeft: rw(12),
    flex: 1,
  },
  textContainer: {
    width: '100%',
  },
  textLine: {
    borderRadius: rw(24),
  },
  marginTop4: {
    marginTop: rh(4),
  },
  priceTag: {
    marginBottom: rh(8),
    marginLeft: 'auto',
    borderRadius: rw(24),
  },
  topBar: {
    flexDirection: 'row',
    paddingHorizontal: rw(24),
    paddingVertical: rh(16),
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  filterButtons: {
    flexDirection: 'row',
    gap: rw(8),
  },
  roundedShimmer: {
    borderRadius: rw(24),
  },
});

export default PlpListShimmer;
