import {FlatList, StyleSheet, View} from 'react-native';
import React from 'react';
import {
  GroceryItemCardShimmer,
  GrocerySponseredAdsShimmer,
  GrocerySubCategoryShimmer,
  GroceryTitleShimmer,
  GroceryTopBarShimmer,
} from './PlpShimmer';

const GridProductListingScreenShimmer = () => {
  return (
    <View style={{flex: 1, backgroundColor: '#ffffff'}}>
      <GroceryTitleShimmer />
      <View
        style={{
          flexDirection: 'row',
          columnGap: 8,
          flex: 1,
        }}>
        <GrocerySubCategoryShimmer />
        <FlatList
          data={Array.from({length: 10}, (_, v) => v)}
          renderItem={() => {
            return <GroceryItemCardShimmer />;
          }}
          bounces={false}
          ListHeaderComponent={() => {
            return (
              <>
                <GrocerySponseredAdsShimmer />
                <GroceryTopBarShimmer />
              </>
            );
          }}
          numColumns={2}
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{
            paddingBottom: 24,
            marginRight: 8,
            rowGap: 12,
          }}
          ListHeaderComponentStyle={{
            rowGap: 12,
          }}
        />
      </View>
    </View>
  );
};

export default GridProductListingScreenShimmer;

const styles = StyleSheet.create({});
