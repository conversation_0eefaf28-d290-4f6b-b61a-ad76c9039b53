import {View, StyleSheet, Pressable, ScrollView} from 'react-native';
import React from 'react';
import {JioIcon, JioText, useColor} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import Animated from 'react-native-reanimated';
import {rw} from '../../../jiomart-common/src/JMResponsive';

interface GroceriesTopBarProps {
  openSortBtmSheet: () => void;
  openBrandBtmSheet: () => void;
  openFilterBtmSheet: () => void;
  selSortFilterName: string;
  selBrandName: string;
  readFilterDataUtil: (data: any) => void;
  config?: any;
  appliedFilters?: number;
  appliedBrandFilters?: number;
}

const GroceriesTopBar = ({
  openSortBtmSheet,
  openFilterBtmSheet,
  openBrandBtmSheet,
  selSortFilterName,
  appliedFilters,
  appliedBrandFilters,
  config,
}: GroceriesTopBarProps) => {
  const requiredKeys = ['brand', 'min_price_effective', 'l3_category_names']; // Can have more keys
  const sortIconColor = useColor('secondary_40');
  return (
    <Animated.View
      style={[
        {
          width: '100%',
          paddingVertical: 6,
          marginLeft: 6,
        },
      ]}>
      <ScrollView
        contentContainerStyle={[{columnGap: 4}]}
        horizontal={true}
        showsHorizontalScrollIndicator={false}>
        {config?.sort?.isVisible && (
          <Pressable
            style={[styles.topBar, {columnGap: 3}]}
            onLongPress={() => {}}
            onPress={openSortBtmSheet}>
            <JioIcon {...config?.sort?.leftIcon} />
            {selSortFilterName ? (
              <JioText
                text={config?.sort?.text?.title?.replace(
                  '[TITLE]',
                  selSortFilterName,
                )}
                appearance={JioTypography.BODY_XXS}
                color={config?.sort?.text?.color}
              />
            ) : null}
            <JioIcon {...config?.sort?.icon} />
          </Pressable>
        )}
        {config?.brand?.isVisible && (
          <Pressable
            style={[styles.topBar, {columnGap: 3}]}
            onLongPress={() => {}}
            onPress={openBrandBtmSheet}>
            <JioText
              text={config?.brand?.text?.title ?? ''}
              appearance={JioTypography.BODY_XXS}
              color={config?.brand?.text?.color}
            />
            {appliedBrandFilters && appliedBrandFilters > 0 ? (
              <View
                style={[
                  {
                    alignSelf: 'center',
                    backgroundColor: sortIconColor,
                    paddingHorizontal: rw(4),
                    borderRadius: rw(8),
                  },
                ]}>
                <JioText
                  text={appliedBrandFilters.toString()}
                  appearance={JioTypography.BODY_XXS}
                  color={'white'}
                />
              </View>
            ) : null}
            <JioIcon {...config?.brand?.icon} />
          </Pressable>
        )}
        {config?.filter?.isVisible && (
          <Pressable
            style={[styles.topBar, {columnGap: 3, marginRight: 12}]}
            onPress={openFilterBtmSheet}>
            <JioIcon {...config?.filter?.leftIcon} />
            <JioText
              text={config?.filter?.text?.title}
              appearance={JioTypography.BODY_XXS}
              color={config?.filter?.text?.color}
            />
            {appliedFilters && appliedFilters > 0 ? (
              <View
                style={[
                  {
                    alignSelf: 'center',
                    backgroundColor: sortIconColor,
                    paddingHorizontal: rw(4),
                    borderRadius: rw(8),
                  },
                ]}>
                <JioText
                  text={appliedFilters.toString()}
                  appearance={JioTypography.BODY_XXS}
                  color={'white'}
                />
              </View>
            ) : null}
            <JioIcon {...config?.filter?.icon} />
          </Pressable>
        )}
      </ScrollView>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  topBar: {
    paddingRight: 5,
    borderRadius: 14,
    flexDirection: 'row',
    backgroundColor: 'white',
    alignItems: 'center',
    height: 28,
    marginLeft: 0,
    paddingLeft: 8,
  },
});

export default GroceriesTopBar;
