import React from 'react';
import {View, StyleSheet, Pressable} from 'react-native';
import {Jio<PERSON><PERSON>ider, JioText, useColor} from '@jio/rn_components';
import {SafeAreaView} from 'react-native';
import {JioTypography} from '@jio/rn_components/src/index.types';
import MultiVariantCardView from './MultiVariantCardView';
import JMBtmSheetHeader from '../../../jiomart-general/src/ui/JMBtmSheetHeader';
import {rh} from '../../../jiomart-common/src/JMResponsive';
import JMProductNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMProductNetworkController';
import {useQueries} from '@tanstack/react-query';
import {RQKey} from '../../../jiomart-common/src/JMConstants';

interface MultiVariantView {
  config: any;
  packets: any;
  verticalCode: any;
  onClose: () => void;
  handleImagePress: (item: any) => void;
}

const productController = new JMProductNetworkController();

const MultiVariantView = ({
  config,
  packets,
  verticalCode,
  onClose,
  handleImagePress,
}: MultiVariantView) => {
  const primary50 = useColor('primary_50');
  const multiVariants = useQueries({
    queries: (packets?.[0]?.items || [])?.map((variant: any) => {
      return {
        queryKey: [RQKey.PRODUCT_MULTI_VARIANTS_SIZE, variant?.uid], // Unique key per size fetch
        queryFn: () =>
          productController.fetchProductSize({slug: variant?.slug}),
        enabled: !!variant?.slug,
        select: (data: any) => ({
          ...data,
          packetItem: variant, // Merge the original packet item
        }),
      };
    }),
  });
  return (
    <SafeAreaView style={{flex: 1}}>
      <JMBtmSheetHeader title={config?.headerTitle ?? ''} onPress={onClose} />
      <View style={styles.container}>
        {multiVariants?.map((data: any, index: number) => {
          const productMultiVariant = data?.data;
          return (
            <React.Fragment
              key={productMultiVariant?.packetItem?.slug || index}>
              <MultiVariantCardView
                imageUrl={productMultiVariant?.packetItem?.medias[0]?.url}
                title={productMultiVariant?.packetItem?.value}
                price={productMultiVariant?.price?.effective?.max}
                originalPrice={productMultiVariant?.price?.marked?.max}
                discount={productMultiVariant?.discount}
                outOfStock={!productMultiVariant?.sizes[0]?.is_available}
                isLoading={data?.isLoading}
                slug={productMultiVariant?.packetItem?.slug}
                itemId={productMultiVariant?.packetItem?.uid}
                itemSize={productMultiVariant?.sizes[0]?.value}
                verticalCode={verticalCode}
                handleImagePress={() => {
                  handleImagePress(productMultiVariant);
                }}
              />
              <JioDivider />
            </React.Fragment>
          );
        })}
        {config?.button?.done?.isVisible ? (
          <Pressable
            style={[styles.doneButtonStyle, , {backgroundColor: primary50}]}
            onPress={onClose}>
            <JioText
              text={config?.button?.done?.title}
              appearance={JioTypography.BUTTON}
              color="primary_inverse"
            />
          </Pressable>
        ) : null}
      </View>
      {/* <JMToast /> */}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  bottom_container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 30,
    paddingVertical: 16,
  },
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    // marginBottom: rh(16),
    marginHorizontal: rh(24),
    flexWrap: 'wrap',
    flex: 1,
  },
  doneButtonStyle: {
    marginVertical: rh(12),
    paddingVertical: rh(12),
    paddingHorizontal: 16,
    alignSelf: 'stretch',
    borderRadius: 100,
    alignItems: 'center',
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeIcon: {
    fontSize: 20,
    color: '#666',
  },
  packetContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  leftContent: {
    flex: 1,
  },
  sizeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  size: {
    fontSize: 16,
    fontWeight: '500',
    marginRight: 8,
  },
  discountBadge: {
    backgroundColor: '#e8f5e9',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  discountText: {
    color: '#2e7d32',
    fontSize: 12,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  price: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  originalPrice: {
    fontSize: 14,
    color: '#666',
    textDecorationLine: 'line-through',
    marginRight: 8,
  },
  pricePerKg: {
    fontSize: 14,
    color: '#666',
  },
  addButton: {
    backgroundColor: '#0078AD',
    paddingHorizontal: 24,
    paddingVertical: 8,
    borderRadius: 4,
  },
  addButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  outOfStock: {
    color: '#d32f2f',
    fontSize: 14,
  },
});

export default MultiVariantView;
