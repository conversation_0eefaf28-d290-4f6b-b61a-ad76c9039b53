import React, {useCallback, useRef, useState, useEffect} from 'react';
import {
  View,
  Pressable,
  StyleSheet,
  FlatList,
  ViewStyle,
} from 'react-native';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/components/JioText/JioText.types';
import {useTheme} from '@jio/rn_components';
import {rw} from '../../../jiomart-common/src/JMResponsive';

interface CategoryChipsProps {
  categories: any[];
  onCategorySelect?: (category: string[] | null) => void;
  style?: ViewStyle;
}

const CategoryChips: React.FC<CategoryChipsProps> = React.memo(
  ({categories, onCategorySelect, style}) => {
    const theme = useTheme();
    const sortCategories = useCallback(() => {
      const selectedItems = categories.filter(item => item.is_selected);
      const unselectedItems = categories.filter(item => !item.is_selected);

      // Sort selected items alphabetically
      const sortedSelected = [...selectedItems].sort((a, b) => {
        const displayA = a.display.split('::').pop() || a.display;
        const displayB = b.display.split('::').pop() || b.display;
        return displayA.localeCompare(displayB);
      });

      return [...sortedSelected, ...unselectedItems];
    }, [categories]);

    const reArrangeData = () => {
      if (categories?.length) {
        // Sort categories if there are any selected items
        const hasSelectedItems = categories.some(cat => cat.is_selected);
        if (hasSelectedItems) {
          const sortedCategories = sortCategories();
          return sortedCategories;
        } else {
          return categories;
        }
      }
    };

    const [orderedCategories, setOrderedCategories] = useState(reArrangeData());

    const flatListRef = useRef<FlatList>(null);

    const scrollToTop = useCallback(() => {
      flatListRef.current?.scrollToOffset({offset: 0, animated: true});
    }, []);

    // const handleCategoryPress = useCallback(
    //   (value: string | null) => {
    //     // Get currently selected categories
    //     const selectedCategories =
    //       value === null
    //         ? []
    //         : orderedCategories
    //             .filter(cat => cat.is_selected || cat.value === value)
    //             .map(cat => cat.value);

    //     // Call the parent's onCategorySelect with the updated selection
    //     onCategorySelect?.(
    //       selectedCategories.length ? selectedCategories : null,
    //     );
    //     scrollToTop();
    //   },
    //   [onCategorySelect, orderedCategories, scrollToTop],
    // );
    const handleCategoryPress = useCallback(
      (value: string | null) => {
        // Handle "All" selection
        if (value === null) {
          onCategorySelect?.(null);
          scrollToTop();
          return;
        }

        // Find the clicked item
        const clickedItem = orderedCategories.find(cat => cat.value === value);
        if (!clickedItem) return;

        // Get current selected categories
        let selectedCategories = orderedCategories
          .filter(cat => cat.is_selected)
          .map(cat => cat.value);

        // Toggle selection
        if (clickedItem.is_selected) {
          // Remove the item if it's already selected
          selectedCategories = selectedCategories.filter(cat => cat !== value);
        } else {
          // Add the item if it's not selected
          selectedCategories = [...selectedCategories, value];
        }

        // Call onCategorySelect with null if no categories are selected
        onCategorySelect?.(
          selectedCategories.length > 0 ? selectedCategories : null,
        );
        scrollToTop();
      },
      [onCategorySelect, orderedCategories, scrollToTop],
    );
    const renderItem = useCallback(
      ({item}: {item: FilterValue}) => {
        return (
          <Pressable
            activeOpacity={0.9}
            onPress={() => handleCategoryPress(item.value)}>
            <View
              style={[
                styles.chip,
                {
                  backgroundColor: item.is_selected
                    ? theme.primary_50
                    : '#F5F5F5',
                },
              ]}>
              <JioText
                text={item.display.split('::').pop() || item.display}
                appearance={JioTypography.BODY_XXS_BOLD}
                color={item.is_selected ? 'primary_20' : 'primary_60'}
              />
            </View>
          </Pressable>
        );
      },
      [theme, handleCategoryPress],
    );

    if (!categories?.length) return null;

    // Check if any category is selected to determine "All" button state
    const isAllSelected = !categories.some(cat => cat.is_selected);

    return (
      <View style={[styles.container, style]}>
        <Pressable
          onPress={() => handleCategoryPress(null)}
          style={styles.allContainer}>
          <View
            style={[
              styles.allChip,
              {
                backgroundColor: isAllSelected ? theme.primary_50 : '#F5F5F5',
              },
            ]}>
            <JioText
              text="All"
              appearance={JioTypography.BODY_XXS_BOLD}
              color={isAllSelected ? 'primary_20' : 'primary_60'}
            />
          </View>
        </Pressable>

        <FlatList
          ref={flatListRef}
          data={orderedCategories}
          renderItem={renderItem}
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.list}
          contentContainerStyle={styles.listContent}
          keyExtractor={item => item.value ?? item.display}
          snapToInterval={0}
        />
      </View>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: '#F5F5F5',
    borderTopLeftRadius: 24,
    borderBottomLeftRadius: 24,
    alignItems: 'center',
    marginBottom: 12,
    marginLeft: rw(24),
  },
  list: {
    flex: 1,
  },
  listContent: {
    alignItems: 'center',
    paddingRight: 24,
  },
  chip: {
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 15,
    marginRight: 10,
    paddingHorizontal: 10,
    height: 24,
  },
  allContainer: {
    width: 55,
    height: 32,
    backgroundColor: '#F5F5F5',
    borderTopLeftRadius: 40,
    borderBottomLeftRadius: 40,
    justifyContent: 'center',
  },
  allChip: {
    width: 47,
    height: 24,
    marginLeft: 5,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default CategoryChips;
