import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {useConfigFile} from '../../../jiomart-general/src/hooks/useJMConfig';
import type {UseProductGridListingScreenProps} from '../types/ProductGridListingScreenType';
import {RQKey} from '../../../jiomart-common/src/JMConstants';
import {useInfiniteQuery} from '@tanstack/react-query';
import JMProductNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMProductNetworkController';
import useTransitionState from '../../../jiomart-general/src/hooks/useTransitionState';
import {useMemo, useRef, useState} from 'react';
import {
  interpolate,
  useAnimatedStyle,
  useSharedValue,
} from 'react-native-reanimated';
import {
  formatCategoryName,
  generateFilterReq,
  isDateValid,
  isHyperLocal,
  isScheduleDelivery,
} from '../../../jiomart-common/src/utils/JMCommonFunctions';
import {getBaseURL} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import {navigateTo} from '../../../jiomart-general/src/navigation/JMNavGraph';
import {navBeanObj} from '../../../jiomart-common/src/JMNavGraphUtil';

const productController = new JMProductNetworkController();

const useProductGridListingScreenController = (
  props: UseProductGridListingScreenProps,
) => {
  const {route, navigation} = props;
  const initialSortApplied = {
    value: 'popular',
    logo: 'https://cdn.pixelbin.io/v2/jio-mart-2/MOluAr/original/rhos-sit/misc/default-assets/original/popular.png',
    display: 'Popularity',
    priority: 1,
    name: 'Popularity',
    is_selected: false,
  };

  const slug = route?.params?.params?.slug;
  const page = route?.params?.params?.page;
  const sort = route?.params?.params?.sort || initialSortApplied?.value;
  const filter = route?.params?.params?.filter || '';
  const config = useConfigFile(JMConfigFileName.JMGridPLPConfigurationFileName);

  const isTransitionComplete = useTransitionState();
  const [params, setParams] = useState({
    page_id: '*',
    page_size: 20,
    sort_on: sort,
    f: filter,
  });
  const [selectedSort, setSelectedSort] = useState<any>(initialSortApplied);
  const [selectedVariants, setSelectedVariants] = useState<any[]>([]);
  const [filterTab, setFilterTab] = useState<string>('');
  const [initializeFirsTab, setInitializeFirsTab] = useState(false)

  //bottomsheet
  const [sortBtmSheet, setSortBtmSheet] = useState(false);
  const [filterBtmSheet, setFilterBtmSheet] = useState(false);
  const [brandBtmSheet, setBrandBtmSheet] = useState(false);
  const [multiVariantBtmSheet, setMultiVariantBtmSheet] = useState(false);

  const listRef = useRef<any>(null);
  const firstFilterRes = useRef(null);

  const scrollYd = useSharedValue(0);

  const productListRes = useInfiniteQuery({
    queryKey: [
      RQKey.PRODUCT_LIST,
      params.page_id,
      params.f,
      params.sort_on,
      slug,
    ],
    queryFn: ({pageParam = '*'}) => {
      return productController.fetchProductList({
        slug: slug,
        params: {
          ...params,
          page_id: pageParam,
        },
      });
    },
    gcTime: 0,
    initialPageParam: '*',
    placeholderData: (previousData, _) => previousData,
    enabled: !!(slug && isTransitionComplete),
    getNextPageParam: lastPage => {
      return lastPage?.page?.has_next ? lastPage?.page?.next_id : undefined;
    },
  });

  const handleLoadMore = () => {
    if (productListRes.hasNextPage && !productListRes.isFetchingNextPage) {
      productListRes.fetchNextPage();
    }
  };

  const pages = productListRes.data?.pages || [];
  const productList = pages?.flatMap(item => item?.items) || [];
  console.log("🚀 ~ productList:", productList)
  const sortOptions = pages?.[0]?.sort_on?.filter(
    (item: any) => item?.display !== 'Relevance',
  );
  const filterOption = pages?.[0]?.filters;

  const categoryL2Title = useMemo(() => {
    const categoryFilter = filterOption?.find(
      (f: any) =>
        f?.key?.kind === 'multivalued' && f?.key?.name === 'category-l2',
    );
    return categoryFilter?.values || [];
  }, [filterOption]);

  const l4CategoryData = useMemo(() => {
    if (firstFilterRes.current) {
      return firstFilterRes.current;
    }
    firstFilterRes.current = filterOption
      ?.find(
        (f: any) =>
          f?.key?.kind === 'multivalued' &&
          f?.key?.name === 'l3_category_names',
      )
      ?.values?.map((item: any) => {
        return {
          ...item,
          display: formatCategoryName(item?.display),
        };
      });
    return firstFilterRes.current;
  }, [filterOption]);
  const allImage =
    filterOption?.find(
      (f: any) =>
        f?.key?.kind === 'multivalued' && f?.key?.name === 'category-l2',
    )?.values?.[0]?.logo ?? '';

  const animatedCatHeaderTitle = useAnimatedStyle(() => {
    const stripTranslateY = interpolate(
      scrollYd.value,
      [0, 180],
      [0, -20],
      'clamp',
    );
    const height = interpolate(scrollYd.value, [0, 70], [40, 0], 'clamp');
    // const opacity = interpolate(scrollYd.value, [0, 100], [1, 0], 'clamp');
    return {
      transform: [{translateY: stripTranslateY}],
      height,
      // opacity,
    };
  });
  const fabAnimatedStyle = useAnimatedStyle(() => {
    const outputRange = scrollYd.value > 400 ? 1 : 0;
    const opacity = outputRange;
    return {opacity};
  });
  const scrollToTop = () => {
    listRef.current?.scrollToOffset({offset: 0, animated: true});
  };

  // sort method
  const handleSelectedSort = (value: any) => {
    setSelectedSort(value);
    setParams(prev => {
      return {
        ...prev,
        sort_on: value?.value,
        page_id: '*',
      };
    });
    scrollToTop();
  };
  // filter method
  const handleApplyFilter = (value: string) => {
    setParams(prev => {
      return {
        ...prev,
        f: value,
      };
    });
    closeFilterBtmSheet();
  };
  const handleClearAllFilter = (value: string) => {
    setParams(prev => {
      return {
        ...prev,
        f: value,
      };
    });
  };
  //category
  const handleCategorySelect = (category: any) => {
    let newFilters;
    if (category.display === 'All') {
      newFilters = {};
    } else {
      newFilters = {
        ...(category.display === 'All' || !category.value
          ? {}
          : {l3_category_names: [category.value]}),
      };
    }
    handleApplyFilter(generateFilterReq(newFilters));
  };

  const makeQuickDeliverable = (item, config) => {
    const deliveryDate = new Date(Date.now() + config?.scheduleDelivery?.delay);
    let tag;
    const shouldShowHyperLocal = config?.hyperLocal?.show !== false;
    const shouldShowScheduleDelivery = config?.scheduleDelivery?.show !== false;

    const hyperLocalText = config?.hyperLocal?.text || 'Quick \nDelivery';
    const scheduleDeliveryText =
      config?.scheduleDelivery?.text || 'Scheduled Delivery By';

    if (
      isHyperLocal(item?.seller_id, item?.attributes['vertical-code']) &&
      shouldShowHyperLocal
    ) {
      tag = {
        IcScheduleDelivery: false,
        displayText: hyperLocalText,
        hyperLocal: true,
        image: config?.hyperLocal?.image,
        backgroundColor: config?.hyperLocal?.backgroundColor,
        textColor: config?.hyperLocal?.textColor,
        primaryText: config?.hyperLocal?.primaryText,
        secondaryText: config?.hyperLocal?.secondaryText,
        iconHeight: config?.hyperLocal?.iconHeight,
        iconWidth: config?.hyperLocal?.iconWidth,
      };
    }

    if (
      isScheduleDelivery(item?.seller_id, item?.attributes['vertical-code']) &&
      shouldShowScheduleDelivery
    ) {
      tag = {
        IcScheduleDelivery: true,
        displayText: `${scheduleDeliveryText} ${deliveryDate.toLocaleDateString(
          'en-US',
          {
            weekday: 'short',
          },
        )}, ${deliveryDate.getDate()} ${deliveryDate.toLocaleDateString(
          'en-US',
          {month: 'short'},
        )}`,
        hyperLocal: false,
        backgroundColor: config?.scheduleDelivery?.backgroundColor,
        textColor: config?.scheduleDelivery?.textColor,
        primaryText: config?.scheduleDelivery?.primaryText,
        secondaryText: config?.scheduleDelivery?.secondaryText,
      };
    }
    return tag;
  };
  const checkOfferCount = (item: any) => {
    const isTradeIn = item?.attributes?.['trade-in'];
    const paymentTag = item?.attributes?.['payment-tag'];
    const paymentTagToDate = item?.attributes?.['payment-tag-todate'];
    const discount = item?.discount_meta;
    const discountToDate = item?.discount;
    let count = 0;

    if (isTradeIn?.toUpperCase() === 'TRUE') {
      count++;
    }

    if (paymentTag && isDateValid(paymentTagToDate ?? '')) {
      count++;
    }

    if (
      discount &&
      Object.keys?.(discount)?.length > 0 &&
      isDateValid(discountToDate ?? '')
    ) {
      count += Object.keys?.(discount)?.length;
    }

    return count;
  };

  const handleRedirectProductPage = (slug: string, cta: NavigationBean) => {
    const actionUrl = `${getBaseURL()}${cta?.actionUrl?.replace(
      '[SLUG]',
      slug,
    )}`;

    navigateTo(
      navBeanObj({
        ...cta,
        actionUrl,
        params: {
          slug,
        },
      }),
      navigation,
    );
    closeMultiVariantBtmSheet();
  };

  const openSortBtmSheet = () => {
    setSortBtmSheet(true);
  };
  const closeSortBtmSheet = () => {
    setSortBtmSheet(false);
  };
  const openFilterBtmSheet = () => {
    setFilterBtmSheet(true);
  };
  const closeFilterBtmSheet = () => {
    setFilterBtmSheet(false);
  };
  const openBrandBtmSheet = () => {
    setFilterTab('brand');
    setBrandBtmSheet(true);
  };
  const closeBrandBtmSheet = () => {
    setBrandBtmSheet(false);
  };
  const openMultiVariantBtmSheet = (val: any) => {
    setSelectedVariants(val);
    setMultiVariantBtmSheet(true);
  };
  const closeMultiVariantBtmSheet = () => {
    setMultiVariantBtmSheet(false);
  };
  const handleScroll = (e: any) => {
    if (
      e.nativeEvent.layoutMeasurement.height + e.nativeEvent.contentOffset.y <=
      e.nativeEvent.contentSize.height - 80
    ) {
      scrollYd.value = e.nativeEvent.contentOffset.y;
    }
  };

  return {
    ...props,
    navigationBean: route.params,
    config,
    slug,
    page,
    params,
    sortOptions,
    selectedSort,
    sortBtmSheet,
    productList,
    productListRes,
    isTransitionComplete,
    openSortBtmSheet,
    closeSortBtmSheet,
    filterOption,
    filterBtmSheet,
    openFilterBtmSheet,
    closeFilterBtmSheet,
    brandBtmSheet,
    openBrandBtmSheet,
    closeBrandBtmSheet,
    handleSelectedSort,
    scrollToTop,
    categoryL2Title,
    listRef,
    animatedCatHeaderTitle,
    l4CategoryData,
    allImage,
    fabAnimatedStyle,
    filterTab,
    setFilterTab,
    handleApplyFilter,
    handleClearAllFilter,
    selectedVariants,
    openMultiVariantBtmSheet,
    closeMultiVariantBtmSheet,
    multiVariantBtmSheet,
    handleRedirectProductPage,
    handleCategorySelect,
    makeQuickDeliverable,
    checkOfferCount,
    handleLoadMore,
    handleScroll,
    initializeFirsTab,
    setInitializeFirsTab,
  };
};

export default useProductGridListingScreenController;
