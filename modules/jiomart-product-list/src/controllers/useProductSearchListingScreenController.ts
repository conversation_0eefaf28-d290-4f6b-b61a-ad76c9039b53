import {useConfigFile} from '../../../jiomart-general/src/hooks/useJMConfig';
import type {UseProductSearchListingScreenProps} from '../types/ProductSearchListingScreenType';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {useRef, useState} from 'react';
import {useInfiniteQuery} from '@tanstack/react-query';
import {RQKey} from '../../../jiomart-common/src/JMConstants';
import JMProductNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMProductNetworkController';
import {
  formatCategoryName,
  generateFilterReq,
} from '../../../jiomart-common/src/utils/JMCommonFunctions';
import {getBaseURL} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import {navigateTo} from '../../../jiomart-general/src/navigation/JMNavGraph';
import {
  navBeanObj,
  type NavigationBean,
} from '../../../jiomart-common/src/JMNavGraphUtil';
import useTransitionState from '../../../jiomart-general/src/hooks/useTransitionState';

const productController = new JMProductNetworkController();

const useProductSearchListingScreenController = (
  props: UseProductSearchListingScreenProps,
) => {
  const {route, navigation} = props;
  const initialSortApplied = {
    value: 'popular',
    logo: 'https://cdn.pixelbin.io/v2/jio-mart-2/MOluAr/original/rhos-sit/misc/default-assets/original/popular.png',
    display: 'Popularity',
    priority: 1,
    name: 'Popularity',
    is_selected: false,
  };

  let query = route?.params?.params?.query;
  query = typeof query === 'string' ? [query] : query;

  const initialFilter =
    query?.length > 1
      ? generateFilterReq({
          slist: query,
        })
      : '';

  const sort = route?.params?.params?.sort || initialSortApplied?.value;
  const filter = route?.params?.params?.filter || initialFilter;

  const isTransitionComplete = useTransitionState();
  const config = useConfigFile(
    JMConfigFileName.JMSearchPLPConfigurationFileName,
  );

  const [currentQuery, setCurrentQuery] = useState<string>(query[0] ?? '');
  const [params, setParams] = useState({
    page_id: '*',
    page_size: 20,
    sort_on: sort,
    f: filter,
    q: currentQuery,
  });
  const [selectedSort, setSelectedSort] = useState<any>(initialSortApplied);
  const [selectedVariants, setSelectedVariants] = useState<any[]>([]);
  // bottomsheet state
  const [sortBtmSheet, setSortBtmSheet] = useState(false);
  const [filterBtmSheet, setFilterBtmSheet] = useState(false);
  const [multiVariantBtmSheet, setMultiVariantBtmSheet] = useState(false);
  const listRef = useRef<any>(null);

  const productListRes = useInfiniteQuery({
    queryKey: [
      RQKey.PRODUCT_SEARCH_LIST,
      params.page_id,
      params.f,
      params.sort_on,
      params.q,
    ],
    queryFn: ({pageParam = '*'}) => {
      return productController.fetchSearchProductList({
        params: {
          ...params,
          page_id: pageParam,
        },
      });
    },
    gcTime: 0,
    initialPageParam: '*',
    placeholderData: (previousData, _) => previousData,
    getNextPageParam: lastPage => {
      return lastPage?.page?.has_next ? lastPage?.page?.next_id : undefined;
    },
    enabled: isTransitionComplete,
  });

  const pages = productListRes.data?.pages || [];
  const productList = pages?.flatMap(item => item?.items) || [];
  const sortOptions = pages?.[0]?.sort_on?.filter(
    (item: any) => item?.display !== 'Relevance',
  );
  const filterOption = pages?.[0]?.filters;
  const [filterTab, setFilterTab] = useState<string>('');
  const [initializeFirsTab, setInitializeFirsTab] = useState(false)

  const handleLoadMore = () => {
    if (productListRes.hasNextPage && !productListRes.isFetchingNextPage) {
      productListRes.fetchNextPage();
    }
  };

  const l4CategoryData = filterOption
    ?.find(
      (f: any) =>
        f?.key?.kind === 'multivalued' && f?.key?.name === 'l3_category_names',
    )
    ?.values?.map((item: any) => {
      return {
        ...item,
        display: formatCategoryName(item?.display),
      };
    });
  // allImage =
  //   state.tabCategoryData?.find(
  //     filter =>
  //       filter?.key?.kind === 'multivalued' &&
  //       filter?.key?.name === 'category-l2',
  //   )?.values?.[0]?.logo ?? '';

  const getCategoryTitle = (category: any, selectedL1Category: any) => {
    const title = category?.filter(
      (data: any) => data?.slug === selectedL1Category,
    );
    return title?.[0]?.name;
  };

  // sort methods

  const handleSelectedSort = (value: any) => {
    setSelectedSort(value);
    setParams(prev => {
      return {
        ...prev,
        sort_on: value?.value,
        page_id: '*',
      };
    });
    scrollToTop();
  };

  // filter method
  const handleApplyFilter = (value: string) => {
    setParams(prev => {
      return {
        ...prev,
        f: value,
      };
    });
    closeFilterBtmSheet();
  };
  const handleClearAllFilter = (value: string) => {
    setParams(prev => {
      return {
        ...prev,
        f: value,
      };
    });
  };

  // multi search query
  const sortedMultiSearchQuery = () => {
    if (!query) {
      return [];
    }
    return [
      currentQuery,
      ...query.filter((keyword: string) => keyword !== currentQuery),
    ];
  };
  const handleMultiSearchQuery = (value: string) => {
    setCurrentQuery(value);
    setParams(prev => {
      return {
        ...prev,
        q: value,
        page_id: '*',
      };
    });
    scrollToTop();
  };

  const handleRedirectProductPage = (slug: string, cta: NavigationBean) => {
    const actionUrl = `${getBaseURL()}${cta?.actionUrl?.replace(
      '[SLUG]',
      slug,
    )}`;

    navigateTo(
      navBeanObj({
        ...cta,
        actionUrl,
        params: {
          slug,
        },
      }),
      navigation,
    );
    closeMultiVariantBtmSheet();
  };

  // bottomsheet methods
  const openSortBtmSheet = () => {
    setSortBtmSheet(true);
  };
  const closeSortBtmSheet = () => {
    setSortBtmSheet(false);
  };
  const openFilterBtmSheet = () => {
    setFilterBtmSheet(true);
  };
  const closeFilterBtmSheet = () => {
    setFilterBtmSheet(false);
  };
  const openMultiVariantBtmSheet = (val: any) => {
    setSelectedVariants(val);
    setMultiVariantBtmSheet(true);
  };
  const closeMultiVariantBtmSheet = () => {
    setMultiVariantBtmSheet(false);
  };

  const scrollToTop = () => {
    listRef.current?.scrollToOffset({offset: 0, animated: true});
  };

  return {
    ...props,
    navigationBean: route.params,
    config,
    params,
    productList,
    isTransitionComplete,
    sortOptions,
    filterOption,
    productListRes,
    selectedSort,
    sortBtmSheet,
    openSortBtmSheet,
    closeSortBtmSheet,
    filterBtmSheet,
    openFilterBtmSheet,
    closeFilterBtmSheet,
    multiVariantBtmSheet,
    openMultiVariantBtmSheet,
    closeMultiVariantBtmSheet,
    selectedVariants,
    getCategoryTitle,
    handleSelectedSort,
    l4CategoryData,
    handleLoadMore,
    listRef,
    scrollToTop,
    handleApplyFilter,
    handleClearAllFilter,
    filterTab,
    setFilterTab,
    handleMultiSearchQuery,
    sortedMultiSearchQuery,
    handleRedirectProductPage,
    initializeFirsTab,
    setInitializeFirsTab,
  };
};

export default useProductSearchListingScreenController;
