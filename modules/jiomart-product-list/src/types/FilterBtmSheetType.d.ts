import {AppScreens} from '../../../jiomart-common/src/JMAppScreenEntry';
import type {BottomSheetChildren} from '../../../jiomart-general/src/ui/BottomSheet/types/BottomSheetType';

export interface FilterOptionProps {
  title?: string;
  rightIcon?: RightIcon;
  showRightIcon?: boolean;
  type?: FilterOptionType;
  value?: FilterOptionValue;
  style?: any;
  disabled?: boolean;
  onPress?: () => void;
}

export interface FilterBtmSheetProps extends BottomSheetChildren {
  config?: any;
  screen?: AppScreens;
  slug?: any;
  selectedTab?: string;
  params?: any;
  data: FilterOption[];
  initializeFirsTab: any;
  onClose: () => void;
  onApply?: (filter: any) => void;
  onFilterTab?: (val: string) => void;
  onClearAll?: (filter: any) => void;
}

export enum FilterOptionType {
  CHECKBOX = 'checkbox',
  RADIO = 'radio',
  CAT = 'cat',
}

export enum FilterOptionValue {
  SELECTED = 'selected',
  UNSELECTED = 'unselected',
  PARTIAL = 'partial',
}

interface RightIcon {
  ic: string;
  color: string;
  size?: string;
}
