export interface ProductListingViewProps {
  loading?: boolean;
  config?: any;
  filterAndSortBar: FilterAndSortBarProps;
  data?: any[];
  l3CategoryData?: any[];
  l4CategoryData?: any[];
  selectedL3Category?: any;
  department?: any;
  slug?: any;
  hasNextPageDataIsLoading?: boolean;
  multiSearchQuery?: any[];
  onPressMultiSearchQuery?: (value: string) => void;
  onl3CategoryPress?: (data: any) => void;
  onl4CategoryPress?: (data: any) => void;
  onMultiVariantPress?: (data: any) => void;
  onProductCardPress?: (data: any) => void;
  onLoadMore?: () => void;
  onListRef?: (ref: any) => void;
}
