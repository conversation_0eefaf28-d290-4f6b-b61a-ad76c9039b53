import {ActivityIndicator, Platform, StyleSheet, View} from 'react-native';
import React from 'react';
import useProductGridListingScreenController from '../controllers/useProductGridListingScreenController';
import type {ProductGridListingScreenProps} from '../types/ProductGridListingScreenType';
import ScreenSlot, {
  DeeplinkHandler,
} from '../../../jiomart-general/src/ui/JMScreenSlot';
import BottomSheet from '../../../jiomart-general/src/ui/BottomSheet/BottomSheet';
import SortBtmSheet from '../components/SortBtmSheet';
import FilterBtmSheet from '../components/FilterBtmSheet';
import MultiVariantView from '../components/MultiVariantView';
import SubCategoriesView from '../components/SubCategoriesView';
import {FlashList} from '@shopify/flash-list';
import Animated from 'react-native-reanimated';
import JMFab from '../components/JMFab';
import {JioText} from '@jio/rn_components';
import GroceriesTopBar from '../components/GroceriesTopBar';
import NegativeScreenUI from '../../../jiomart-general/src/ui/NegativeScreenUI';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {formatCategoryName} from '../../../jiomart-common/src/utils/JMCommonFunctions';
import {rh} from '../../../jiomart-common/src/JMResponsive';
import {AppScreens} from '../../../jiomart-common/src/JMAppScreenEntry';
import LgProductGridCard from '../../../jiomart-general/src/ui/LgProductGridCard';
import {checkValueIsExistInArray} from '../../../jiomart-common/src/utils/JMArrayUtility';
import {GroceryGridShimmer} from '../components/shimmer/PlpShimmer';
import GridProductListingScreenShimmer from '../components/shimmer/GridProductListingScreenShimmer';
import {countSelectedFilterTabs} from '../utils';

const ProductGridListingScreen = (props: ProductGridListingScreenProps) => {
  const {
    navigation,
    navigationBean,
    config,
    slug,
    params,
    sortOptions,
    selectedSort,
    sortBtmSheet,
    productList,
    productListRes,
    isTransitionComplete,
    openSortBtmSheet,
    closeSortBtmSheet,
    filterOption,
    filterBtmSheet,
    openFilterBtmSheet,
    closeFilterBtmSheet,
    brandBtmSheet,
    openBrandBtmSheet,
    closeBrandBtmSheet,
    selectedVariants,
    openMultiVariantBtmSheet,
    closeMultiVariantBtmSheet,
    multiVariantBtmSheet,
    handleSelectedSort,
    scrollToTop,
    categoryL2Title,
    listRef,
    animatedCatHeaderTitle,
    l4CategoryData,
    allImage,
    fabAnimatedStyle,
    filterTab,
    setFilterTab,
    handleApplyFilter,
    handleClearAllFilter,
    handleRedirectProductPage,
    handleCategorySelect,
    makeQuickDeliverable,
    checkOfferCount,
    handleLoadMore,
    handleScroll,
    initializeFirsTab,
    setInitializeFirsTab,
  } = useProductGridListingScreenController(props);

  const renderItem = ({item}: {item: any}) => {
    // if (item?.adSpotID) {
    //   return null;
    // }
    return (
      <>
        <LgProductGridCard
          title={item?.name ?? ''}
          titleColor={config?.gridCard?.title?.color}
          titleMaxLine={config?.gridCard?.title?.maxLine}
          slug={item?.slug ?? ''}
          productCode={item?.uid ?? ''}
          sellingPrice={item?.price?.effective?.min}
          effectivePriceColor={config?.gridCard?.effectivePriceColor}
          stridePrice={item?.price?.marked?.min}
          markedPriceColor={config?.gridCard?.markedPriceColor}
          discount={item?.discount}
          discountColor={config?.gridCard?.discount?.color}
          sellable={item?.sellable}
          image={item?.medias?.[0]?.url}
          itemSize={item?.sizes?.[0]}
          verticalCode={item?.attributes?.['vertical-code']}
          outOfStockText={config?.gridCard?.outOfStock?.text}
          outOfStockColor={config?.gridCard?.outOfStock?.color}
          addCta={{
            disableDefaultFun: item?.variants?.length > 0,
            button: {
              text: {
                text: config?.gridCard?.options?.title?.replace(
                  '[TITLE]',
                  item?.variants?.[0]?.items?.length,
                ),
                appearance: JioTypography.BODY_XS_BOLD,
                color: config?.gridCard?.options?.color,
              },
              icon: config?.gridCard?.options?.icon,
              style: {
                columnGap: 0,
                paddingHorizontal: 12,
                paddingVertical: 6,
              },
            },
            onPress: () => {
              openMultiVariantBtmSheet(item?.variants);
              // multivariantBottomSheetOpen(item?.variants);
            },
          }}
          tag={makeQuickDeliverable(item, config)}
          showOutOfStock={!item.sellable}
          // onPress={handleProductPress(item)}
          addToCartTitle={config?.addToCart?.title}
          style={{margin: 1, minHeight: 258}}
          sellerId={item?.seller_id}
          storeId={item?.store_id}
          articleAssignment={item?.article_assignment}
          showAddToCart={
            (checkValueIsExistInArray(
              item?.attributes?.['vertical-code'],
              config?.addToCartVerticalCode,
            ) &&
              item.sellable) ||
            !config?.addToCart?.isVisible
          }
          showWishlist={
            checkValueIsExistInArray(
              item?.attributes?.['vertical-code'],
              config?.wishlistVerticalCode,
            ) || !config?.wishlist?.isVisible
          }
          showOffer={checkOfferCount(item) > 0}
          offerText={config?.gridCard?.offer?.title?.replace(
            '[OFFER]',
            checkOfferCount(item),
          )}
          onOfferPress={() => {
            // openOfferBottomSheet(item);
          }}
        />
      </>
    );
  };

  const bottomSheetContent = () => {
    return (
      <>
        <BottomSheet
          visible={sortBtmSheet}
          onBackDropClick={closeSortBtmSheet}
          onDrag={closeSortBtmSheet}>
          <SortBtmSheet
            onClose={closeSortBtmSheet}
            data={sortOptions}
            title={config?.bottomSheet?.sort?.headerTitle ?? ''}
            defaultSelected={selectedSort?.display}
            onSelectedSort={handleSelectedSort}
          />
        </BottomSheet>
        <BottomSheet
          visible={filterBtmSheet}
          maxHeightPercent={95}
          isStatic
          staticHeight={rh(650)}
          onBackDropClick={closeFilterBtmSheet}
          onDrag={closeFilterBtmSheet}>
          <FilterBtmSheet
            data={filterOption}
            screen={AppScreens.PRODUCT_LISTING_SCREEN}
            slug={slug}
            params={params}
            selectedTab={filterTab}
            initializeFirsTab={initializeFirsTab}
            onFilterTab={val => {
              setInitializeFirsTab(true);
              setFilterTab(val);
            }}
            onClose={closeFilterBtmSheet}
            onApply={handleApplyFilter}
            onClearAll={handleClearAllFilter}
            config={config?.bottomSheet?.filter}
          />
        </BottomSheet>
        <BottomSheet
          visible={brandBtmSheet}
          maxHeightPercent={95}
          isStatic
          staticHeight={rh(650)}
          onBackDropClick={closeBrandBtmSheet}
          onDrag={closeBrandBtmSheet}>
          <FilterBtmSheet
            data={filterOption}
            screen={AppScreens.PRODUCT_LISTING_SCREEN}
            slug={slug}
            params={params}
            selectedTab={filterTab}
            initializeFirsTab={initializeFirsTab}
            onFilterTab={val => {
              setInitializeFirsTab(true);
              setFilterTab(val);
            }}
            onClose={closeBrandBtmSheet}
            onApply={handleApplyFilter}
            onClearAll={handleClearAllFilter}
            config={config?.bottomSheet?.brand}
          />
        </BottomSheet>
        <BottomSheet
          visible={multiVariantBtmSheet}
          onBackDropClick={closeMultiVariantBtmSheet}
          onDrag={closeMultiVariantBtmSheet}>
          <MultiVariantView
            onClose={closeMultiVariantBtmSheet}
            verticalCode={'GROCERIES'}
            config={config?.bottomSheet?.multiVariant}
            handleImagePress={data => {
              handleRedirectProductPage(
                data?.packetItem?.slug,
                config?.bottomSheet?.multiVariant?.cta,
              );
            }}
            packets={selectedVariants}
          />
        </BottomSheet>
      </>
    );
  };

  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={bean => {
        return (
          <ScreenSlot
            navigationBean={bean}
            navigation={navigation}
            bottomSheetContent={bottomSheetContent}
            children={() => {
              if (productListRes.isLoading || !isTransitionComplete) {
                return <GridProductListingScreenShimmer />;
              }
              return (
                <>
                  {!config?.disableCategoryName &&
                    categoryL2Title?.length > 0 && (
                      <>
                        <Animated.View
                          style={[
                            animatedCatHeaderTitle,
                            styles.categoryTitle,
                            {overflow: 'hidden'},
                          ]}>
                          <JioText
                            text={formatCategoryName(
                              categoryL2Title?.[0]?.display,
                            )}
                            appearance={JioTypography.HEADING_XXS}
                            style={{
                              flex: 1,
                              alignSelf: 'center',
                              textAlignVertical: 'center',
                              marginTop: Platform.OS == 'ios' ? 10 : 0,
                            }}
                          />
                        </Animated.View>
                      </>
                    )}
                  <View style={styles.container}>
                    <SubCategoriesView
                      data={l4CategoryData}
                      onCategorySelect={handleCategorySelect}
                      allImage={allImage}
                      // resetCategoryAnimation={resetCategoryAnimation}
                      // setResetCategoryAnimation={setResetCategoryAnimation}
                    />
                    <View style={styles.listContainer}>
                      {productList?.length > 0 && (
                        <GroceriesTopBar
                          openFilterBtmSheet={openFilterBtmSheet}
                          openSortBtmSheet={openSortBtmSheet}
                          openBrandBtmSheet={openBrandBtmSheet}
                          selSortFilterName={selectedSort?.display}
                          // selBrandName={selectedFilters?.brand?.[0] || ''}
                          readFilterDataUtil={handleApplyFilter}
                          appliedFilters={countSelectedFilterTabs(filterOption)}
                          appliedBrandFilters={countSelectedFilterTabs(
                            filterOption,
                          )}
                          config={config}
                        />
                      )}

                      {/* {productListRes.isFetching && <GroceryGridShimmer />} */}
                      {productList?.length > 0 ? (
                        <FlashList
                          ref={listRef}
                          data={productList}
                          scrollEnabled={productList?.length > 2}
                          renderItem={renderItem}
                          numColumns={2}
                          showsVerticalScrollIndicator={false}
                          estimatedItemSize={290}
                          onScroll={handleScroll}
                          onEndReached={handleLoadMore}
                          onEndReachedThreshold={0.5}
                          ListFooterComponent={
                            productListRes.isFetchingNextPage ? (
                              <View style={styles.footer}>
                                <ActivityIndicator
                                  size="large"
                                  color="#0078AD"
                                />
                              </View>
                            ) : null
                          }
                          contentContainerStyle={{
                            paddingHorizontal: 8,
                            paddingTop: 10,
                            paddingBottom: 120,
                          }}
                        />
                      ) : (
                        <NegativeScreenUI
                          image={{
                            uri: config?.negativeCases?.noItemFound?.imageUrl,
                          }}
                          title={
                            config?.negativeCases?.noItemFound?.title ?? ''
                          }
                          subTitle={
                            config?.negativeCases?.noItemFound?.subTitle ?? ''
                          }
                          isButtonVisible={true}
                          button={
                            config?.negativeCases?.noItemFound?.buttonTitle
                          }
                          onPress={() => {
                            // JMNavigation({
                            //   navigationType: NavigationType.GO_BACK,
                            // });
                          }}
                        />
                      )}
                    </View>

                    {!config?.disableFabIcon && productList?.length > 0 && (
                      <Animated.View style={fabAnimatedStyle}>
                        <JMFab style={{bottom: 65}} onPress={scrollToTop} />
                      </Animated.View>
                    )}
                  </View>
                </>
              );
            }}
          />
        );
      }}
    />
  );
};

export default ProductGridListingScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#EFEFEF',
    flexDirection: 'row',
    zIndex: 10,
  },
  negativeScreenContainer: {flex: 1, backgroundColor: '#EFEFEF'},
  listContainer: {
    flex: 1,
  },
  footer: {
    padding: 16,
    alignItems: 'center',
  },
  categoryTitle: {
    zIndex: -1000000,
    backgroundColor: '#ffff',
  },
});
