import React, {useRef} from 'react';
import {ActivityIndicator, Platform, StyleSheet, View} from 'react-native';
import {PlpGridShimmer} from '../components/shimmer/PlpShimmer';
import FilterAndSortBar from '../components/FilterAndSortBar';
import {rh, rw} from '../../../jiomart-common/src/JMResponsive';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
} from 'react-native-reanimated';
import {FlashList} from '@shopify/flash-list';
import L3CategoryComponent from '../components/L3CategoryComponent';
import CategoryChips from '../components/CategoryChips';
import {checkValueIsExistInArray} from '../../../jiomart-common/src/utils/JMArrayUtility';
import JMFab from '../components/JMFab';
import NewFashionGridCard from '../../../jiomart-general/src/ui/NewFashionGridCard';
import type {ProductListingViewProps} from '../types/ProductListingType';

const ProductGridListingView = (props: ProductListingViewProps) => {
  const {
    loading,
    config,
    filterAndSortBar,
    data,
    l3CategoryData,
    l4CategoryData,
    selectedL3Category,
    department,
    slug,
    hasNextPageDataIsLoading,
    onl3CategoryPress,
    onl4CategoryPress,
    onMultiVariantPress,
    onProductCardPress,
    onLoadMore,
    onListRef,
  } = props;
  const listRef = useRef<any>(null);
  const scrollYd = useSharedValue(0);
  const fabAnimatedStyle = useAnimatedStyle(() => {
    const outputRange = scrollYd.value > 400 ? 1 : 0;
    const opacity = outputRange;
    return {opacity};
  });

  const scrollToTop = () => {
    listRef.current?.scrollToOffset({offset: 0, animated: true});
  };

  const handleScroll = (e: any) => {
    if (
      e.nativeEvent.layoutMeasurement.height + e.nativeEvent.contentOffset.y <=
      e.nativeEvent.contentSize.height - 80
    ) {
      scrollYd.value = e.nativeEvent.contentOffset.y;
    }
  };

  const renderItem = ({item, index}: any) => {
    const isFirstColumn = index % 2 === 0;
    const isSecondColumn = index % 2 === 1;

    // if (item?.adSpotID) {
    //   return (
    //     <JioAds
    //       key={item?.adSpotID}
    //       adSpotId={item?.adSpotID}
    //       adHeight={item?.adHeight}
    //       adWidth={item?.adWidth}
    //       adType={item?.adType}
    //       adjHeight={item?.adjHeight}
    //       adjWidth={Math.floor(getScreenDim.width - 48)}
    //       style={[isFirstColumn ? styles.firstColumn : styles.secondColumn]}
    //       adMetaData={{
    //         l1: '' + slug,
    //         l2: '',
    //         l3: '' + slug,
    //         search_query: '',
    //       }}
    //     />
    //   );
    // }

    return (
      <View
        style={[
          {marginTop: rh(16), paddingTop: rh(16)},
          index == 0 || index == 1
            ? undefined
            : {borderTopWidth: 1, borderTopColor: '#E0E0E0'},
          isFirstColumn ? {marginLeft: rw(24)} : {marginRight: rw(24)},
        ]}>
        <View
          style={[
            isFirstColumn && styles.firstColumn,
            isSecondColumn && styles.secondColumn,
          ]}>
          <NewFashionGridCard
            uid={item?.uid ?? ''}
            slug={item?.slug ?? ''}
            imageUrl={item.medias?.[0]?.url}
            disableWishlist={
              checkValueIsExistInArray(
                item?.attributes?.['vertical-code'],
                config?.wishlistVerticalCode,
              ) || !config?.wishlist?.isVisible
            }
            disableAddToCart={
              (checkValueIsExistInArray(
                item?.attributes?.['vertical-code'],
                config?.addToCartVerticalCode,
              ) &&
                item.sellable) ||
              !config?.addToCart?.isVisible
            }
            shouldShowVeg={item.attributes?.['food-type']}
            shouldShowSmartBazzar={
              item.seller_id === config?.smartBazzarToShowSellerId &&
              checkValueIsExistInArray(
                item?.attributes?.['vertical-code'],
                config?.smartBazzarToShowVerticalCode,
              )
            }
            vegIcon={config?.gridCard?.vegIcon}
            smartBazzarImage={config?.smartBazzarImage}
            tag={item?.attributes?.tag}
            tagBackgroundColor={config?.gridCard?.tag?.backgroundColor}
            tagColor={config?.gridCard?.tag?.color}
            disableTag={config?.gridCard?.tag?.isVisible}
            title={item?.name ?? ''}
            titleColor={config?.gridCard?.title?.color}
            titleMaxLine={config?.gridCard?.title?.maxLine}
            effectivePrice={item.price.effective.max}
            effectivePriceColor={config?.gridCard?.effectivePriceColor}
            markedPrice={item.price.marked.max}
            markedPriceColor={config?.gridCard?.markedPriceColor}
            discount={item.discount}
            discountBackgroundColor={
              config?.gridCard?.discount?.backgroundColor
            }
            discountColor={config?.gridCard?.discount?.color}
            shouldShowMultiVariant={item?.variants?.[0]?.items?.length > 0}
            size={item?.sizes[0] ?? 'Demo'}
            totalMultiVariant={config?.gridCard?.multiVariant?.total?.replace(
              '[TOTAL]',
              item?.variants?.[0]?.total,
            )}
            multiVariantColor={config?.gridCard?.multiVariant?.title?.color}
            multiVariantIcon={config?.gridCard?.multiVariant?.icon}
            totalMultiVariantColor={config?.gridCard?.multiVariant?.totalColor}
            onMultiVariantPress={() => {
              onMultiVariantPress?.(item?.variants);
            }}
            addToCartTitle={config?.addToCart?.title}
            verticalCode={item?.attributes?.['vertical-code']}
            shouldShowOutofStock={!item.sellable}
            outOfStockText={config?.gridCard?.outOfStock?.text}
            outOfStockColor={config?.gridCard?.outOfStock?.color}
            onPress={() => {
              onProductCardPress?.(item);
            }}
          />
        </View>
      </View>
    );
  };
  const renderListHeaderComponent = () => {
    // const TopAds = config?.jioAds?.topAds?.map((item, index) => {
    //   return item?.isVisible ? (
    //     <JioAds
    //       key={`${item?.adSpotID}-${index}`}
    //       adSpotId={item?.adSpotID}
    //       adHeight={item?.adHeight}
    //       adWidth={item?.adWidth}
    //       adType={item?.adType}
    //       adjHeight={item?.adjHeight}
    //       adjWidth={
    //         Platform.OS === 'ios'
    //           ? Math.floor(getScreenDim.width - 48)
    //           : item?.adjWidth
    //       }
    //       style={styles.ads}
    //     />
    //   ) : null;
    // });
    return (
      <>
        {/* {TopAds} */}
        {!config?.disableCategoryList &&
        checkValueIsExistInArray(
          department,
          config?.categoryListDepartment ?? [],
        ) &&
        l3CategoryData ? (
          <L3CategoryComponent
            slug={slug}
            categories={l3CategoryData}
            isL3Slug={!!selectedL3Category}
            onPress={onl3CategoryPress}
          />
        ) : null}
        {!config?.disableL4CategoryList && slug ? (
          <CategoryChips
            categories={l4CategoryData || []}
            onCategorySelect={category => {
              onl4CategoryPress?.(category);
            }}
          />
        ) : null}
      </>
    );
  };

  if (loading) {
    return <PlpGridShimmer style={{paddingTop: 66}} />;
  }

  return (
    <View style={styles.container}>
      <FilterAndSortBar {...filterAndSortBar} />
      <View style={styles.wrapper}>
        <FlashList
          bounces={false}
          ref={ref => {
            onListRef?.(ref);
            listRef.current = ref;
          }}
          data={data || []}
          renderItem={renderItem}
          ListHeaderComponentStyle={{flex: 1, width: '100%'}}
          showsVerticalScrollIndicator={false}
          estimatedItemSize={60}
          contentContainerStyle={styles.listContainer}
          onScroll={handleScroll}
          onEndReached={onLoadMore}
          onEndReachedThreshold={0.5}
          ListFooterComponent={
            hasNextPageDataIsLoading ? (
              <View style={styles.footer}>
                <ActivityIndicator size="large" color="#0078AD" />
              </View>
            ) : null
          }
          ListHeaderComponent={renderListHeaderComponent}
          numColumns={2}
        />
      </View>
      {!config?.disableFabIcon && (
        <Animated.View style={fabAnimatedStyle}>
          <JMFab onPress={scrollToTop} style={styles.fab} />
        </Animated.View>
      )}
    </View>
  );
};

export default ProductGridListingView;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  wrapper: {flex: 1},
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    paddingTop: 60,
    paddingBottom: 143,
  },
  footer: {
    padding: 16,
    alignItems: 'center',
  },
  ads: {
    alignItems: Platform.OS === 'ios' ? 'center' : 'baseline',
    borderBottomWidth: 1,
    paddingBottom: 12,
    marginBottom: 16,
    borderColor: '#E0E0E0',
    marginHorizontal: rw(24),
  },
  firstColumn: {
    // marginLeft: rw(24),
    marginRight: rw(8),
  },
  secondColumn: {
    marginLeft: rw(8),
    // marginRight: rw(24),
  },
  multiSearch: {
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderRadius: 50,
    borderColor: '#E0E0E0',
  },
  selectedMultiSearch: {
    backgroundColor: '#0078AD',
    borderWidth: 0,
  },
  multiSearchBadge: {flexDirection: 'row', gap: 8, marginBottom: rh(24)},
  fab: {
    bottom: 97,
  },
});
