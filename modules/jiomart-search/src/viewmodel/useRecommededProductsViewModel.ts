import { getPrefString } from "../../../jiomart-common/src/JMAsyncStorageHelper";
import { AsyncStorageKeys } from "../../../jiomart-common/src/JMConstants";
import { isNullOrUndefinedOrEmpty } from "../../../jiomart-common/src/JMObjectUtility";
import SearchService from "../../../jiomart-networkmanager/src/JMNetworkController/JMSearchAPINetworkController";


const useRecommendedProductsViewModel = (config: any) => {
  const getRecommendedItems = async () => {
    try {
      const user = await getPrefString(AsyncStorageKeys.USER_DETAILS);

      const userDetails = !isNullOrUndefinedOrEmpty(user)
        ? JSON.parse(user ?? '')
        : {};
      const queryParams = {
        userid: userDetails?._id,
      };
      if (config.is_buy_again_api_enabled ?? false) {
        let buyAgainData = await buyAgain(queryParams);

        if (buyAgainData && buyAgainData?.items.length > 0) {
          const recommandedData = await recommandedApi(
            buyAgainData?.items,
            queryParams,
          );
          if (recommandedData && recommandedData?.items?.length > 0) {
            // BuyAgain + Recommanded Data ... Need to check response of recommandedData
            return (buyAgainData = {
              ...buyAgainData,
              items: buyAgainData?.items.concat(recommandedData?.items || []),
            });
          } else {
            return buyAgainData;
          }
        }
      }

      const itemCodesArray = await SearchService.fetchRecommendedItems(
        queryParams,
      );
      if (itemCodesArray && itemCodesArray.length > 0) {
        return await getProductsServiceabilityItems(itemCodesArray);
      }
    } catch (error) {
      console.error('getRecommendedItems API Failed', error);
    }
  };

  const buyAgain = async (queryParams: any) => {
    try {
      let buyAgainItemArray: any[] = [];
      buyAgainItemArray = await SearchService.getBuyAgainItems(queryParams);
      const uniqueBuyAgainItems = Array.from(new Set(buyAgainItemArray));
      if (buyAgainItemArray.length > 0) {
        const buyAgainInventory = await getProductsServiceabilityItems(
          uniqueBuyAgainItems,
        );
        buyAgainItemArray = buyAgainInventory;
      }
      return buyAgainItemArray;
    } catch (error) {
      console.error('buyAgain API Failed', error);
      return null;
    }
  };

  const recommandedApi = async (buyAgainItems: [], queryParams: any) => {
    try {
      let recommendedItemsArray: any[] = [];
      if (buyAgainItems.length < config.maxItemsVisible) {
        if (config.is_recommended_product_api_enabled ?? false) {
          recommendedItemsArray = await SearchService.fetchRecommendedItems(
            queryParams,
          );
          if (recommendedItemsArray && recommendedItemsArray.length > 0) {
          const recommendedItemsData = await getProductsServiceabilityItems(
            recommendedItemsArray,
          );
          return recommendedItemsData;
          }
        }
      }
    } catch (error) {
      console.error('Recommended API Failed', error);
      return null;
    }
  };

  const getProductsServiceabilityItems = async (items_codes: Array<string>) => {
    try {
      const body = items_codes;
      return await SearchService.fetchRecommendedItemsServiceability(body).then(
        recommendedItemsData => {
          return recommendedItemsData;
        },
      );
    } catch (error) {
      console.error('getProductsServiceabilityItems API Failed', error);
    }
  };

  return {
    getRecommendedItems,
  };
};

export default useRecommendedProductsViewModel;
