import {StyleSheet, View} from 'react-native';
import React, {FC} from 'react';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {JMTagProps} from '../types/JMTagProps';

const JMTag: FC<JMTagProps> = ({
  color,
  textColor = 'sparkle_60',
  appearance = JioTypography.BODY_XXS_BOLD,
  text,
  style,
  icon,
  textstyle,
  maxLine,
  minLines,
}) => {
  return (
    <View style={[styles.tagContainer, style, {backgroundColor: color}]}>
      {React.isValidElement(icon) && <>{React.cloneElement(icon)}</>}
      <JioText
        appearance={appearance}
        text={text}
        color={textColor}
        style={textstyle}
        maxLines={maxLine}
        minLines={minLines}
      />
    </View>
  );
};

export default JMTag;

const styles = StyleSheet.create({
  tagContainer: {
    backgroundColor: '#E5F7EE',
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
    flexDirection: 'row',
    columnGap: 4,
    alignItems: 'center',
  },
});
