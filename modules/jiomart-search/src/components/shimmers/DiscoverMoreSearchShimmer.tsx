import { View } from "react-native";
import { ShimmerKind } from "@jio/rn_components/src/index.types";
import { getChipHeight, getChipWidth } from "../../../../jiomart-common/src/utils/JMCommonFunctions";
import JioMartShimmer from "../../../../jiomart-general/src/ui/JioMartShimmer";

export const DiscoverMoreSearchShimmer = () => {
  return (
    <View style={{marginHorizontal: 24}}>
      <JioMartShimmer
        width={getChipWidth() * 118}
        height={getChipHeight() * 24}
        kind={ShimmerKind.RECTANGLE}
        style={{
          borderRadius: 16,
          marginVertical: 12 * getChipHeight(),
          marginTop: 32,
        }}
      />
      <View
        style={{
          marginBottom: 12 * getChipHeight(),
          flexDirection: 'row',
          flexWrap: 'wrap',
        }}>
        <JioMartShimmer
          width={getChipWidth() * 140}
          height={getChipHeight() * 20}
          kind={ShimmerKind.RECTANGLE}
          style={{
            borderRadius: 80 * getChipWidth(),
          }}
        />
        <JioMartShimmer
          height={getChipHeight() * 20}
          width={getChipWidth() * 90}
          style={{
            borderRadius: 80 * getChipWidth(),
            marginHorizontal: 6 * getChipWidth(),
          }}
          kind={ShimmerKind.RECTANGLE}
        />
        <JioMartShimmer
          height={getChipHeight() * 20}
          width={getChipWidth() * 70}
          style={{
            borderRadius: 80 * getChipWidth(),
          }}
          kind={ShimmerKind.RECTANGLE}
        />
      </View>
      <View
        style={{
          marginBottom: 12 * getChipHeight(),
          flexDirection: 'row',
          flexWrap: 'wrap',
        }}>
        <JioMartShimmer
          height={getChipHeight() * 20}
          width={getChipWidth() * 70}
          style={{
            borderRadius: 80 * getChipWidth(),
          }}
          kind={ShimmerKind.RECTANGLE}
        />
        <JioMartShimmer
          height={getChipHeight() * 20}
          width={getChipWidth() * 90}
          style={{
            borderRadius: 80 * getChipWidth(),
            marginHorizontal: 6 * getChipWidth(),
          }}
          kind={ShimmerKind.RECTANGLE}
        />
        <JioMartShimmer
          height={getChipHeight() * 20}
          width={getChipWidth() * 140}
          style={{
            borderRadius: 80 * getChipWidth(),
          }}
          kind={ShimmerKind.RECTANGLE}
        />
      </View>
    </View>
  );
};
