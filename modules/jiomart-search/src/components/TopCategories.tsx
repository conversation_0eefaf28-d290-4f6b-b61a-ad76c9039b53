import React from 'react';
import {View, StyleSheet, ScrollView} from 'react-native';
// import {TopCategoriesShimmer} from '../shimmer/SearchShimmer';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import useTopCategoriesController from '../controller/useTopCategoriesController';
import frameStyles from '../styles/FrameStyles';

import Frame from './Frame';
import { getChipHeight, getChipWidth, getScreenWidth } from '../../../jiomart-common/src/utils/JMCommonFunctions';

interface PropsType {
  title: string;
  categoryViewContent: any;
}

const TopCategories = (props: {template: PropsType; onClick: any}) => {
  const {title, categoryViewContent} = props?.template;
  const {topCategoriesData, imageRatio, frameBackgroundColor, loading} =
    useTopCategoriesController(categoryViewContent);
  return (
    <View style={{marginBottom: 20 * getChipHeight()}}>
      {topCategoriesData &&
      topCategoriesData !== undefined &&
      topCategoriesData.length > 0 ? (
        <>
          <JioText
            text={title}
            appearance={JioTypography.HEADING_XXS}
            color={'primary_grey_100'}
            style={{
              paddingLeft: 24 * getChipWidth(),
              marginTop: 32 * getChipHeight(),
              marginBottom: 12 * getChipHeight(),
            }}
          />
          <View style={{width: getScreenWidth()}}>
            <ScrollView
              horizontal={true}
              showsHorizontalScrollIndicator={false}
              scrollEnabled={
                topCategoriesData && topCategoriesData.length > 3 ? true : false
              }>
              <View style={{flexDirection: 'row'}}>
                {topCategoriesData &&
                topCategoriesData !== undefined &&
                topCategoriesData.length > 0
                  ? topCategoriesData.map((item, index) => {
                      return (
                        <View
                          key={index}
                          style={{
                            paddingLeft: index == 0 ? 24 * getChipWidth() : 0,
                            marginRight: 16 * getChipWidth(),
                          }}>
                          <Frame
                            updateCategory={() => {
                              props?.onClick(item.slug, {
                                query: {
                                  department: item.department?.[0],
                                  category:
                                    item?.childs && item?.childs?.length > 0
                                      ? item?.childs?.[0]?.slug
                                      : item?.department?.[0],
                                },
                              });
                            }}
                            text={item.name ?? ''}
                            imageUrl={item.banners?.portrait?.url ?? ''}
                            imageWidth={60 * imageRatio}
                            imageHeight={60 * imageRatio}
                            bgViewStyle={{
                              ...frameStyles.bgViewStyle,
                              backgroundColor: frameBackgroundColor,
                            }}
                            textStyle={frameStyles.textStyle}
                            verticalGap={4}
                          />
                        </View>
                      );
                    })
                  : null}
              </View>
            </ScrollView>
          </View>
        </>
      ) : loading ? /*(
        <TopCategoriesShimmer />
      )*/ null : null}
    </View>
  );
};

export default TopCategories;

const styles = StyleSheet.create({
  top_cat_text: {
    fontSize: 16,
    fontWeight: '900',
    lineHeight: 20,
    color: '#141414',
    fontFamily: 'JioType-Medium',
    paddingLeft: 24 * getChipWidth(),
  },
});
