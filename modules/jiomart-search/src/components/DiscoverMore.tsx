import React from 'react';
import {View, StyleSheet} from 'react-native';
// import {DiscoverMoreSearchShimmer} from '../shimmer/SearchShimmer';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import useDiscoverMoreController from '../controller/useDiscoverMoreController';
import {Item} from '../types/JMSearchAutoCompleteResponse';
import ChipStyles from '../styles/ChipStyles';
import {DiscoverMoreSearchShimmer} from './shimmers/DiscoverMoreSearchShimmer';
import { getChipHeight, getChipWidth } from '../../../jiomart-common/src/utils/JMCommonFunctions';
import TextChip from '../../../jiomart-general/src/ui/TextChip';

interface PropsType {
  title: string;
  visibleSearchKeywordCount: number;
}

const DiscoverMore = (props: {template: PropsType; onClick: any}) => {
  const {title, visibleSearchKeywordCount} = props?.template;
  const {discoverMoreData, filteredItemList, ChipBorderColor, loading} =
    useDiscoverMoreController(props?.template);
  return (
    <>
      {discoverMoreData &&
      discoverMoreData &&
      discoverMoreData?.length != 0 &&
      filteredItemList &&
      filteredItemList?.length != 0 ? (
        <>
          <JioText
            text={title}
            style={[styles.discover_text]}
            appearance={JioTypography.HEADING_XXS}
            color={'primary_grey_100'}
          />
          <View style={styles.container}>
            {discoverMoreData
              ?.filter(
                (item: Item) => item?.display && item?.display?.trim() !== '',
              )
              ?.slice(0, visibleSearchKeywordCount)
              ?.map((item: Item, index: number) => (
                <View
                  style={{
                    marginRight: 8 * getChipWidth(),

                    marginBottom: 12 * getChipHeight(),
                  }}
                  key={`dis-${index}`}>
                  <TextChip
                    onPress={() => {
                      props?.onClick(item?.display);
                    }}
                    text={item?.display}
                    containerStyle={[
                      ChipStyles.containerStyle,
                      {borderColor: ChipBorderColor},
                    ]}
                  />
                </View>
              ))}
          </View>
        </>
      ) : loading ? (
        <DiscoverMoreSearchShimmer />
      ) : null}
    </>
  );
};

export default DiscoverMore;

const styles = StyleSheet.create({
  discover_text: {
    paddingHorizontal: 24 * getChipWidth(),
    marginTop: 20 * getChipHeight(),
    marginBottom: 12 * getChipHeight(),
  },
  container: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 24 * getChipWidth(),
  },
});
