import React from 'react';
import {View, Image, ImageSourcePropType} from 'react-native';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {TouchableWithoutFeedback} from 'react-native-gesture-handler';
import { getChipWidth, getChipHeight } from '../../../jiomart-common/src/utils/JMCommonFunctions';

interface bgViewStyleType {
  width: number;
  height: number;
  borderRadius: number;
  backgroundColor: string;
}
interface textStyleType {
  fontSize: number;
  fontWeight: string;
  color: string;
}
interface Props {
  updateCategory: Function;
  bgViewStyle: bgViewStyleType;
  text: string;
  imageUrl: string;
  imageWidth: number;
  imageHeight: number;
  textStyle: textStyleType;
  verticalGap: number;
}

type ImageType = ImageSourcePropType | {uri: string} | undefined;

const Frame = ({
  updateCategory,
  text,
  imageUrl,
  imageWidth,
  imageHeight,
  bgViewStyle,
  textStyle,
  verticalGap,
}: Props) => {
  const url: ImageType = {uri: imageUrl};
  return (
    <TouchableWithoutFeedback
      onPress={() => updateCategory()}
      style={{
        flexDirection: 'column',
        alignItems: 'center',
        alignSelf: 'flex-start',
      }}>
      <View
        style={[
          bgViewStyle,
          {
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
          },
        ]}>
        <Image
          style={{width: imageWidth * getChipWidth(), height: imageHeight * getChipWidth()}}
          source={url}
        />
      </View>
      <JioText
        text={text}
        appearance={JioTypography.BODY_XXS}
        color={'primary_grey_80'}
        style={{
          marginTop: verticalGap * getChipHeight(),
          width: 72 * getChipWidth(),
        }}
        maxLines={2}
        textAlign="center"
      />
    </TouchableWithoutFeedback>
  );
};

export default Frame;
