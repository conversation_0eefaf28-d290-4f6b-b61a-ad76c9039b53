import {JioText} from '@jio/rn_components';
import {
  JioTypography,
  IconSize,
  IconColor,
} from '@jio/rn_components/src/index.types';
import {View, FlatList, StyleSheet} from 'react-native';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import {
  getChipWidth,
  getChipHeight,
} from '../../../jiomart-common/src/utils/JMCommonFunctions';
import useRecommendedProductsController from '../controller/useRecommendedProductsController';
import {
  isHyperLocal,
  isScheduleDelivery,
} from '../controller/useSearchScreenController';
import NewProductGridCard from './NewProductGridCard';
import {RecommendedProductsShimmer} from './shimmers/RecommendedProductsShimmer';

interface RecommendedProductsProps {
  title: string;
}

const RecommendedProducts = (props: {
  template: RecommendedProductsProps;
  onClick: any;
}) => {
  const {recommendedItemData, loading} = useRecommendedProductsController(
    props?.template,
  );
  return (
    <>
      {recommendedItemData && recommendedItemData?.items?.length > 0 ? (
        <View
          style={{
            flex: 1,
          }}>
          <JioText
            text={props?.template?.title}
            appearance={JioTypography.HEADING_XXS}
            color={'primary_grey_100'}
            style={styles.recommendedTextHeading}
          />
          <FlatList
            horizontal={true}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{
              columnGap: 8,
              paddingHorizontal: 24,
              paddingVertical: 8,
            }}
            data={recommendedItemData.items?.slice(0, 10)}
            renderItem={({item, index}) => (
              <View key={index}>
                <NewProductGridCard
                  productCode={
                    JMSharedViewModel.Instance.appSource ===
                    AppSourceType.JM_BAU
                      ? item?.item_code
                      : item?.uid
                  }
                  sellerId={`${item?.seller_id}`}
                  title={item?.name}
                  image={item?.medias?.[0]?.url}
                  sellingPrice={item?.price?.effective?.min}
                  stridePrice={item?.price?.marked?.min}
                  discount={item?.discount}
                  verticalCode={item?.attributes['vertical-code']}
                  isQuickDeliveryLable={{
                    hyperLocal: isHyperLocal(
                      item?.seller_id,
                      item?.attributes['vertical-code'],
                    ),
                    IcScheduleDelivery: isScheduleDelivery(
                      item?.seller_id,
                      item?.attributes['vertical-code'],
                    ),
                    displayText: 'Scheduled Delivery By',
                  }}
                  slug={item?.slug}
                  onPress={() => {
                    props?.onClick(item?.slug, item?.url);
                  }}
                  onCartPress={() => {}}
                  onWishlistPress={() => {}}
                  sellable={item?.sellable}
                  itemSize={item?.sizes?.[0]}
                  storeId={item?.store_id}
                  articleAssignment={item?.article_assignment}
                  addCta={{
                    disableDefaultFun: item?.variants?.length > 0,
                    button: {
                      text: {
                        text: `${item?.variants?.[0]?.items?.length} Options`,
                        appearance: JioTypography.BODY_XS_BOLD,
                        color: 'primary_60',
                      },
                      icon: {
                        ic: 'IcChevronDown',
                        size: IconSize.SMALL,
                        color: IconColor.PRIMARY60,
                      },
                      style: {
                        columnGap: 0,
                        paddingHorizontal: 12,
                        paddingVertical: 6,
                      },
                    },
                  }}
                />
              </View>
            )}
          />
        </View>
      ) : loading ? (
        <RecommendedProductsShimmer />
      ) : null}
    </>
  );
};

export default RecommendedProducts;

const styles = StyleSheet.create({
  recommendedTextHeading: {
    marginHorizontal: 24 * getChipWidth(),
    marginTop: 20 * getChipHeight(),
    marginBottom: 12 * getChipHeight(),
  },
});
