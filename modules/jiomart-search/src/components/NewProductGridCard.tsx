import { JioText } from "@jio/rn_components";
import { JioTypography } from "@jio/rn_components/src/index.types";
import React, { useState } from "react";
import { Pressable, Text, View } from "react-native";
import FastImage from "react-native-fast-image";
import { StaticImages } from "../../../jiomart-common/src/JMConstants";
import { rh, rw } from "../../../jiomart-common/src/JMResponsive";
import { formatPrice } from "../../../jiomart-common/src/utils/JMCommonFunctions";
import WishlistToggleView from "../../../jiomart-general/src/ui/Wishlist/WishlistToggleView";
import { ProductGridCardProps } from "../types/ProductGridCardType";
import JMTag from "./JMTag";
import QCTag from "./QCTag";
import AddCtaView from "../../../jiomart-general/src/ui/AddCta/AddCtaView";
import styles from "../styles/NewProductGridCartStyle";


const NewProductGridCard = (props: ProductGridCardProps) => {
  const {
    image,
    verticalCode,
    title,
    productCode,
    sellerId,
    sellingPrice,
    stridePrice,
    discount,
    isQuickDeliveryLable,
    onPress,
    slug,
    sellable,
    itemSize,
    storeId,
    articleAssignment,
    isMultiVariants,
    containerStyle,
    multiVariantsOnPress,
    addCta,
    gridConfig,
    minQty,
    maxQty,
  } = props;

  const deliveryDate = new Date(Date.now() + 5 * 24 * 60 * 60 * 1000);

  const shouldShowHyperLocal =
    gridConfig?.deliveryConfig?.hyperLocal?.showHyperLocal !== false;
  const shouldShowScheduleDelivery =
    gridConfig?.deliveryConfig?.scheduleDelivery?.showScheduleDelivery !==
    false;

  const hyperLocalText =
    gridConfig?.deliveryConfig?.hyperLocal?.hyperLocalText ||
    'Quick \nDelivery';
  const scheduleDeliveryText =
    gridConfig?.deliveryConfig?.scheduleDelivery?.scheduleDeliveryText ||
    'Scheduled Delivery By';

  const shouldShowWishlist = gridConfig?.showWishListButton !== false;
  const [imageError, setImageError] = useState(false);
  return (
    <Pressable
      style={[
        styles.container,
        {marginHorizontal: 1, marginBottom: 1},
        containerStyle,
      ]}
      activeOpacity={0.65}
      onPress={onPress}
      onLongPress={() => {}}>
      <FastImage
        source={{uri: imageError ? StaticImages.PLACEHOLDER_IMAGE : image}}
        style={styles.image}
        resizeMode={FastImage.resizeMode.contain}
        onError={() => setImageError(true)}
      />
      {shouldShowWishlist && (
        <WishlistToggleView
          request={{uid : productCode}}
          style={{
            position: 'absolute',
            right: 8,
            top: 8,
          }}
        />
      )}
      <Text
        numberOfLines={2}
        style={[
          styles.title,
          {
            lineHeight: 16,
            minHeight: 16 * 2,
            color: 'rgba(0, 0, 0, 0.65)',
            fontWeight: '500',
            fontSize: 12,
          },
        ]}>
        {title}
      </Text>
      <View style={styles.priceContainer}>
        <View>
          <JioText
            text={`₹${formatPrice(sellingPrice)}`}
            appearance={JioTypography.BODY_XS_BOLD}
            color={'primary_grey_100'}
            style={sellingPrice ? styles.activeOpacity : styles.deActiveOpacity}
          />
          <JioText
            text={`₹${formatPrice(stridePrice)}`}
            appearance={JioTypography.BODY_XS}
            color={'primary_grey_60'}
            style={[
              styles.stridePrice,
              sellingPrice != stridePrice && stridePrice
                ? styles.activeOpacity
                : styles.deActiveOpacity,
            ]}
          />
        </View>
        {discount ? (
          <JioText
            text={`${discount}`}
            appearance={JioTypography.BODY_XS_BOLD}
            color={'secondary_50'}
            style={[
              styles.discount,
              discount ? styles.activeOpacity : styles.deActiveOpacity,
            ]}
          />
        ) : null}
      </View>

      {verticalCode?.toLocaleLowerCase() === 'GROCERIES'.toLocaleLowerCase() &&
        (sellable ? (
          <AddCtaView
            request={{
              uid: productCode,
              slug: slug,
              size: `${itemSize}`,
              meta: {vertical_code: verticalCode},
              sellerId: sellerId,
              minQty: minQty ?? 0,
              maxQty: maxQty ?? 0,
            }}
            style={{marginRight: 4}}
          />
        ) : (
          <JioText
            text="Out of Stock"
            appearance={JioTypography.BODY_XXS}
            style={{
              alignSelf: 'flex-end',
              marginRight: 4,
              paddingVertical: rh(9),
            }}
            color={'feedback_error_50'}
          />
        ))}
      <View
        style={{
          flex: 1,
          flexDirection: 'row',
          marginTop: 'auto',
          justifyContent: 'center',
        }}>
        {isQuickDeliveryLable?.hyperLocal && shouldShowHyperLocal ? (
          <QCTag
            // icon={<IcQcTagsLogoMini />}
            color={'#E5F7EE'}
            textColor={'sparkle_70'}
            primaryText={'Delivery'}
            secondaryText={'in 10 to 30 mins'}
            style={[
              styles.hyperLocal,
              isQuickDeliveryLable?.hyperLocal ||
              isQuickDeliveryLable?.IcScheduleDelivery
                ? styles.activeOpacity
                : styles.deActiveOpacity,
            ]}
          />
        ) : isQuickDeliveryLable?.IcScheduleDelivery &&
          shouldShowScheduleDelivery ? (
          <JMTag
            color={'#E5F1F7'}
            textColor={'primary_60'}
            text={`${scheduleDeliveryText} ${deliveryDate.toLocaleDateString(
              'en-US',
              {
                weekday: 'long',
              },
            )}, ${deliveryDate.getDate()} ${deliveryDate.toLocaleDateString(
              'en-US',
              {month: 'long'},
            )}`}
            style={[
              styles.hyperLocal,
              {width: rw(120)},
              isQuickDeliveryLable?.hyperLocal ||
              isQuickDeliveryLable?.IcScheduleDelivery
                ? styles.activeOpacity
                : styles.deActiveOpacity,
            ]}
            minLines={2}
          />
        ) : null}
      </View>
    </Pressable>
  );
};

export default React.memo(NewProductGridCard);
