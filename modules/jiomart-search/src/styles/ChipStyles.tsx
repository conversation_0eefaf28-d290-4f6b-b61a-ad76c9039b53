import {StyleSheet} from 'react-native';
import { getChipWidth, getChipHeight } from '../../../jiomart-common/src/utils/JMCommonFunctions';


const ChipStyles = StyleSheet.create({
  containerStyle: {
    borderRadius: 80,
    paddingHorizontal: 12 * getChipWidth(),
    paddingVertical: 4 * getChipHeight(),
    borderWidth: 1,
    alignSelf: 'flex-start',
  },
  textStyle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#141414',
  },
});

export default ChipStyles;
