import {StyleSheet} from 'react-native';
import { rw, rh } from '../../../jiomart-common/src/JMResponsive';
const styles = StyleSheet.create({
  recent_text: {
    fontSize: 16,
    fontWeight: '900',
    lineHeight: 20,
    color: '#141414',
    fontFamily: 'JioType-Medium',
  },
  clear_all_text: {
    fontSize: 14,
    fontWeight: '700',
    lineHeight: 20,
    color: '#0C5273',
    fontFamily: 'JioType-Medium',
  },
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: rw(24),
    marginTop: rh(24),
    marginBottom: rh(12),
    alignItems: 'center',
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: rw(24),
  },
  recenSearchContainer: {marginRight: rw(8), marginBottom: rh(12)},
  containerStyle: {
    borderRadius: 80,
    paddingHorizontal: rw(12),
    paddingVertical: rh(4),
    borderWidth: 1,
    alignSelf: 'flex-start',
  },
});

export default styles;
