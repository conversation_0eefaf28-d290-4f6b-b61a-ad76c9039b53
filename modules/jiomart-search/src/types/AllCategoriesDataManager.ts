export class CustomCategoryItems {
  action?: ProductListingAction | null;
  navAction?: Action | null;
  name?: string | null;
  childs?: CustomCategoryItems[] | null;
  banners?: ReturnImages | null;
  uid?: number | null;
  slug?: string | null;
  isExpended: boolean = false;
  viewType: number = 3;
  color?: string | null;
  department?: string | null;
  priority_order?: number | null;
  level?: CategoryLevel;
  logo: Media | null | undefined;
}

enum CategoryLevel {
  First = 1,
  Second,
  Third,
}

export class ProductListingAction {
  type?: string | null;
  page?: ProductListingActionPage | null;
}

export class ProductListingActionPage {
  type?: string | null;
  query?: Record<string, any> | null;
  params?: Record<string, any> | null;
}

export class Action {
  page?: ActionPage | null;
  popup?: ActionPage | null;
  type?: string | null;
}

export class ActionPage {
  params?: Record<string, string[]> | null;
  query?: Record<string, string[]> | null;
  url?: string | null;
  type?: PageType | null;
}

export class PageType {
  value: string;

  private constructor(value: string) {
    this.value = value;
  }

  static AboutUs = new PageType('about-us');
  static Addresses = new PageType('addresses');
  static Blog = new PageType('blog');
  static Brands = new PageType('brands');
  static Cards = new PageType('cards');
  static Cart = new PageType('cart');
  static Categories = new PageType('categories');
  static Brand = new PageType('brand');
  static Category = new PageType('category');
  static Collection = new PageType('collection');
  static Collections = new PageType('collections');
  static ContactUs = new PageType('contact-us');
  static ExternalLink = new PageType('external');
  static FAQ = new PageType('faq');
  static Freshchat = new PageType('freshchat');
  static Home = new PageType('home');
  static NotificationSettings = new PageType('notification-settings');
  static MartNotification = new PageType('mart_notification');
  static Orders = new PageType('orders');
  static Page = new PageType('page');
  static Policy = new PageType('policy');
  static Product = new PageType('product');
  static ProductReviews = new PageType('product-reviews');
  static AddProductReview = new PageType('add-product-review');
  static ProductRequest = new PageType('product-request');
  static Products = new PageType('products');
  static Profile = new PageType('profile');
  static ProfileOrderShipment = new PageType('profile-order-shipment');
  static ProfileBasic = new PageType('profile-basic');
  static ProfileCompany = new PageType('profile-company');
  static ProfileEmails = new PageType('profile-emails');
  static ProfilePhones = new PageType('profile-phones');
  static RateUs = new PageType('rate-us');
  static ReferEarn = new PageType('refer-earn');
  static Settings = new PageType('settings');
  static SharedCart = new PageType('shared-cart');
  static TNC = new PageType('tnc');
  static TrackOrder = new PageType('track-order');
  static Wishlist = new PageType('wishlist');
  static Sections = new PageType('sections');
  static Form = new PageType('form');
  static CartDelivery = new PageType('cart-delivery');
  static CartPayment = new PageType('cart-payment');
  static CartReview = new PageType('cart-review');
  static Login = new PageType('login');
  static Register = new PageType('register');
  static ShippingPolicy = new PageType('shipping-policy');
  static ReturnPolicy = new PageType('return-policy');

  static valueOfPageType(value: string): PageType | undefined {
    const pageTypes: PageType[] = Object.values(PageType);
    return pageTypes.find(pt => pt.value === value);
  }

  static fromUrl(url: string): PageType | undefined {
    return undefined; // Placeholder
  }
}

export class ReturnImages {
  //@JsonProperty({ name: 'portrait' })
  portrait?: Media | null;
  //@JsonProperty({ name: 'landscape' })
  landscape?: Media | null;
}

export class Media {
  //@JsonProperty({ name: 'type' })
  type?: string | null;
  //@JsonProperty({ name: 'alt' })
  alt?: string | null;
  //@JsonProperty({ name: 'url' })
  url?: string | null;
  //@JsonProperty({ name: 'meta' })
  meta?: Meta | null;
}

export class Meta {
  //@JsonProperty({ name: 'source' })
  source?: string | null;
}
