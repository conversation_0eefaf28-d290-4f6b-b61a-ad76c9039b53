import {ViewStyle, type StyleProp, type TextStyle} from 'react-native';
import React from 'react';
import {JioColor, JioTypography} from '@jio/rn_components/src/index.types';

export interface JMTagProps {
  icon?: React.JSX.Element;
  color: string;
  textColor?: JioColor;
  text: string;
  style?: StyleProp<ViewStyle>;
  textstyle?: StyleProp<TextStyle>;
  maxLine?: number;
  minLines?: number;
  appearance?: JioTypography;
}
