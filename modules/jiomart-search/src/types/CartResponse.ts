// Generated by https://quicktype.io

export interface Cart2JSON {
  cartDetailResponse: CartDetailResponse;
  wishListResponse: WishListResponse | null;
}

export interface CartDetailResponse {
  breakup_values: BreakupValues;
  buy_now: boolean;
  checkout_mode: string;
  comment: string;
  coupon_text: string;
  currency: Currency;
  delivery_charge_info: string;
  delivery_promise: DeliveryPromise;
  id: string;
  is_valid: boolean;
  items: CartDetailResponseItem[];
  last_modified: string;
  message: string;
  payment_selection_lock: PaymentSelectionLock;
  restrict_checkout: boolean;
  custom_cart: CustomCart;
  success: boolean;
}

export interface CustomCart {
  is_universal: boolean;
  id: string;
  cart_type: string;
  cart_name: string;
}

export interface BreakupValues {
  coupon: Coupon;
  display: Display[];
  loyalty_points: LoyaltyPoints;
  raw: {[key: string]: number};
}

export interface Coupon {
  code: string;
  coupon_type: string;
  coupon_value: number;
  description: string;
  is_applied: boolean;
  message: string;
  minimum_cart_value: number;
  sub_title: string;
  title: string;
  type: string;
  value: number;
}

export interface Display {
  currency_code: string;
  currency_symbol: string;
  display: string;
  key: string;
  message: any[];
  value: number;
}

export interface LoyaltyPoints {
  applicable: number;
  description: string;
  is_applied: boolean;
  total: number;
}

export interface Currency {
  code: string;
  symbol: string;
}

export interface DeliveryPromise {}

export interface CartDetailResponseItem {
  article: Article;
  availability: Availability;
  bulk_offer: DeliveryPromise;
  coupon_message: string;
  discount: string;
  identifiers: Identifiers;
  is_set: boolean;
  key: string;
  message: string;
  parent_item_identifiers: DeliveryPromise;
  price: PurplePrice;
  price_per_unit: PricePerUnitClass;
  product: Product;
  promo_meta: DeliveryPromise;
  promotions_applied: any[];
  quantity: number;
  moq?: Moq;
}

export interface Moq {
  increment_unit?: number;
  minimum?: number;
  maximum?: number;
}

export interface Article {
  extra_meta: DeliveryPromise;
  price: PricePerUnitClass;
  product_group_tags: string[];
  quantity: number;
  seller: Seller;
  size: string;
  store: Store;
  type: string;
  uid: string;
}

export interface PricePerUnitClass {
  base: PricePerUnitBase;
  converted: PricePerUnitBase;
}

export interface PricePerUnitBase {
  currency_code: string;
  currency_symbol: string;
  effective: number;
  marked: number;
}

export interface Seller {
  name: string;
  uid: number;
}

export interface Store {
  uid: number;
}

export interface Availability {
  deliverable: boolean;
  is_valid: boolean;
  other_store_quantity: number;
  out_of_stock: boolean;
  sizes: string[];
}

export interface Identifiers {
  identifier: string;
}

export interface PurplePrice {
  base: PurpleBase;
  converted: PurpleBase;
}

export interface PurpleBase {
  add_on: number;
  currency_code: string;
  currency_symbol: string;
  effective: number;
  marked: number;
  selling: number;
}

export interface Product {
  action: ProductAction;
  brand: Seller;
  categories: Seller[];
  images: Image[];
  item_code: string;
  name: string;
  attributes: ProductAttributes;
  slug: string;
  type: string;
  uid: number;
}

export interface ProductAction {
  query: ActionQuery;
  type: string;
  url: string;
}

export interface ActionQuery {
  product_slug: string[];
}

export interface ProductAttributes {
  brand_name: string;
  name: string;
  'vertical-code': string;
}

export interface Image {
  aspect_ratio: string;
  secure_url: string;
  url: string;
}

export interface PaymentSelectionLock {
  default_options: string;
  enabled: boolean;
  payment_identifier: string;
}

export interface WishListResponse {
  items: WishListResponseItem[];
  page: WishListResponsePage;
}

export interface WishListResponseItem {
  action: ItemAction;
  attributes: ItemAttributes;
  brand: Brand;
  categories: Category[];
  _custom_meta: any[];
  description: string;
  discount: string;
  highlights: any[];
  item_code: string;
  item_type: string;
  medias: Media[];
  name: string;
  net_quantity: DeliveryPromise;
  price: FluffyPrice;
  sellable: boolean;
  short_description: string;
  slug: string;
  type: string;
  uid: number;
}

export interface ItemAction {
  page: PurplePage;
  type: string;
}

export interface PurplePage {
  params: Params;
  type: string;
}

export interface Params {
  slug: string[];
}

export interface ItemAttributes {
  'availability-status': string;
  'is-gen-subs-available': string;
  'formulation-type': string;
  'max-qty-in-order': number;
  'visible-in-catalog': string;
  'is-sodexo-eligible': string;
  catagoryl1?: string;
  'vertical-code': string;
  'is-cold-storage': string;
  'sold-qty-for-rank': number;
  jsonext: string;
  action?: string;
  'brand-id': number;
  'rx-required': string;
  'product-type'?: string;
  'is-subscribable': string;
  'available-at-3p-seller': string;
  'url-path': string;
  brand_name: string;
  searchable: string;
  'is-dpco': string;
  schedule: string;
  'is-high-value': string;
  'display-name': string;
  'is-returnable': string;
  'is-always-available': string;
  name: string;
  'manufacturer-id': number;
  'is-swp': string;
  status: string;
  'level3-search-keys'?: string;
  'level1-search-keys'?: string;
  'level2-search-keys'?: string;
  'payment-tag'?: string;
  'mart-availability'?: string;
  tag?: string;
  'option-code'?: string;
  'search-keywords'?: string;
  'payment-tag-todate'?: string;
  'uom-for-price-compare-factor'?: number;
  'tag-todate'?: string;
  'seller-ids'?: string[];
  product_details?: string;
}

export interface Brand {
  action: BrandAction;
  logo: Logo;
  name: string;
}

export interface BrandAction {
  page: FluffyPage;
  type: string;
}

export interface FluffyPage {
  query: PurpleQuery;
  type: string;
}

export interface PurpleQuery {
  brand: string[];
}

export interface Logo {
  type: string;
  url: string;
}

export interface Category {
  action: CategoryAction;
  logo: Logo;
  name: string;
  uid: number;
}

export interface CategoryAction {
  page: TentacledPage;
  type: string;
}

export interface TentacledPage {
  query: FluffyQuery;
  type: string;
}

export interface FluffyQuery {
  category: string[];
}

export interface Media {
  alt?: string;
  type: string;
  url: string;
}

export interface FluffyPrice {
  effective: Effective;
  marked: Effective;
}

export interface Effective {
  currency_code: string;
  currency_symbol: string;
  max: number;
  min: number;
}

export interface WishListResponsePage {
  has_next: boolean;
  has_previous: boolean;
  item_total: number;
  type: string;
  next_id: string;
}

export interface WishlistActionResponse {
  message: string;
  id: string;
}
