import {StyleProp, type ViewStyle} from 'react-native';
import type {JMTagProps} from '../../utilities/JMTag';
import type {AddCtaViewProps} from '../components/AddCta/types/AddCtaModel';

interface DeliveryConfig {
  hyperLocal?: {
    showHyperLocal: boolean;
    hyperLocalText: string;
  };
  scheduleDelivery?: {
    showScheduleDelivery: boolean;
    scheduleDeliveryText: string;
  };
}

export interface ProductGridCardProps {
  style?: StyleProp<ViewStyle>;
  image?: string;
  brand?: string;
  productCode: number;
  sellerId: string;
  discount?: string;
  title?: string;
  sellingPrice?: number;
  stridePrice?: number;
  isQuickDeliveryLable?: isQuickDeliveryLableProps;
  onPress?: () => void;
  onWishlistPress?: (state: boolean) => void;
  onCartPress?: (value: 'Add' | 'Remove') => void;
  slug?: string;
  verticalCode?: string;
  sellable?: boolean;
  itemSize?: string;
  storeId?: string | number;
  articleAssignment?: string;
  isMultiVariants?: any;
  multiVariantsOnPress?: () => void;
  containerStyle?: StyleProp<ViewStyle>;
  gridConfig?: any;
  showAddToCart?: boolean;
  showWishlist?: boolean;
  showOutOfStock?: boolean;
  showOffer?: boolean;
  offerText?: string;
  onOfferPress?: () => void;
  tag?: JMTagProps;
  minQty?: number;
  maxQty?: number;

  addCta?: {
    disableDefaultFun?: boolean;
    onPress?: () => void;
    button?: AddCtaViewProps['button'];
  };
}

export interface isQuickDeliveryLableProps {
  hyperLocal: boolean;
  IcScheduleDelivery: boolean;
  displayText: string;
}
