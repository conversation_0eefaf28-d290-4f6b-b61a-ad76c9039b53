export interface RecommendedItemsData {
  status: boolean;
  message: string;
  data: Data;
}

export interface Data {
  details: Detail[];
}

export interface Detail {
  action: string;
  departments: number[];
  return_config: ReturnConfig;
  multi_size: boolean;
  name: string;
  custom_order: CustomOrder;
  no_of_boxes: number;
  is_image_less_product: boolean;
  product_publish: ProductPublish;
  is_set: boolean;
  category_slug: string;
  variant_media: VariantMedia;
  item_code: string;
  brand_uid: number;
  currency: string;
  country_of_origin: string;
  is_dependent: boolean;
  slug: string;
  tags: string[];
  item_type: string;
  template_tag: string;
  category_uid: number;
  size?: string;
  variants: Variants;
  created_on: string;
  created_by: CreatedBy;
  modified_on: string;
  modified_by: ModifiedBy;
  stage: string;
  uid: number;
  all_company_ids: number[];
  all_identifiers: string[];
  all_sizes: AllSize[];
  _custom_json?: CustomJson2;
  description: string;
  highlights?: string[];
  media: Medum[];
  net_quantity?: NetQuantity;
  'product-type'?: string;
  product_group_tag?: string[];
  short_description?: string;
  size_guide?: string;
  teaser_tag?: TeaserTag;
  catagoryl1?: string;
  category_name: string;
  is_active: boolean;
  tax_identifier: TaxIdentifier;
  sizes: Size[];
  id: string;
  brand: Brand;
  images: Image[];
  price: Price3;
  status: string;
  is_expirable: boolean;
  hsn_code: string;
  'url-path'?: string;
  'is-swp'?: string;
  'max-qty-in-order'?: number;
  'visible-in-catalog'?: string;
  'is-high-value'?: string;
  jsonext?: string;
  'is-cold-storage'?: string;
  'manufacturer-id'?: number;
  'sold-qty-for-rank'?: number;
  searchable?: string;
  'brand-id'?: number;
  schedule?: string;
  'vertical-code'?: string;
  'display-name'?: string;
  'is-always-available'?: string;
  'available-at-3p-seller'?: string;
  'is-returnable'?: string;
  'is-dpco'?: string;
  'formulation-type'?: string;
  'is-sodexo-eligible'?: string;
  'is-gen-subs-available'?: string;
  'availability-status'?: string;
  'rx-required'?: string;
  'is-subscribable'?: string;
  'sizes-26-58'?: number;
  'additional-colours'?: string;
  color?: string;
  'mart-availability'?: string;
  'level1-search-keys'?: string;
  'level2-search-keys'?: string;
  'level3-search-keys'?: string;
  'option-code'?: string;
  'payment-tag'?: string;
  'payment-tag-todate'?: string;
  'search-keywords'?: string;
  'seller-ids'?: string[];
  tag?: string;
  'tag-todate'?: string;
  'uom-for-price-compare-factor'?: number;
  'brand-name'?: string;
  cuisine?: string;
  tradername?: string;
  actualprice?: number;
  'alternate-product-code'?: string;
  sellingprice?: number;
  'food-type'?: string;
}

export interface ReturnConfig {
  time: number;
  unit: string;
  returnable: boolean;
}

export interface CustomOrder {
  manufacturing_time: number;
  is_custom_order: boolean;
  manufacturing_time_unit: string;
}

export interface ProductPublish {
  is_set: boolean;
  product_online_date: string;
}

export interface VariantMedia {}

export interface Variants {
  'size-test'?: string;
  color?: string;
}

export interface CreatedBy {
  username: string;
  user_id: string;
  super_user: boolean;
}

export interface ModifiedBy {
  username: string;
  user_id: string;
  super_user: boolean;
}

export interface AllSize {
  item_height: number;
  price: Price;
  item_width: number;
  size: string;
  brand_uid: number;
  price_transfer: number;
  item_code: string;
  track_inventory: boolean;
  item_length: number;
  company_id: number;
  item_weight: number;
  seller_identifier: string;
  identifiers: Identifier[];
  sellable: boolean;
  _custom_json?: CustomJson;
}

export interface Price {
  marked: Marked;
  effective: Effective;
}

export interface Marked {
  min: number;
  max: number;
}

export interface Effective {
  min: number;
  max: number;
}

export interface Identifier {
  primary: boolean;
  gtin_value: string;
  gtin_type: string;
}

export interface CustomJson {}

export interface CustomJson2 {}

export interface Medum {
  url: string;
  type: string;
}

export interface NetQuantity {
  unit?: string;
}

export interface TeaserTag {}

export interface TaxIdentifier {
  hsn_code_id: string;
  reporting_hsn: string;
  hsn_code: string;
}

export interface Size {
  size: string;
  store_count: number;
  sellable_quantity: number;
  sellable: boolean;
  price: Price2;
  seller_identifier: string;
  price_transfer: number;
  track_inventory: boolean;
}

export interface Price2 {
  marked: Marked2;
  effective: Effective2;
}

export interface Marked2 {
  min: number;
  max: number;
}

export interface Effective2 {
  min: number;
  max: number;
}

export interface Brand {
  name: string;
  logo: Logo;
  uid: number;
}

export interface Logo {
  aspect_ratio: string;
  aspect_ratio_f: number;
  url: string;
  secure_url: string;
}

export interface Image {
  aspect_ratio: string;
  aspect_ratio_f: number;
  url: string;
  secure_url: string;
}

export interface Price3 {
  marked: Marked3;
  effective: Effective3;
}

export interface Marked3 {
  min: number;
  max: number;
}

export interface Effective3 {
  min: number;
  max: number;
}
