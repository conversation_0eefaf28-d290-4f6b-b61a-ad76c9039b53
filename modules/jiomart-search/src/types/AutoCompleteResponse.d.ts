import {Attributes} from 'react';

export type AutoCompleteResponse = Item[];

export interface Item {
  type: string;
  display: string;
  departments?: boolean;
  logo: Logo;
  _custom_json: any; // You can define a proper type for _custom_json if needed
  action: Action;
}

export interface Logo {
  type: string;
  url: string;
}

export interface Action {
  page: Page;
  type: string;
}

export interface Page {
  type: string;
  query: {
    q: string;
  };
}

export interface BuyAgain {
  items: BuyAgainItem[];
}

export interface BuyAgainItem {
  type: string;
  name: string;
  slug: string;
  uid: number;
  item_code: string;
  medias: Media[];
  discount: string;
  seller_id: number;
  price: Price;
  attributes: Attribute;
  sellable: boolean;
  url: string;
  category_level_id: any;
}

export interface Media {
  url: string;
  type: string;
  alt: string;
}

export interface Price {
  effective: Effective;
  marked: Effective;
}

export interface Effective {
  min: number;
  max: number;
}

export interface Attribute {
  'vertical-code': string;
}
