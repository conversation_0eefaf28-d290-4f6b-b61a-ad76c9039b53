import {useEffect, useState} from 'react';
import {useColor} from '@jio/rn_components';
import useAllCategoriesViewModel from '../../../jiomart-category/src/viewModel/useAllCategoriesViewModel';
import { addStringPref } from '../../../jiomart-common/src/JMAsyncStorageHelper';
import { AsyncStorageKeys } from '../../../jiomart-common/src/JMConstants';
import { JMSharedViewModel } from '../../../jiomart-common/src/JMSharedViewModel';
import { AppSourceType } from '../../../jiomart-common/src/SourceType';
import { getChipWidth, getChipHeight } from '../../../jiomart-common/src/utils/JMCommonFunctions';
import { getConfigFileDataAsync } from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileManager';
import { JMConfigFileName } from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import { SourceItem } from '../../../jiomart-networkmanager/src/models/JMSearch/JMSearchModels';
import { CustomCategoryItems } from '../types/AllCategoriesDataManager';

const useTopCategoriesController = (poplularCategory?: any) => {
  const {getAllCategoryApi} = useAllCategoriesViewModel();
  const [topCategoriesData, setTopCategoriesData] = useState<
    CustomCategoryItems[]
  >([]);

  const [loading, setLoading] = useState(true);
  const frameBackgroundColor = useColor('feedback_error_20');

  const imageRatio =
    getChipWidth() < getChipHeight() ? getChipWidth() : getChipHeight();

  useEffect(() => {
    getTopCategoriesData();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchTopCategoriesData = async () => {
    if (JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU) {
      let data = convertToCustomCategoryItems(poplularCategory);
      const newdata = JSON.stringify(data);
      await storeTopCategoriesData(newdata);
      setTopCategoriesData(data);
      setLoading(false);
    } else {

      const content = await getConfigFileDataAsync(
        JMConfigFileName.JMAllCategoriesConfigurationFileName
      );
      if(content){
        const data = await getAllCategoryApi();
        const newdata = JSON.stringify(data);
        await storeTopCategoriesData(newdata);
        setTopCategoriesData(data);
        setLoading(false);
      }
    }
  };

  function convertToCustomCategoryItems(
    sourceItems: SourceItem[],
  ): CustomCategoryItems[] {
    try {
      return sourceItems.map(item => {
        const category = new CustomCategoryItems();
        category.slug = item.ActionWebURL;
        category.name = item.Title;
        category.banners = {
          portrait: {
            url: item.IconURL,
          },
        };
        category.department = item.ActionWebURL;
        category.childs = [
          {
            slug: item.ActionWebURL,
          } as CustomCategoryItems,
        ];
        return category;
      });
    } catch (error) {
      console.error('Invalid JSON input', error);
      return [];
    }
  }

  const getTopCategoriesData = async () => {
    fetchTopCategoriesData();
  };

  const storeTopCategoriesData = async (data: string) => {
    await addStringPref(AsyncStorageKeys.ALL_CATEGORIES, data);
  };

  return {
    topCategoriesData,
    imageRatio,
    frameBackgroundColor,
    loading,
  };
};

export default useTopCategoriesController;
