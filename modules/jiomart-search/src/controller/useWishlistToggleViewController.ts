import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { Platform } from "react-native";
import { AppScreens } from "../../../jiomart-common/src/JMAppScreenEntry";
import { navBeanObj, ActionType, UserJourneyRequiredState } from "../../../jiomart-common/src/JMNavGraphUtil";
import { isNullOrUndefinedOrEmpty } from "../../../jiomart-common/src/JMObjectUtility";
import handleHapticFeedback from "../../../jiomart-common/src/utils/JMHapticFeedback";
import { navigateTo } from "../../../jiomart-general/src/navigation/JMNavGraph";
import { JMDatabaseManager } from "../../../jiomart-networkmanager/src/db/JMDatabaseManager";
import { useWishlistItems } from "../../../jiomart-wishlist/src/hooks/useWishlist";
import useWishlistOperation from "../../../jiomart-wishlist/src/hooks/useWishlistOperation";
import { WishlistToggleProps } from "../types/WishlistToggleType";


const useWishlistToggleViewController = (props: WishlistToggleProps) => {
  const {request, style, onPressWishlist} = props;
  const wishlistToggle = useWishlistItems({uid: request?.uid});
  const navigation = useNavigation<NativeStackNavigationProp<any>>();
  const {
    addToWishlist,
    removeFromWishlist,
    generateAddToWishlistRequest,
    generateRemoveToWishlistRequest,
  } = useWishlistOperation();
  const actionWishlist = async () => {
    if (Platform.OS === 'android') {
      handleHapticFeedback('impactMedium');
    } else {
      handleHapticFeedback('impactLight');
    }
    const userSession = await JMDatabaseManager.user.getUserSession();
    if (isNullOrUndefinedOrEmpty(userSession)) {
      const bean = navBeanObj({
        actionType: ActionType.OPEN_NATIVE,
        destination: AppScreens.ONE_RETAIL_SCREEN,
        userJourneyRequiredState: UserJourneyRequiredState.LOGIN_REQUIRED,
      });
      navigateTo(bean, navigation);
      return;
    }
    if (wishlistToggle) {
      removeFromWishlist.mutate(generateRemoveToWishlistRequest(request));
      onPressWishlist?.('remove');
      return;
    }
    addToWishlist.mutate(generateAddToWishlistRequest(request));
    onPressWishlist?.('add');
  };

  // useEffect(() => {
  //   // if (wishlistData?.wishlistDataMap?.includes?.(Number(uid))) {
  //   //   setWishlistIconToggle(true);
  //   // } else {
  //   //   setWishlistIconToggle(false);
  //   // }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [uid]);

  return {style, actionWishlist, wishlistToggle};
};

export default useWishlistToggleViewController;
