import React, {useState} from 'react';
import ScreenSlot, {
  DeeplinkHandler,
} from '../../../jiomart-general/src/ui/JMScreenSlot';
import {JioButton, JioIcon, JioText} from '@jio/rn_components';
import {
  ButtonKind,
  IconColor,
  IconSize,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import JMOrderNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMOrderNetworkController';
import {useQuery} from '@tanstack/react-query';
import {RQKey} from '../../../jiomart-common/src/JMConstants';
import Divider, {
  DividerGap,
  DividerType,
} from '../../../jiomart-general/src/ui/Divider';
import useJMRefundDetailsScreenController from '../controllers/useJMRefundDetailsScreenController';
import type {JMRefundDetailsScreenProps} from '../types/JMRefundDetailsScreenType';
import {FlatList, View} from 'react-native';
import Accordion, {
  AccordionPanel,
} from '../../../jiomart-general/src/ui/Accordion/Accordion';
import JMTable, {
  type JMTableBodyProps,
} from '../../../jiomart-general/src/ui/Table/JMTable';
import {rw} from '../../../jiomart-common/src/JMResponsive';
import {formatPrice} from '../../../jiomart-common/src/utils/JMCommonFunctions';
import JMMessageBox from '../../../jiomart-general/src/ui/JMMessageBox';
import JMShortCardDescribe from '../components/JMShortCardDescribe';

const orderController = new JMOrderNetworkController();

const config = {
  block: [
    {
      isVisible: true,
      type: 'ORDER_DETAILS',
    },
    {
      isVisible: true,
      type: 'REFUND_DETAILS',
    },
  ],
};

const JMRefundDetailScreen = (props: JMRefundDetailsScreenProps) => {
  const {route} = props;
  const orderId = route?.params?.params?.orderId;
  const {navigation, navigationBean} =
    useJMRefundDetailsScreenController(props);

  const refundDetailsRes = useQuery({
    queryKey: [RQKey.REFUND_DETAILS],
    queryFn: () => orderController.getRefundDetails({orderId}),
  });

  const [expand, setExpand] = useState<{[index: number]: boolean}>({});

  const tbBody: JMTableBodyProps[][] = [
    [
      {
        text: 'Order ID',
        color: 'primary_grey_80',
        appearance: 'body_xs',
      },
      {
        text: refundDetailsRes.data?.result?.refundDetails?.orderId,
        color: 'primary_grey_100',
        appearance: 'body_xs',
      },
    ],
    [
      {
        text: 'Order Date',
        color: 'primary_grey_80',
        appearance: 'body_xs',
      },
      {
        text: refundDetailsRes.data?.result?.refundDetails?.orderDate,
        color: 'primary_grey_100',
        appearance: 'body_xs',
      },
    ],
    [
      {
        text: 'Total Refund',
        color: 'primary_grey_100',
        appearance: 'body_xs_bold',
      },
      {
        text: `₹${formatPrice(
          refundDetailsRes.data?.result?.refundDetails?.totalRefund,
        )}`,
        color: 'primary_grey_100',
        appearance: 'body_xs_bold',
      },
    ],
  ];

  const tableItemSeprator = () => {
    return (
      <Divider
        type={DividerType.THIN}
        color={'primary_grey_40'}
        vertical={DividerGap.GAP4}
      />
    );
  };

  const renderItem = ({item}) => {
    switch (item?.type) {
      case 'ORDER_DETAILS':
        return (
          <View style={{backgroundColor: '#ffffff', flex: 1}}>
            <JioText
              text="Order Details"
              appearance={JioTypography.BODY_S_BOLD}
              color={'primary_grey_100'}
              style={{paddingTop: 6, paddingLeft: 16}}
            />
            <JMTable
              data={tbBody}
              style={{marginLeft: 16, marginRight: 28, marginTop: 12}}
              cellStyle={{
                paddingVertical: 6,
              }}
              columnStyle={{
                0: {
                  width: rw(128),
                },
              }}
              ItemSeparatorComponent={tableItemSeprator}
            />
            <Divider
              type={DividerType.LARGE}
              color={'primary_grey_20'}
              vertical={DividerGap.GAP16}
            />
          </View>
        );
      case 'REFUND_DETAILS':
        return (
          <View style={{backgroundColor: '#ffffff', flex: 1}}>
            <JioText
              text="Refund Details"
              appearance={JioTypography.BODY_S_BOLD}
              color={'primary_grey_100'}
              style={{paddingLeft: 16, paddingBottom: 16}}
            />
            <Accordion
              onChange={index => {
                console.log('🚀 ~ Accordion ~ index:', index);
              }}
              ItemSeparatorComponent={
                <Divider
                  type={DividerType.LARGE}
                  color={'primary_grey_20'}
                  vertical={DividerGap.GAP16}
                />
              }>
              {refundDetailsRes.data?.result?.shipments?.map((item, index) => {
                let refundBreakup: any[][] = [];
                let bankRef = item?.mop?.find(
                  item => item?.bank_reference_number,
                )?.bank_reference_number;
                item?.mop?.map(refund => {
                  let refundBreakupRow = [];
                  refundBreakupRow.push({
                    text: refund?.payment_desc_alias,
                    color: 'primary_grey_80',
                    appearance: 'body_xs',
                  });
                  refundBreakupRow.push({
                    text: `₹${formatPrice(refund?.refund_amt)}`,
                    color: 'primary_grey_100',
                    appearance: 'body_xs',
                  });
                  refundBreakup.push(refundBreakupRow);
                });
                refundBreakup.push([
                  {
                    text: 'Refund Amount',
                    color: 'primary_grey_80',
                    appearance: 'body_xs',
                  },
                  {
                    text: `₹${formatPrice(item?.shipmentRefundAmt)}`,
                    color: 'primary_grey_100',
                    appearance: 'body_xs_bold',
                  },
                ]);
                return (
                  <AccordionPanel
                    key={`shipment-${index}`}
                    accordionHeader={
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                        }}>
                        <View>
                          <JioText
                            text="Refund Rs.399 (2 items)"
                            appearance={JioTypography.BODY_S}
                            color={'primary_grey_100'}
                          />
                          <JioText
                            text="Reason: cancelled | completed on: 3 May 2025"
                            appearance={JioTypography.BODY_XXS}
                            color={'primary_grey_80'}
                          />
                        </View>
                        <JioIcon
                          ic="IcChevronUp"
                          size={IconSize.LARGE}
                          color={IconColor.PRIMARY60}
                        />
                      </View>
                    }
                    style={{
                      paddingHorizontal: 16,
                    }}>
                    <View style={{paddingTop: 16}}>
                      <JioText
                        text="Refund Breakup"
                        appearance={JioTypography.BODY_S_BOLD}
                        color={'primary_grey_100'}
                        style={{paddingVertical: 8}}
                      />
                      <JMTable
                        data={refundBreakup}
                        style={{
                          marginTop: 4,
                        }}
                        cellStyle={{
                          paddingVertical: 8,
                        }}
                        columnStyle={{
                          0: {
                            width: rw(128),
                          },
                        }}
                        ItemSeparatorComponent={tableItemSeprator}
                      />
                      {bankRef ? (
                        <JMMessageBox
                          title={{
                            text: `Bank Reference Number: ${bankRef}`,
                            color: 'primary_grey_80',
                            appearance: 'body_xxs_bold',
                          }}
                          subTitle={{
                            text: 'for any other queries, please contact your bank using this number.',
                            color: 'primary_grey_80',
                            appearance: 'body_xxs',
                          }}
                          backgroundColor={'primary_grey_20'}
                          style={{
                            padding: 12,
                            borderRadius: 12,
                            marginTop: 12,
                          }}
                        />
                      ) : null}
                      {item?.itemDetails?.length > 0 ? (
                        <View style={{marginVertical: 32}}>
                          <JioText
                            text={`Items (${item?.itemDetails?.length})`}
                            appearance={JioTypography.BODY_XXS_BOLD}
                            color={'primary_grey_100'}
                            style={{marginBottom: 4}}
                          />
                          {item?.itemDetails
                            ?.slice(
                              0,
                              expand[index] ? item?.itemDetails?.length : 1,
                            )
                            ?.map((list, index) => {
                              return (
                                <>
                                  <JMShortCardDescribe
                                    key={index}
                                    image={{
                                      uri: list?.productImage,
                                    }}
                                    title={{
                                      text: list?.productName,
                                    }}
                                    qty={{
                                      text: `Refund Qty: ${list?.orderedQty}`,
                                    }}
                                    price={{
                                      text: `Refund: ₹${formatPrice(
                                        list?.orderedAmt,
                                      )}`,
                                    }}
                                  />
                                  {index !== item?.itemDetails?.length - 1 ? (
                                    <Divider
                                      type={DividerType.LARGE}
                                      color={'primary_grey_20'}
                                      top={DividerGap.GAP24}
                                      bottom={DividerGap.GAP12}
                                      horizontal={-DividerGap.GAP16}
                                    />
                                  ) : null}
                                </>
                              );
                            })}
                        </View>
                      ) : null}
                      {item?.itemDetails?.length > 1 ? (
                        <JioButton
                          title={expand[index] ? 'View less' : 'View more'}
                          kind={ButtonKind.TERTIARY}
                          stretch
                          style={{marginBottom: 12}}
                          onClick={() => {
                            setExpand(prev => {
                              return {
                                ...prev,
                                [index]: !prev[index],
                              };
                            });
                          }}
                        />
                      ) : null}
                    </View>
                  </AccordionPanel>
                );
              })}
            </Accordion>
          </View>
        );
      default:
        return null;
    }
  };

  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={bean => (
        <ScreenSlot
          navigationBean={bean}
          navigation={navigation}
          children={_ => {
            return (
              <FlatList
                data={config?.block}
                renderItem={renderItem}
                style={{backgroundColor: '#ffffff', flex: 1}}
              />
            );
          }}
        />
      )}
    />
  );
};

export default JMRefundDetailScreen;
