import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import type {NavigationStackData} from '../../jiomart-common/src/JMNavGraphUtil';
import {AppScreens} from '../../jiomart-common/src/JMAppScreenEntry';
import JMOrderListScreen from './screens/JMOrderListScreen';
import JMRefundDetailScreen from './screens/JMRefundDetailScreen';
import { Platform } from 'react-native';

const Stack = createNativeStackNavigator<NavigationStackData>();

const JMOrderNavigation = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: Platform.OS==="ios" ? false : true,
      }}>
      <Stack.Screen
        name={AppScreens.ORDER_LIST_SCREEN}
        component={JMOrderListScreen}
      />
      <Stack.Screen
        name={AppScreens.REFUND_DETAIL_SCREEN}
        component={JMRefundDetailScreen}
      />
    </Stack.Navigator>
  );
};

export default JMOrderNavigation;
