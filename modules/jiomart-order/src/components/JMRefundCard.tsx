import {
  Pressable,
  View,
  type StyleProp,
  type ViewStyle,
} from 'react-native';
import React from 'react';
import {JioIcon, JioText} from '@jio/rn_components';
import {
  IconColor,
  JioTypography,
  type JioIconProps,
  type JioTextProps,
} from '@jio/rn_components/src/index.types';

interface JMRefundCardProps {
  style?: StyleProp<ViewStyle>;
  headerTitle?: JioTextProps;
  icon?: JioIconProps;
  title?: JioTextProps;
  subTitle?: JioTextProps;
  onPress?: () => void;
}

const JMRefundCard = (props: JMRefundCardProps) => {
  const {headerTitle, style, icon, title, subTitle, onPress} = props;
  return (
    <Pressable style={style} onPress={onPress}>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        <JioText
          style={{
            paddingVertical: 6,
          }}
          appearance={JioTypography.BODY_XS_BOLD}
          color={'primary_grey_80'}
          {...headerTitle}
        />
        <JioIcon
          ic="IcChevronRight"
          color={IconColor.PRIMARY60}
          style={{padding: 4}}
          {...icon}
        />
      </View>
      <JioText
        style={{
          paddingVertical: 3,
        }}
        appearance={JioTypography.BODY_XS}
        color={'primary_grey_80'}
        {...title}
      />
      <JioText
        style={{
          paddingVertical: 3,
        }}
        appearance={JioTypography.BODY_XS}
        color={'primary_grey_80'}
        {...subTitle}
      />
    </Pressable>
  );
};

export default JMRefundCard;
