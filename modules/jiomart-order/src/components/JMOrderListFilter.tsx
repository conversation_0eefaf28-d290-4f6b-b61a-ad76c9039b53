import {FlatList, Platform, StyleSheet, Pressable, View} from 'react-native';
import React, {useState} from 'react';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import JMBtmSheetHeader from '../../../jiomart-general/src/ui/JMBtmSheetHeader';
import type {BottomSheetChildren} from '../../../jiomart-general/src/ui/BottomSheet/types/BottomSheetType';
import Divider, {DividerType} from '../../../jiomart-general/src/ui/Divider';
import {JioIcon, JioText, useColor} from '@jio/rn_components';
import {
  IconColor,
  IconSize,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import {TapGestureHandler} from 'react-native-gesture-handler';
import type {IconKey} from '@jio/rn_components/src/utils/IconUtility';
import {capitalizeFirstLetter} from '../../../jiomart-common/src/utils/JMStringUtility';
import {formatCategoryName} from '../../../jiomart-common/src/utils/JMCommonFunctions';

interface JMOrderListFilterProps extends BottomSheetChildren {
  config: any;
  onClose?: () => void;
  data?: any[];
  selectedTab?: string;
  onFilterTab?: (val: string) => void;
  selected?: any;
  onApply?: (val: any) => void;
  onClearAll?: (val: any) => void;
}

const JMOrderListFilter = (props: JMOrderListFilterProps) => {
  const {
    close,
    onClose,
    data,
    selectedTab,
    onFilterTab,
    onApply,
    onClearAll,
    selected,
    config,
  } = props;
  const insets = useSafeAreaInsets();
  const grey20 = useColor('primary_grey_20');
  const white = useColor('primary_inverse');
  const grey40 = useColor('primary_grey_40');
  const primary50 = useColor('primary_50');

  const [selectedFilters, setSelectedFilters] = useState<any>(selected);

  const isFilterApplied = () => {
    return Object.values(selectedFilters || {}).some(
      val => val !== undefined && val !== null && val !== '',
    );
  };

  const renderFilterTab = ({item}: {item: any; index: number}) => {
    return (
      <Pressable
        style={[
          styles.tab,
          selectedTab === item.key.name
            ? {
                backgroundColor: white,
              }
            : null,
        ]}
        onPress={() => {
          onFilterTab?.(item.key.name);
        }}>
        <JioText
          text={item.key.display ?? ''}
          appearance={
            selectedTab === item.key.name
              ? JioTypography.BODY_XXS_BOLD
              : JioTypography.BODY_XXS
          }
          maxLines={2}
          color={selectedTab === item.key.name ? 'black' : 'primary_grey_80'}
          style={{flexShrink: 1}}
        />
        {selectedFilters?.[item.key.name] && <View style={styles.badge} />}
      </Pressable>
    );
  };

  const handleFilterSelect = (tab, value) => {
    setSelectedFilters((prev: any) => {
      return {
        ...prev,
        [tab]: value,
      };
    });
  };

  const renderFilterOption = ({
    item: value,
    index,
  }: {
    item: any;
    index: number;
  }) => {
    return (
      <FilterOption
        key={`value-${index}`}
        title={capitalizeFirstLetter(formatCategoryName(value?.value))}
        type={FilterOptionType.RADIO}
        value={
          selectedFilters?.[selectedTab as string] === value.key
            ? FilterOptionValue.SELECTED
            : FilterOptionValue.UNSELECTED
        }
        onPress={() => {
          handleFilterSelect(`${selectedTab}`, value.key);
        }}
      />
    );
  };

  const handleClearAll = () => {
    setSelectedFilters({});
    close?.(() => onClearAll?.({}));
  };

  const handleApply = () => {
    close?.(() => onApply?.(selectedFilters));
  };

  return (
    <View style={{flex: 1, paddingBottom: insets.bottom}}>
      <JMBtmSheetHeader
        title={config?.title ?? ''}
        onPress={() => {
          close?.(onClose);
        }}
      />
      <View style={styles.filterContainer}>
        <View style={[styles.leftPanel, {backgroundColor: grey20}]}>
          <FlatList
            data={data}
            renderItem={renderFilterTab}
            keyExtractor={(_, index) => `filter-tab-${index}`}
            showsVerticalScrollIndicator={false}
            bounces={false}
            initialNumToRender={20}
          />
        </View>
        <View style={styles.rightPanel}>
          <FlatList
            data={data?.find(f => f.key.name === selectedTab)?.values}
            renderItem={renderFilterOption}
            keyExtractor={(_, index) => `filter-option-${index}`}
            showsVerticalScrollIndicator={false}
            bounces={false}
            initialNumToRender={20}
            contentContainerStyle={styles.rightPanelContent}
          />
        </View>
      </View>
      <Divider type={DividerType.THIN} />
      <View style={styles.filterFooter}>
        {config?.button?.clearAll?.isVisible ? (
          <Pressable
            style={[
              styles.clear,
              {borderColor: grey40, opacity: isFilterApplied() ? 1 : 0.4},
            ]}
            disabled={!isFilterApplied()}
            onPress={handleClearAll}>
            <JioText
              text={config?.button?.clearAll?.title ?? ''}
              appearance={JioTypography.BUTTON}
              color={isFilterApplied() ? 'primary_60' : 'primary_grey_60'}
            />
          </Pressable>
        ) : null}
        {config?.button?.apply?.isVisible ? (
          <Pressable
            style={[
              styles.apply,
              {
                backgroundColor: primary50,
                opacity: isFilterApplied() ? 1 : 0.4,
              },
            ]}
            disabled={!isFilterApplied()}
            onPress={handleApply}>
            <JioText
              text={config?.button?.apply?.title ?? ''}
              appearance={JioTypography.BUTTON}
              color="primary_inverse"
            />
          </Pressable>
        ) : null}
      </View>
    </View>
  );
};

export interface FilterOptionProps {
  title?: string;
  rightIcon?: RightIcon;
  showRightIcon?: boolean;
  type?: FilterOptionType;
  value?: FilterOptionValue;
  style?: any;
  disabled?: boolean;
  onPress?: () => void;
}

export enum FilterOptionType {
  CHECKBOX = 'checkbox',
  RADIO = 'radio',
  CAT = 'cat',
}

export enum FilterOptionValue {
  SELECTED = 'selected',
  UNSELECTED = 'unselected',
  PARTIAL = 'partial',
}

interface RightIcon {
  ic: string;
  color: string;
  size?: string;
}

const FilterOption = (props: FilterOptionProps) => {
  const {
    title,
    rightIcon,
    showRightIcon,
    type = FilterOptionType.RADIO,
    value,
    style,
    disabled,
    onPress,
  } = props;

  const primary50 = useColor('primary_50');
  const grey80 = useColor('primary_grey_80');

  return title ? (
    <>
      <TapGestureHandler onActivated={onPress} disabled={disabled}>
        <View style={[styles.filterOption, disabled ? styles.disabled : null]}>
          {(() => {
            switch (type) {
              case FilterOptionType.CHECKBOX:
                switch (value) {
                  case FilterOptionValue.SELECTED:
                    return (
                      <View
                        style={[styles.checkbox, {backgroundColor: primary50}]}>
                        <JioIcon
                          ic="IcConfirm"
                          size={IconSize.XS}
                          color={IconColor.INVERSE}
                        />
                      </View>
                    );
                  case FilterOptionValue.UNSELECTED:
                    return <View style={styles.unSelectedCheckbox} />;
                  case FilterOptionValue.PARTIAL:
                    return (
                      <View
                        style={[styles.checkbox, {backgroundColor: primary50}]}>
                        <JioIcon
                          ic="IcMinus"
                          size={IconSize.XS}
                          color={IconColor.INVERSE}
                        />
                      </View>
                    );
                  default:
                    return null;
                }
              case FilterOptionType.RADIO:
                switch (value) {
                  case FilterOptionValue.SELECTED:
                    return (
                      <View
                        style={[styles.selectedRadio, {borderColor: primary50}]}
                      />
                    );
                  case FilterOptionValue.UNSELECTED:
                    return (
                      <View
                        style={[styles.unSelectedRadio, {borderColor: grey80}]}
                      />
                    );
                  default:
                    return null;
                }
              default:
                return null;
            }
          })()}

          <JioText
            text={title ?? ''}
            appearance={JioTypography.BODY_XXS}
            color={
              value === FilterOptionValue.SELECTED ||
              value === FilterOptionValue.PARTIAL
                ? 'primary_grey_100'
                : 'primary_grey_80'
            }
            style={styles.title}
            maxLines={1}
          />

          {showRightIcon && rightIcon ? (
            <JioIcon
              ic={rightIcon.ic as IconKey}
              color={rightIcon.color as IconColor}
              size={rightIcon.size as IconSize}
              style={styles.rightIcon}
            />
          ) : null}
        </View>
      </TapGestureHandler>
      <Divider type={DividerType.THIN} />
    </>
  ) : null;
};

export default JMOrderListFilter;

const styles = StyleSheet.create({
  filterOption: {
    flexDirection: 'row',
    columnGap: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  unSelectedCheckbox: {
    borderWidth: 0.45,
    width: 16,
    height: 16,
    borderRadius: 4,
  },
  checkbox: {
    width: 16,
    height: 16,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  unSelectedRadio: {
    borderWidth: 1,
    width: 16,
    height: 16,
    borderRadius: 100,
  },
  selectedRadio: {
    borderWidth: 4,
    width: 16,
    height: 16,
    borderRadius: 100,
  },
  title: {marginRight: 'auto', flexShrink: 1},
  rightIcon: {paddingHorizontal: 4},
  category: {paddingLeft: 16},
  bottom: {paddingBottom: 24},
  search: {
    paddingHorizontal: 12,
    paddingVertical: Platform.OS === 'android' ? 0 : 8,
    borderRadius: 100,
    columnGap: 12,
    flexDirection: 'row',
    marginBottom: 8,
    alignItems: 'center',
  },
  leftPanel: {flex: 11},
  rightPanel: {
    flex: 19,
    paddingRight: 8,
  },
  rightPanelContent: {
    paddingTop: 8,
  },
  filterContainer: {
    flexDirection: 'row',
    marginTop: 8,
    columnGap: 8,
    marginRight: 16,
    flex: 1,
  },
  container: {flex: 1},
  filterFooter: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    flexDirection: 'row',
    columnGap: 16,
    backgroundColor: '#ffff',
  },
  clear: {
    borderWidth: 1,
    paddingVertical: 4,
    paddingHorizontal: 16,
    alignSelf: 'stretch',
    borderRadius: 100,
    alignItems: 'center',
    flex: 1,
  },
  apply: {
    paddingVertical: 4,
    paddingHorizontal: 16,
    alignSelf: 'stretch',
    borderRadius: 100,
    alignItems: 'center',
    flex: 1,
  },
  tab: {
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    columnGap: 7,
    // borderRadius: 4,
    // marginHorizontal: 4,
  },
  disabled: {opacity: 0.5},
  textinput: {
    fontSize: 16,
    flex: 1,
    height: Platform.OS === 'android' ? 40 : undefined,
    lineHeight: 24,
  },
  badge: {
    marginLeft: 'auto',
    borderRadius: 100,
    borderWidth: 4,
    borderColor: '#FC6770',
  },
});
