import {StyleSheet, View} from 'react-native';
import React from 'react';
import FastImage from 'react-native-fast-image';
import {rw} from '../../../jiomart-common/src/JMResponsive';
import {JioText} from '@jio/rn_components';
import {
  JioTypography,
  type JioTextProps,
} from '@jio/rn_components/src/index.types';

interface JMShortCardDescribeProps {
  image?: any;
  title?: JioTextProps;
  qty?: JioTextProps;
  price?: JioTextProps;
}

const JMShortCardDescribe = (props: JMShortCardDescribeProps) => {
  const {image, title, qty, price} = props;
  return (
    <View style={{flexDirection: 'row', columnGap: 12}}>
      <FastImage
        source={image}
        style={{
          width: rw(64),
          height: rw(64),
          borderRadius: 12,
        }}
      />
      <View style={{rowGap: 4, flexShrink: 1}}>
        <JioText
          appearance={JioTypography.BODY_XS}
          maxLines={1}
          color={'primary_grey_100'}
          {...title}
        />
        <JioText
          appearance={JioTypography.BODY_XXS}
          maxLines={1}
          color={'primary_grey_100'}
          {...qty}
        />
        <JioText 
         appearance={JioTypography.BODY_XS}
        maxLines={1}
        color={'primary_grey_100'}
        {...price} />
      </View>
    </View>
  );
};

export default JMShortCardDescribe;

const styles = StyleSheet.create({});
