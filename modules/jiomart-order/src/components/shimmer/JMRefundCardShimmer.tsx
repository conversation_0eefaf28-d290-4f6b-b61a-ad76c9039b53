import {View} from 'react-native';
import React from 'react';
import JMShimmer from '../../../../jiomart-general/src/ui/JMShimmer';
import {ShimmerKind} from '@jio/rn_components/src/index.types';
import {rh, rw} from '../../../../jiomart-common/src/JMResponsive';

const JMRefundCardShimmer = () => {
  return (
    <View style={{rowGap: 12, paddingHorizontal: 16}}>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}>
        <JMShimmer
          kind={ShimmerKind.PARAGRAPH}
          width={rw(275)}
          height={rh(20)}
          style={{}}
        />
        <JMShimmer
          kind={ShimmerKind.PARAGRAPH}
          width={rw(24)}
          height={rw(24)}
          style={{}}
        />
      </View>
      <JMShimmer
        kind={ShimmerKind.PARAGRAPH}
        width={rw(250)}
        height={rh(20)}
        style={{}}
      />
      <JMShimmer
        kind={ShimmerKind.PARAGRAPH}
        width={rw(250)}
        height={rh(20)}
        style={{}}
      />
    </View>
  );
};

export default JMRefundCardShimmer;
