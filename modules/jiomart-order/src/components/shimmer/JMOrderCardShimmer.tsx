import {View} from 'react-native';
import React from 'react';
import JMShimmer from '../../../../jiomart-general/src/ui/JMShimmer';
import {ShimmerKind} from '@jio/rn_components/src/index.types';
import {rh, rw} from '../../../../jiomart-common/src/JMResponsive';

const JMOrderCardShimmer = () => {
  return (
    <View style={{rowGap: 12, paddingHorizontal: 16}}>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}>
        <JMShimmer
          kind={ShimmerKind.PARAGRAPH}
          width={rw(275)}
          height={rh(20)}
          style={{}}
        />
        <JMShimmer
          kind={ShimmerKind.PARAGRAPH}
          width={rw(24)}
          height={rw(24)}
          style={{}}
        />
      </View>
      <View style={{flexDirection: 'row', columnGap: 16}}>
        <JMShimmer
          kind={ShimmerKind.PARAGRAPH}
          width={rw(64)}
          height={rw(64)}
          style={{}}
        />
        <View style={{rowGap: 4}}>
          <JMShimmer
            kind={ShimmerKind.PARAGRAPH}
            width={rw(250)}
            height={rh(20)}
            style={{}}
          />
          <JMShimmer
            kind={ShimmerKind.PARAGRAPH}
            width={rw(250)}
            height={rh(20)}
            style={{}}
          />
          <JMShimmer
            kind={ShimmerKind.PARAGRAPH}
            width={rw(250)}
            height={rh(20)}
            style={{}}
          />
        </View>
      </View>
      <JMShimmer
        kind={ShimmerKind.PARAGRAPH}
        width={rw(312)}
        height={rh(32)}
        style={{alignSelf: 'center'}}
      />
    </View>
  );
};

export default JMOrderCardShimmer;
