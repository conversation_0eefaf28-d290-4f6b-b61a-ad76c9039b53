import {AppState, AppStateStatus, Platform} from 'react-native';
import {useEffect, useRef} from 'react';
import useAddressOperation from '../../../jiomart-address/src/hooks/useAddressOperation';
import useCurrentLocation from '../../../jiomart-address/src/hooks/useCurrentLocation';
import {addStringPref} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '../../../jiomart-common/src/JMConstants';
import {JMAddressModel} from '../../../jiomart-common/src/uiModals/JMAddressModel';
import {
  checkIsLocationPermissionGranted,
  checkIsLocationEnabled,
} from '../../../jiomart-common/src/utils/JMLocationUtility';
import {JMLogger} from '../../../jiomart-common/src/utils/JMLogger';
import {JMDatabaseManager} from '../../../jiomart-networkmanager/src/db/JMDatabaseManager';
import {useGlobalState} from '../../../jiomart-general/src/context/JMGlobalStateProvider';
import { JMSharedViewModel } from '../../../jiomart-common/src/JMSharedViewModel';
import { PlatformType } from '../../../jiomart-common/src/JMObjectUtility';
import useGlobalBottomSheetController from '../features/StartUp/controllers/useGlobalBottomSheetController';

const useAppStatePermissionSync = () => {
  const appState = useRef(AppState.currentState);
  const {setAddress} = useGlobalState();
  const {fetchLocationFromReverseGeoCodeFromLatLong} = useCurrentLocation({
    alertBlocked: {title: ''},
  });
  const {checkAndSetPincode} = useAddressOperation();

  const {checkAndOpenNextSheet} = useGlobalBottomSheetController();

  useEffect(() => {
    if(Platform.OS === PlatformType.ANDROID) return 
    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => {
      subscription.remove();
    };
  }, []);

  const setCurrentPincodeIfLocationIsEnabled = async () => {
    const isLocationPermissionGranted =
      await checkIsLocationPermissionGranted();
    const isLocationEnabled = await checkIsLocationEnabled();
    if (isLocationPermissionGranted && isLocationEnabled) {
      const stats = await fetchLocationFromReverseGeoCodeFromLatLong(true, true);
      JMLogger.log(
        'checkAndSetPincode ' +
          'setCurrentPincodeIfLocationIsEnabled ' +
          stats?.address?.pin,
      );
      if (stats?.address?.pin) {
        await checkAndSetPincode({
          pincode: stats?.address?.pin,
          state: stats?.address?.state,
          city: stats?.address?.city,
        });
        handleAddress();
      }
    }
    await checkAndOpenNextSheet();
  };


  const handleAppStateChange = async (nextAppState: AppStateStatus) => {
    console.log('handleAppStateChange  '+appState.current + " permission Given "+JMSharedViewModel.Instance.getPermissionGivenStatus());
    if (
      appState.current === "background" &&
      nextAppState === 'active' && JMSharedViewModel.Instance.getPermissionGivenStatus() === false
    ) {
      // App came to foreground
      console.log('App has come to the foreground, check permission again');
      await setCurrentPincodeIfLocationIsEnabled();
    }

    appState.current = nextAppState;
  };

  const handleAddress = async (initialAddress?: JMAddressModel) => {
    let address: any = await JMDatabaseManager.address.getDefaultAddress();
    JMLogger.log('handleAddress ', 'handleAddress ' + JSON.stringify(address));
    if (!address) {
      address = initialAddress;
      if (initialAddress) {
        addStringPref(
          AsyncStorageKeys.X_LOCATION_DETAIL,
          JSON.stringify(initialAddress),
        );
      }
    } else {
      address = JSON.parse(address ?? '');
    }
    setAddress(address);
  };
};

export default useAppStatePermissionSync;
