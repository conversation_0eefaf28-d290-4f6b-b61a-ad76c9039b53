import {useEffect, useState} from 'react';
import JMUserApiNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMUserAPINetworkController';
import useCartOperation from '../../../jiomart-cart/src/hooks/useCartOperation';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import {JMLogger} from '../../../jiomart-common/src/utils/JMLogger';
import {useGlobalState} from '../../../jiomart-general/src/context/JMGlobalStateProvider';
import {
  useAppStartupConfig,
} from '../../../jiomart-general/src/hooks/useJMConfig';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import type {JMAddressModel} from '../../../jiomart-common/src/uiModals/JMAddressModel';
import {JMDatabaseManager} from '../../../jiomart-networkmanager/src/db/JMDatabaseManager';
import JMDataLoaderNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMDataLoaderNetworkController';
import useWishlistOperation from '../../../jiomart-wishlist/src/hooks/useWishlistOperation';
import {addStringPref} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {
  AsyncStorageKeys,
  EventEmitterKeys,
} from '../../../jiomart-common/src/JMConstants';
import useCurrentLocation from '../../../jiomart-address/src/hooks/useCurrentLocation';
import useAddressOperation from '../../../jiomart-address/src/hooks/useAddressOperation';
import {
  checkIsLocationEnabled,
  checkIsLocationPermissionGranted,
} from '../../../jiomart-common/src/utils/JMLocationUtility';
import {
  PlatformType,
  isNullOrUndefinedOrEmpty,
} from '../../../jiomart-common/src/JMObjectUtility';
import {getConfigFileDataDbAsync} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileManager';
import JMInAppBannerController from '../../../jiomart-inappbanner/src/controller/JMInAppBannerController';
import useNativeCacheHook from '../../../jiomart-general/src/native_cache/useNativeCacheHook';
import {Platform} from 'react-native';
import { setJMCommonFlags } from '../../../jiomart-common/src/utils/JMCommonFunctions';
import JMDeviceInfoData from '../../../jiomart-common/src/JMDeviceInfo';
import { SendWebJavaScriptFunctions } from '../../../jiomart-general/src/ui/JMScreenSlot';
const userApiNetworkController = new JMUserApiNetworkController();
const dataLoaderNetworkController = new JMDataLoaderNetworkController();
const useJMInitialisationController = () => {
  const [splashJsonData, setSplashJsonData] = useState(
    JMSharedViewModel.Instance.getSplashJsonData(),
  );
  useEffect(() => {
    JMDeviceInfoData.getAllDeviceInfo();
    getConfigFileDataDbAsync(JMConfigFileName.JMSplashScreenFileName).then(
      splashData => {
        if (!JMSharedViewModel.Instance.getSplashJsonData()) {
          setSplashJsonData(splashData?.toString());
        }
      },
    );
  }, []);
  const startupConfig: any = useAppStartupConfig([
    JMConfigFileName.JMSplashScreenFileName,
    JMConfigFileName.JMCommonContentFileName,
    JMConfigFileName.JMAddressConfigurationFileNAme,
  ]);

  const commonContentConfig =
    startupConfig?.[JMConfigFileName.JMCommonContentFileName];

  useEffect(() => {
    JMInAppBannerController.fetchInAppBannerData();
  }, []);

  const {setUserInitials, setAddress, setEvent, setQCDetails} =
    useGlobalState();
  const {handleNativeCache} = useNativeCacheHook();
  useEffect(() => {
    const changeDefaultAddress = async () => {
      if (commonContentConfig?.location) {
        const defaultAddress =
          await JMDatabaseManager.address.getDefaultAddress();
        if (isNullOrUndefinedOrEmpty(defaultAddress)) {
          JMDatabaseManager.address.setIntialAddress({
            ...commonContentConfig?.location,
            pin: commonContentConfig?.location?.pincode,
          });
          await handleAddress({
            ...commonContentConfig?.location,
            pin: commonContentConfig?.location?.pincode,
          });
          SendWebJavaScriptFunctions(setEvent, undefined, "addressModifiedOnNative", {
            pincodeModifiedOnNative: `'${JSON.stringify({
              pin: commonContentConfig?.location?.pincode,
            })}'`,
          })
        } else {
          await handleAddress({
            ...commonContentConfig?.location,
            pin: commonContentConfig?.location?.pincode,
          });
        }
      }
    };
    if(commonContentConfig?.jmFlags){
      setJMCommonFlags(commonContentConfig)
    }
    changeDefaultAddress();
  }, [commonContentConfig?.location]);

  useEffect(() => {
    setQCDetails({
      qcMessage: "",
      scheduledMessage:
        commonContentConfig?.quickCommerceConfig?.scheduledDeliveryMessage,
    });
  }, [commonContentConfig]);

  const handleAddress = async (initialAddress?: JMAddressModel) => {
    let address: any = await JMDatabaseManager.address.getDefaultAddress();
    JMLogger.log('handleAddress ', 'handleAddress ' + JSON.stringify(address));
    if (!address) {
      address = initialAddress;
      if(initialAddress){
        addStringPref(
          AsyncStorageKeys.X_LOCATION_DETAIL,
          JSON.stringify(initialAddress),
        );
      }
    } else {
      address = JSON.parse(address ?? '');
    }
    setAddress(address);
  };

  const {getCart} = useCartOperation();
  const {getWishlistIds} = useWishlistOperation();

  const callSessionApis = async () => {
    try {
      JMLogger.log('callIntialApis');
      const guestUserSession =
        await JMDatabaseManager.user.getGuestUserSession();
      const userSession = await JMDatabaseManager.user.getUserSession();
      if (!guestUserSession && !userSession) {
        if (Platform.OS === PlatformType.ANDROID) {
          await handleNativeCache();
        }
        const cachedUserSession = await JMDatabaseManager.user.getUserSession();
        if (!cachedUserSession) {
          await userApiNetworkController.fetchGuestUserSession();
        }
      }
      await setUserInitialsToGlobal();
      await dataLoaderNetworkController.fetchDataLoader();
      getCart.mutate();
      if (JMDatabaseManager.user.isUserLoggedInFlag()) {
        getWishlistIds.mutate();
      }
    } catch (error) {
      throw error;
    }
  };
  useEffect(() => {
    callSessionApis();
  }, []);

  async function setUserInitialsToGlobal() {
    const userDetailsString = await JMDatabaseManager.user.getUserDetails();
    if (userDetailsString) {
      const userDetails = JSON.parse(userDetailsString);
      if (userDetails) {
        JMSharedViewModel.Instance.setLoggedInStatus(userDetails !== null);
        if (JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU)
          setUserInitials(userDetails?.full_name);
        else setUserInitials(userDetails?.full_name);
      }
    }
  }

  const {fetchLocationFromReverseGeoCodeFromLatLong} = useCurrentLocation({
    alertBlocked: {title: ''},
  });
  const {checkAndSetPincode, setDefaultAddressFromList} = useAddressOperation();

  useEffect(() => {
    const setCurrentPincodeIfLocationIsEnabled = async () => {
      const isLocationPermissionGranted =
        await checkIsLocationPermissionGranted();
      const isLocationEnabled = await checkIsLocationEnabled();
      if (isLocationPermissionGranted && isLocationEnabled) {
        const stats = await fetchLocationFromReverseGeoCodeFromLatLong(true);
        JMLogger.log(
          'checkAndSetPincode ' +
            'setCurrentPincodeIfLocationIsEnabled ' +
            stats?.address?.pin,
        );
        if (stats?.address?.pin) {
          const request = await checkAndSetPincode({
            pincode: stats?.address?.pin,
            state: stats?.address?.state,
            city: stats?.address?.city,
          });
          if (request) {
            await setDefaultAddressFromList(request);
          }
          handleAddress();
        }
      }
    };
    setCurrentPincodeIfLocationIsEnabled();
  }, []);

  return {splashJsonData};
};

export default useJMInitialisationController;
