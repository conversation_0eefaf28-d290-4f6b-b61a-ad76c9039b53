import React from 'react';
import {useGlobalState} from '../../../../jiomart-general/src/context/JMGlobalStateProvider';
import BottomSheet from '../../../../jiomart-general/src/ui/BottomSheet/BottomSheet';
import useGlobalBottomSheetController from './controllers/useGlobalBottomSheetController';
import PermissionBottomSheet from './PermissionBottomSheet';
import PrivacyPolicyBottomSheet from './PrivacyPolicyBottomSheet';
import {GlobalBottomSheetProps} from './types/GlobalBottomSheetType';
import {useEffect} from 'react';
import JMInAppBannerSheet from '../../../../jiomart-inappbanner/src/views/JMInAppBannerSheet';
import EnableLocationBtmSheet from './EnableLocationBtmSheet';

const GlobalBottomSheet = (props: GlobalBottomSheetProps) => {
  const {
    openPincodeBottomSheet,
    openQuickBtmSheet,
    quickBottomSheetIdentifierBean,
    navigation,
  } = props;
  const {
    permissionConfig,
    privacyPolicyConfig,
    checkAndOpenNextSheet,
    handlePermission,
    enableLocationConfig
  } = useGlobalBottomSheetController();
  const {
    permissionBtmSheet,
    privacyPolicyBtmSheet,
    softUpdateBtmSheet,
    setPermissionBtmSheet,
    setPrivacyPolicyBtmSheet,
    setSoftUpdateBtmSheet,
    pincodeBtmSheet,
    setPincodeBtmSheet,
    quickBtmSheet,
    setQuickBtmSheet,
    inAppBanner,
    setInAppBanner,
    enableLocationBtmSheet,
    setEnableLocationBtmSheet
  } = useGlobalState();

  useEffect(() => {
    if (pincodeBtmSheet) {
      openPincodeBottomSheet(true);
      setPincodeBtmSheet(false);
    }
  }, [pincodeBtmSheet]);

  useEffect(() => {
    if (
      quickBtmSheet &&
      quickBottomSheetIdentifierBean &&
      quickBottomSheetIdentifierBean?.length > 0
    ) {
      openQuickBtmSheet();
      setQuickBtmSheet(false);
    }
  }, [quickBtmSheet, quickBottomSheetIdentifierBean]);

  return (
    <>
      {permissionConfig?.isVisible ? (
        <BottomSheet
          visible={permissionBtmSheet}
          disabledBackDropClick
          disableBlurGain
          disabledGesture>
          <PermissionBottomSheet
            permissionConfigData={permissionConfig}
            onProceed={async () => {
              setPermissionBtmSheet(false);
              if (await handlePermission()) {
                checkAndOpenNextSheet();
              }
            }}
          />
        </BottomSheet>
      ) : null}
      {privacyPolicyConfig?.isVisible ? (
        <BottomSheet
          visible={privacyPolicyBtmSheet}
          disabledBackDropClick
          disableBlurGain
          disabledGesture>
          <PrivacyPolicyBottomSheet
            onClose={() => {
              setPrivacyPolicyBtmSheet(false);
              checkAndOpenNextSheet();
            }}
            onAccept={() => {
              setPrivacyPolicyBtmSheet(false);
              checkAndOpenNextSheet();
            }}
            configData={privacyPolicyConfig}
          />
        </BottomSheet>
      ) : null}
      {enableLocationConfig?.isVisible ? (
        <BottomSheet
          visible={enableLocationBtmSheet}
          disabledBackDropClick
          disableBlurGain
          disabledGesture>
          <EnableLocationBtmSheet
            onNavigate={()=>{
              setEnableLocationBtmSheet(false);
            }}
            onClose={() => {
              setEnableLocationBtmSheet(false);
              checkAndOpenNextSheet();
            }}
            configData={enableLocationConfig}
          />
        </BottomSheet>
      ) : null}
      {inAppBanner ? (
        <JMInAppBannerSheet
          navigation={navigation}
          {...inAppBanner}
          bannerDetail={{
            ...inAppBanner?.bannerDetail,
            onBackDropClick: () => {
              checkAndOpenNextSheet();
            },
            onPress: () => {
              checkAndOpenNextSheet();
            },
            onClose: () => {
              checkAndOpenNextSheet();
            },
          }}
        />
      ) : null}

      {/* <BottomSheet
        visible={softUpdateBtmSheet}
        disabledBackDropClick={softUpdateConfig?.immediate_update}
        disabledGesture={softUpdateConfig?.immediate_update}
        disableBlurGain
        onBackDropClick={() => {
          softUpdateRef.current?.close();
          checkAndHideBnb();
          checkAndOpenNextSheet();
        }}
        onDrag={() => {
          softUpdateRef.current?.close();
          checkAndHideBnb();
          checkAndOpenNextSheet();
        }}>
        <SoftUpdateBtmSheet
          config={softUpdateConfig}
          onClose={() => {
            if (softUpdateConfig?.immediate_update) {
              return;
            }
              checkAndHideBnb();
            softUpdateRef.current?.close();
            checkAndOpenNextSheet();
          }}
          onButtonPress={() => {softUpdateApp(); checkAndHideBnb();}}
        />
      </BottomSheet>
    */}
    </>
  );
};

export default GlobalBottomSheet;
