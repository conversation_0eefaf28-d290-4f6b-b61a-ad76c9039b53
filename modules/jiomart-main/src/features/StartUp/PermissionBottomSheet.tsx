import React from 'react';
import {Ji<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, JioText} from '@jio/rn_components';
import {
  ButtonKind,
  ButtonSize,
  IconColor,
  IconSize,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import {Platform, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import JMBtmSheetHeader from '../../../../jiomart-general/src/ui/JMBtmSheetHeader';
import {styles} from './styles/PermissionBottomSheetStyle';
import type {
  PermissionBottomSheetProps,
  PermissionItem,
} from './types/PermissionBottomSheetType';

const PermissionBottomSheet = (props: PermissionBottomSheetProps) => {
  const {onProceed, permissionConfigData, close} = props;
  const insets = useSafeAreaInsets();
  return (
    <View style={{paddingBottom: insets.bottom}}>
      <JMBtmSheetHeader title={permissionConfigData?.title} hideClose />
      <View style={styles.container}>
        <JioText
          text={permissionConfigData?.subTitle}
          appearance={JioTypography.BODY_XS}
          color={'primary_grey_80'}
        />
        <>
          {permissionConfigData?.permissions?.[Platform.OS]?.map(
            (item: PermissionItem, index: number) => (
              <View key={`permission-${index}`} style={styles.cardStyle}>
                <View style={styles.cardIconStyle}>
                  <JioIcon
                    color={IconColor.GREY80}
                    size={IconSize.MEDIUM}
                    {...item?.permissionIcon}
                  />
                </View>
                <View style={styles.permissionText}>
                  <JioText
                    appearance={JioTypography.BODY_S_BOLD}
                    color={'black'}
                    {...item?.permissionTitle}
                  />
                  <JioText
                    appearance={JioTypography.BODY_XXS}
                    color={'primary_grey_80'}
                    {...item?.permissionSubTitle}
                  />
                </View>
              </View>
            ),
          )}
          <JioButton
            title={permissionConfigData?.buttonText}
            size={ButtonSize.MEDIUM}
            kind={ButtonKind.PRIMARY}
            stretch={true}
            onClick={async () => {
              close?.(onProceed);
            }}
            innerStyle={styles.btnInnerStyle}
            style={styles.procced}
          />
        </>
      </View>
    </View>
  );
};

export default PermissionBottomSheet;
