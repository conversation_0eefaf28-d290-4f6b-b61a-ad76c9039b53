import type {BottomSheetChildren} from '../../../../../jiomart-general/src/ui/BottomSheet/types/BottomSheetType';

export interface PrivacyPoliciesDataTypes {
  title: string;
  isVisible: boolean;
  regularString: string;
  clickableString: string;
  redirectionUrl: string;
  openInBrowser: boolean;
  buttonText: string;
}

export interface PrivacyPolicyBottomSheetProps extends BottomSheetChildren {
  onClose: () => void;
  onAccept: () => void;
  configData: PrivacyPoliciesDataTypes;
}
