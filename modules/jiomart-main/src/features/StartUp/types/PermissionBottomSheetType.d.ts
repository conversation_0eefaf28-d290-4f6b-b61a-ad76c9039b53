import type {
  JioIconProps,
  JioTextProps,
} from '@jio/rn_components/src/index.types';
import type {BottomSheetChildren} from '../../../../../jiomart-general/src/ui/BottomSheet/types/BottomSheetType';
import type {Platform} from 'react-native';

interface PermissionItem {
  permissionTitle: JioTextProps;
  permissionSubTitle: JioTextProps;
  permissionIcon: JioIconProps;
}

export interface PermissionsData {
  isVisible: boolean;
  title: string;
  subTitle: string;
  buttonText: string;
  permissions: Record<typeof Platform.OS, PermissionItem[]>;
}

export interface PermissionBottomSheetProps extends BottomSheetChildren {
  onProceed: () => void;
  permissionConfigData: PermissionsData;
}
