import type {BottomSheetChildren} from '../../../../../jiomart-general/src/ui/BottomSheet/types/BottomSheetType';

export interface EnableLocationDataTypes {
    isVisible: boolean;
    title: string;
    subTitle: string;
    enableLocationBtn: EnableLocationBtnDataType;
    selectLocationBtn: SelectLocationBtnDataType;
    locationPermission: LocationPermissionDataType;
    closeIcon: CloseIconDataType;
}

interface EnableLocationBtnDataType {
    isButtonVisible: boolean;
    buttonText: string;
}

interface SelectLocationBtnDataType {
    isButtonVisible: boolean;
    buttonText: string;
}

interface LocationPermissionDataType {
    ios: string[];
    android: string[];
}

interface CloseIconDataType {
  isIconVisible: boolean;
  iconAsset: string;
}

export interface EnableLocationBottomSheetProps extends BottomSheetChildren {
  onClose: () => void;
  onNavigate: () => void;
  configData: EnableLocationDataTypes;
}
