import {useCallback} from 'react';
import useConfigFile from '../../../../utilities/hooks/JMConfigFile';
import {GlobalRef} from '../../../constants/GlobalRefConstants';
import useGlobalRef from '../../../hooks/useGlobalRef';
import {openPlatformStoreNB} from '../../../NativeBridge/JMAppBridge';
import {AsyncStorageConstant} from '../../../constants/AsyncStorageConstants';
import {
  addStringInAsyncStorage,
  readDataInAsyncStorage,
} from '../../../utils/AsyncStorageFunctions';
import useEachAppLaunch from '../../../hooks/useEachAppLaunch';
import {useGLobalRefProvider} from '../../../context/GlobalRefContext';

const useSoftUpdateBottomSheetController = () => {
  const {globalRef} = useGlobalRef();
  const {appVersion, configData: SOFT_UPDATE_CONFIG}: any =
    useConfigFile('app_update_details');
  const {setKeyForEachLaunch, getValueForEachLaunch} = useEachAppLaunch();
  const {softUpdateRef} = useGLobalRefProvider();

  const softUpdateApp = useCallback(() => {
    softUpdateRef.current?.close();
    openPlatformStoreNB();
  }, [softUpdateRef]);

  const countAppLaunch = useCallback(async () => {
    let appLaunchIncrement = await readDataInAsyncStorage(
      AsyncStorageConstant.SOFT_UPDATE_INCREMENT,
    );
    let count: number = appLaunchIncrement ? Number(appLaunchIncrement) : 0;
    count++;

    await addStringInAsyncStorage(
      AsyncStorageConstant.SOFT_UPDATE_INCREMENT,
      `${count}`,
    );

    setKeyForEachLaunch(GlobalRef.softUpdateRef, true);
  }, [setKeyForEachLaunch]);

  const resetCountAppLaunch = useCallback(async () => {
    await addStringInAsyncStorage(
      AsyncStorageConstant.SOFT_UPDATE_INCREMENT,
      '',
    );
  }, []);

  const getAppLaunchCount = useCallback(async () => {
    const count = await readDataInAsyncStorage(
      AsyncStorageConstant.SOFT_UPDATE_INCREMENT,
    );
    return count ? Number(count) : 0;
  }, []);

  const isValidToOpenSoftUpdate = useCallback(
    async (data: any) => {
      const isOpen = await getValueForEachLaunch(GlobalRef.softUpdateRef);
      if (!data?.visible_update_for?.includes(`${data?.appVersion}`)) {
        await resetCountAppLaunch();
        return false;
      }

      const appLaunchCount = await getAppLaunchCount();

      return (appLaunchCount - 1) % data?.show_popup_frequency === 0 && isOpen;
    },
    [getAppLaunchCount, getValueForEachLaunch, resetCountAppLaunch],
  );

  return {
    appVersion,
    softUpdateConfig: SOFT_UPDATE_CONFIG,
    softUpdateApp,
    countAppLaunch,
    resetCountAppLaunch,
    getAppLaunchCount,
    isValidToOpenSoftUpdate,
  };
};

export default useSoftUpdateBottomSheetController;
