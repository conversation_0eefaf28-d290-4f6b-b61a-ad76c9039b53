import {getPrefString} from '../../../../../jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '../../../../../jiomart-common/src/JMConstants';
import {JMSharedViewModel} from '../../../../../jiomart-common/src/JMSharedViewModel';
import {useGlobalState} from '../../../../../jiomart-general/src/context/JMGlobalStateProvider';
import {useConfigFile} from '../../../../../jiomart-general/src/hooks/useJMConfig';
import {JMConfigFileName} from '../../../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import usePermissionBottomSheetController from './usePermissionBottomSheetController';
import {
  checkIsLocationEnabled,
  checkIsLocationPermissionGranted,
} from '../../../../../jiomart-common/src/utils/JMLocationUtility';
import JMInAppBannerController from '../../../../../jiomart-inappbanner/src/controller/JMInAppBannerController';
import {JMLogger} from '../../../../../jiomart-common/src/utils/JMLogger';
import {Platform} from 'react-native';

const useGlobalBottomSheetController = () => {
  const config = useConfigFile(JMConfigFileName.JMCommonContentFileName);
  const permissionConfig = config?.permissionConfig;
  const privacyPolicyConfig = config?.privacyPolicyConfig;
  const enableLocationConfig = config?.enableLocationConfig;

  // const softUpdate = useSoftUpdateBottomSheetController();
  const permission = usePermissionBottomSheetController();
  // const {setKeyForEachLaunch, getValueForEachLaunch} = useEachAppLaunch();

  const {
    setPermissionBtmSheet,
    setPrivacyPolicyBtmSheet,
    setPincodeBtmSheet,
    setSoftUpdateBtmSheet,
    setQuickBtmSheet,
    setInAppBanner,
    setEnableLocationBtmSheet
  } = useGlobalState();

  const checkAndOpenNextSheet = async () => {
    try {
      // const isValidForSoftUpdate = await softUpdate.isValidToOpenSoftUpdate({
      //   appVersion: softUpdate.appVersion,
      //   ...softUpdate.softUpdateConfig,
      // });

      const isPermissionDialogShown = await getPrefString(
        AsyncStorageKeys.PERMISSION_DIALOG,
      );
      const allPermissionGranted =
        await permission.checkIfAllPermissionsGranted();

      const isPrivacyPolicy = await getPrefString(
        AsyncStorageKeys.PRIVACY_POLICY,
      );

      const isLocationPermissionGranted =
        await checkIsLocationPermissionGranted();
      const isLocationEnabled = await checkIsLocationEnabled();
      const isLocationProvided = await getPrefString(AsyncStorageKeys.LOCATION_PERMISSION_GRANTED)
      const isPincodeProvided = await getPrefString(AsyncStorageKeys.PINCODE_PROVIDED)
      const isQuickBtmSheetShown = await getPrefString(AsyncStorageKeys.QUICK_BOTTOMSHEET_SHOWN);
      const isEnableLocationBtmSheetShown = await getPrefString(AsyncStorageKeys.ENABLE_LOCATION);

      const androidLocCondition =
        ((isLocationProvided != 'true' &&
          isLocationPermissionGranted != true) ||
          (!isLocationEnabled && isLocationPermissionGranted)) &&
        isPincodeProvided != 'true';
      const ioslocationCondition =
        isLocationProvided != 'true' &&
        isLocationPermissionGranted != true &&
        isPincodeProvided != 'true';
      const locationConditionStatus =
        Platform.OS == 'android' ? androidLocCondition : ioslocationCondition;

      if (isPermissionDialogShown !== 'true' && !allPermissionGranted) {
        setPermissionBtmSheet(true);
      }
      // else if(// logic for soft update open) {
      //  setSoftUpdateBtmSheet(true)
      // }
      else if (
        JMSharedViewModel.Instance.loggedInStatus &&
        isPrivacyPolicy !== 'true'
      ) {
        setPrivacyPolicyBtmSheet(true);
      } else if(isEnableLocationBtmSheetShown != 'true' && isLocationPermissionGranted !== true){
        setEnableLocationBtmSheet(true);
      } else if (locationConditionStatus) {
        setPincodeBtmSheet(true);
      } else {
        const inAppBannerData =
          await JMInAppBannerController.inAppBannerDataPromise;
        JMLogger.log(
          'inAppBannerData ',
          'inAppBannerData' + JSON.stringify(inAppBannerData),
        );
        if (inAppBannerData) {
          setInAppBanner(inAppBannerData);
        } else if (isQuickBtmSheetShown != 'true') {
          setQuickBtmSheet(true);
        }
      }
    } catch (error) {
      console.log('🚀 ~ checkAndOpenNextSheet ~ error:', error);
    }
  };

  return {
    permissionConfig,
    privacyPolicyConfig,
    enableLocationConfig,
    // ...softUpdate,
    ...permission,
    checkAndOpenNextSheet,
  };
};

export default useGlobalBottomSheetController;
