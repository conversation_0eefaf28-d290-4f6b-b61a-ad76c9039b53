import {Platform} from 'react-native';
import {RESULTS} from 'react-native-permissions';
import useAddressOperation from '../../../../../jiomart-address/src/hooks/useAddressOperation';
import useCurrentLocation from '../../../../../jiomart-address/src/hooks/useCurrentLocation';
import {addStringPref} from '../../../../../jiomart-common/src/JMAsyncStorageHelper';
import {
  AndroidPermission,
  AsyncStorageKeys,
  IOSPermission,
} from '../../../../../jiomart-common/src/JMConstants';
import {
  checkAndRequestMultiPermission,
  checkMultiPermission,
  type JMPermissionType,
} from '../../../../../jiomart-common/src/JMPermission';
import {useConfigFile} from '../../../../../jiomart-general/src/hooks/useJMConfig';
import {JMConfigFileName} from '../../../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {checkIsLocationEnabled} from '../../../../../jiomart-common/src/utils/JMLocationUtility';

const usePermissionBottomSheetController = () => {
  const config: any = useConfigFile(JMConfigFileName.JMCommonContentFileName);
  const permissionConfig = config?.permissionConfig;

  const {fetchLocationFromReverseGeoCodeFromLatLong} = useCurrentLocation({
    alertBlocked: permissionConfig?.alert?.blockedLocation,
  });
  const {checkAndSetPincode} = useAddressOperation();

  const handleRequestPermssion = async () => {
    try {
      const permissionRes = await checkAndRequestMultiPermission(
        permissionConfig?.requestPermissions[Platform.OS],
      );
      return permissionRes;
    } catch (error) {
      return null;
    }
  };

  const handlePermission = async () => {
    try {
      await addStringPref(AsyncStorageKeys.PERMISSION_DIALOG, 'true');
      const permissionRes = await handleRequestPermssion();
      const locationPermission =
        Platform.OS === 'android'
          ? AndroidPermission.ACCESS_FINE_LOCATION
          : IOSPermission.LOCATION_WHEN_IN_USE;
      const isLocationEnabled = await checkIsLocationEnabled();
      const locPermissionStatus =
        permissionRes?.[locationPermission as JMPermissionType] ===
          RESULTS.GRANTED ||
        permissionRes?.[locationPermission as JMPermissionType] ===
          RESULTS.LIMITED;
      const locationPermissionStatus =
        Platform.OS === 'android'
          ? locPermissionStatus && isLocationEnabled
          : locPermissionStatus;

      if (locationPermissionStatus) {
        const stats = await fetchLocationFromReverseGeoCodeFromLatLong();
        console.log('🚀 ~ handlePermission ~ stats:', stats);
        if (stats?.address?.pin) {
          addStringPref(AsyncStorageKeys.LOCATION_PERMISSION_GRANTED, 'true');
          checkAndSetPincode({
            pincode: stats?.address?.pin,
            state: stats?.address?.state,
            city: stats?.address?.city,
          });
        }
      }
    } catch (error) {
    } finally {
      return true;
    }
  };

  const checkIfAllPermissionsGranted = async () => {
    try {
      const results = await checkMultiPermission(
        permissionConfig?.requestPermissions?.[Platform.OS],
      );
      const allGranted = Object.values(results).every(
        result => result === RESULTS.GRANTED || result === RESULTS.LIMITED,
      );
      return allGranted;
    } catch (error) {
      return false;
    }
  };

  return {
    handlePermission,
    checkIfAllPermissionsGranted,
  };
};

export default usePermissionBottomSheetController;
