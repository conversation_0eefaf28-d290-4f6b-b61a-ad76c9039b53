import {SafeAreaView, Pressable, View} from 'react-native';
import React, {memo} from 'react';
import {JioButton, JioIcon, JioText} from '@jio/rn_components';
import {
  ButtonSize,
  IconColor,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import LottieView from 'lottie-react-native';
import {styles} from '../../styles/SoftUpdateBtmSheetStyles';
import type {SoftUpdateBtmSheetProps} from '../../types/SoftUpdateBtmSheetType';
import FastImage from 'react-native-fast-image';

const SoftUpdateBtmSheet = memo((props: SoftUpdateBtmSheetProps) => {
  const {
    config: {
      is_close_button_show = true,
      lottie = {},
      image_url = '',
      title = '',
      message = '',
      button_text = '',
    } = {},
    onClose,
    onButtonPress,
  } = props;

  return (
    <SafeAreaView>
      <View style={styles.container}>
        {is_close_button_show && (
          <Pressable
            style={styles.close}
            onPress={onClose}
            activeOpacity={0.65}>
            <JioIcon
              ic="IcClose"
              color={IconColor.PRIMARY60}
              style={styles.closeButton}
            />
          </Pressable>
        )}
        <View style={styles.lottie}>
          {image_url ? (
            <FastImage
              style={styles.image}
              source={{
                uri: image_url,
              }}
            />
          ) : (
            <LottieView
              source={lottie.icon_url ?? ''}
              {...lottie}
              style={styles.lottieView}
            />
          )}
        </View>
        <JioText
          text={title}
          appearance={JioTypography.HEADING_XXS}
          color={'primary_grey_100'}
          style={styles.title}
        />
        <JioText
          text={message}
          appearance={JioTypography.BODY_XXS}
          color={'primary_grey_80'}
          style={styles.subTitle}
        />
        <JioButton
          title={button_text}
          stretch
          size={ButtonSize.LARGE}
          style={styles.button}
          onClick={onButtonPress}
        />
      </View>
    </SafeAreaView>
  );
});

SoftUpdateBtmSheet.displayName = 'SoftUpdateBtmSheet';

export default SoftUpdateBtmSheet;
