import {StyleSheet} from 'react-native';
import {rh, rw} from '../../../../../jiomart-common/src/JMResponsive';

export const styles = StyleSheet.create({
  container: {
    marginVertical: rh(24),
    marginHorizontal: rw(24),
  },
  enableLocationBtn: {
    height: rh(48),
    backgroundColor: '#0078AD',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 200,
    marginBottom: rh(16)
  },
  selectLocationBtn: {
    height: rh(48),
    backgroundColor: '#ffffff',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 200,
    borderWidth: 1,
    borderColor: "#E0E0E0"
  },
  closeBtn: {
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: rh(10)
  },
  subTitleText: {
    marginBottom: rh(24)
  }
});
