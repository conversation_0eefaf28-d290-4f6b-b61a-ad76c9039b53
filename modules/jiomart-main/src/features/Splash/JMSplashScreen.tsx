import { useEffect } from 'react';
import { StyleSheet, View } from 'react-native';
import {
  AppScreens,
  type ScreenProps,
} from '../../../../jiomart-common/src/JMAppScreenEntry';
import {
  ActionType,
  getHomeAppScreenDestination,
  navBeanObj,
  NavigationType
} from '../../../../jiomart-common/src/JMNavGraphUtil';
import { HeaderType } from '../../../../jiomart-common/src/JMScreenSlot.types';
import { JMSharedViewModel } from '../../../../jiomart-common/src/JMSharedViewModel';
import { changeWindowBackground } from '../../../../jiomart-general/src/bridge/JMRNBridge';
import { useGlobalState } from '../../../../jiomart-general/src/context/JMGlobalStateProvider';
import { navigateTo } from '../../../../jiomart-general/src/navigation/JMNavGraph';
import { LottieComponent } from '../../../../jiomart-general/src/ui/CustomMediaRendered';
import { CustomStatusBar } from '../../../../jiomart-general/src/ui/CustomStatusBar';
import { getBaseURL } from '../../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import useJMInitialisationController from '../../controllers/useJMInitialisationController';

// Define props for SplashScreen using NativeStackScreenProps
type Props = ScreenProps<typeof AppScreens.SPLASH_SCREEN>;

const JMSplashScreen = ({ route, navigation }: Props) => {
  const { splashJsonData } = useJMInitialisationController();
  const { deeplinkData } = useGlobalState();

  useEffect(() => {
    if (deeplinkData) {
      JMSharedViewModel.Instance.setShowAnimationOnDeeplink(1)
    }
  }, [deeplinkData])

  const onAnimationFinish = () => {
    JMSharedViewModel.Instance.setSplashJsonData(undefined)
    JMSharedViewModel.Instance.setDeeplinkUrlData("")
    changeWindowBackground()
    navigateTo(
      navBeanObj({
        actionType: ActionType.OPEN_WEB_URL,
        destination: getHomeAppScreenDestination(true),
        headerVisibility: HeaderType.CUSTOM,
        navigationType: NavigationType.REPLACE,
        loginRequired: false,
        actionUrl: getBaseURL(),
        shouldShowBottomNavBar: false,
        shouldShowDeliverToBar: true,
        headerType: 5,
      }),
      navigation,
    )
  };
  if (!splashJsonData) return null
  const mainUI = () => {
    return (
      <View style={styles.container}>
        <CustomStatusBar color={'#0078AD'} />
        <LottieComponent
          source={splashJsonData}
          autoPlay
          loop={false}
          resizeMode="cover"
          onAnimationFinish={onAnimationFinish}
          style={{ width: '100%', height: '100%' }}
        />
      </View>
    );
  };
  return (
    mainUI()
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0078AD', // Mart Default background for splash screen
  },
});

export default JMSplashScreen;
