import React, {useEffect, useState} from 'react';
import {FlatList, View, StyleSheet, Text} from 'react-native';
import {Ji<PERSON><PERSON>pin<PERSON>, JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import GridCategoryCard from './GridCategoryCard';
import useAllCategoriesScreenController from '../../../jiomart-category/src/controller/useAllCategoriesScreenController';
import ProductListWithHeadingShimmer from '../../../jiomart-home/src/components/ProductListWithHeadingShimmer';
import {getCategoryDetails} from '../../../jiomart-home/src/utils/jmDashboardUtils';
import {getBaseURL} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import JMServiceablityNetworkNewController from '../../../jiomart-networkmanager/src/JMNetworkController/JMServiceablityNetworkNewController';
import {useGlobalState} from '../../../jiomart-general/src/context/JMGlobalStateProvider';
import {useQuery} from '@tanstack/react-query';
import {getPersistString} from '../../../jiomart-home/src/utils/HomeUtils';

interface AllCatogeriesHomeProps {
  onClick?: any;
  newplp?: string;
  l1?: string;
  categoryType: string; // e.g., "GROCERIES" or "ELECTRONICS"
}

const serviciablityControllerNew = new JMServiceablityNetworkNewController();

const AllCatogeriesHome: React.FC<AllCatogeriesHomeProps> = ({
  onClick,
  newplp,
  l1,
  categoryType,
}) => {
  const {selectedOption} = useGlobalState();

  const fetchCategories = async () => {
    const res = await serviciablityControllerNew.getCachedHcatConfigData();
    let categoriesArr: number[] | null = null;
    let categoriesStr = res?.[categoryType]?.categories;
    if (
      (!categoriesStr || categoriesStr.length === 0) &&
      typeof l1 === 'string' &&
      l1.length > 0
    ) {
      categoriesStr = l1;
    }
    if (typeof categoriesStr === 'string' && categoriesStr.length > 0) {
      categoriesArr = categoriesStr
        .split(',')
        .map((cat: string) => parseInt(cat, 10))
        .filter(cat => !isNaN(cat));
    }
    const response = await getCategoryDetails(
      categoriesArr,
      selectedOption === 'Quick' ? 1 : 2,
    );
    return response?.resultData;
  };

  // For @tanstack/react-query v4+, use the object syntax:
  const {
    data: category,
    isLoading,
    isError,
  } = useQuery({
    queryKey: [
      'categories',
      categoryType,
      l1,
      selectedOption,
      getPersistString('groceries-electronics'),
    ],
    queryFn: fetchCategories,
    staleTime: 3 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  // Determine which category to display based on type
  let displayCategory = null;
  if (category && Array.isArray(category) && category.length > 0) {
    if (selectedOption !== 'Quick') {
      displayCategory = category.find(
        (cat: any) => cat.vertical_code === categoryType,
      );
    } else {
      displayCategory = category[0];
    }
  }

  return (
    <>
      {/* {console.log('AllCatogeriesHome category:', category.data)} */}
      {displayCategory &&
      Array.isArray(displayCategory.sub_categories) &&
      displayCategory.sub_categories.length > 0 ? (
        <FlatList
          data={displayCategory.sub_categories}
          keyExtractor={(item, index) => index.toString()}
          horizontal={false}
          showsHorizontalScrollIndicator={false}
          renderItem={({item}) => (
            <View>
              <GridCategoryCard
                title={item.name}
                onClick={onClick}
                columns={4}
                items={
                  Array.isArray(item.sub_categories)
                    ? item.sub_categories.map((sub: any, idx: number) => ({
                        image:
                          `${getBaseURL()}/${sub.thumbnail_image_path}` || '',
                        name: sub.name || '',
                        position: idx,
                        url:
                          newplp === '1'
                            ? (sub.url_path || '').replace(/^c\//, 'cn/')
                            : sub.url_path || '',
                      }))
                    : []
                }
                borderRadius={0}
                margin={0}
                backgroundColor="white"
                imageBackgroundColor="#f2f9fc"
                paddingHorizontal={20}
                imageGap={10}
                imageAspectRatio={1}
                // borderBottomWidth={1}
                imageBorderRadius={12}
                imageMarginTop={0}
                imageMarginBottom={28}
                itemTitleColor="secondary_grey_100"
                itemTitleFontWeight="500"
                paddingTop={0}
                imagePaddingTop={10}
                imagePaddingRight={6}
                imagePaddingBottom={2}
                imagePaddingLeft={6}
                showGridTitle={true}
                paddingBottom={15}
                sliceToFullRows={false}
              />
              <View
                style={{
                  borderWidth: 0.5,
                  marginHorizontal: 20,
                  borderColor: '#e0e0e0',
                }}></View>
            </View>
          )}
        />
      ) : isLoading ? (
        <ProductListWithHeadingShimmer />
      ) : isError ? (
        <Text>Error loading categories</Text>
      ) : (
        <Text>No categories found</Text>
      )}
    </>
  );
};

export default AllCatogeriesHome;
