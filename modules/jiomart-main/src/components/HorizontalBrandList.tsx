import {FlatList, StyleSheet, View, Pressable} from 'react-native';
import FastImage from 'react-native-fast-image';
import {getBaseURL} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import {rh, rw} from '../../.../../../jiomart-common/src/JMResponsive';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';

interface HorizontalBrandListProps {
  title: string;
  images: {
    brandName: string;
    brandImage: string;
    productImage: string;
  }[];
  onImagePress?: (imageUrl: string, index: number) => void;
  brandHeight: number;
  brandWidth: number;
  productHeight: number;
  productWidth: number;
}

const renderItem = (
  {
    item,
    index,
  }: {
    item: {
      brandName: string;
      brandImage: string;
      productImage: string;
    };
    index: number;
  },
  onPress?: (url: string, idx: number) => void,
  brandHeight?: number,
  brandWidth?: number,
  productHeight?: number,
  productWidth?: number,
) => {
  return (
    <Pressable
      onPress={() => onPress?.(item.brandImage, index)}
      style={{alignItems: 'center'}}>
      <FastImage
        source={{
          // uri: `${getBaseURL()}/images/product/original/${item}`,
          uri: item?.brandImage,
        }}
        style={[styles.image, {height: brandHeight, width: brandWidth}]}
        resizeMode={FastImage.resizeMode.cover}
      />
      <View style={styles.productImage}>
        <FastImage
          source={{
            // uri: `${getBaseURL()}/images/product/original/${item}`,
            uri: item?.productImage,
          }}
          style={{height: productHeight, width: productWidth}}
          resizeMode={FastImage.resizeMode.cover}
        />
        <JioText
          text={item.brandName}
          color="black"
          appearance={JioTypography.BODY_XS_BOLD}
          style={{fontWeight: '700'}}
          textAlign="center"
        />
      </View>
    </Pressable>
  );
};

const HorizontalBrandList = (props: HorizontalBrandListProps) => {
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <JioText
          text={props.title}
          color="black"
          appearance={JioTypography.HEADING_XS}
          style={styles.title}
          textAlign="center"
        />
      </View>
      <FlatList
        data={props.images}
        renderItem={item =>
          renderItem(
            item,
            props.onImagePress,
            props.brandHeight,
            props.brandWidth,
            props.productHeight,
            props.productWidth,
          )
        }
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.flatListContent}
      />
    </View>
  );
};
export default HorizontalBrandList;

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    paddingBottom: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignContent: 'center',
  },
  title: {
    padding: 22,
    fontWeight: '900',
    width: '100%',
  },
  flatListContent: {
    paddingHorizontal: 16,
    gap: 12,
  },
  image: {
    borderRadius: 999,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    zIndex: 1,
  },
  productImage: {
    marginTop: -48,
    borderRadius: 24,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    paddingTop: 52,
    paddingBottom: 12,
    gap: 4,
  },
});
