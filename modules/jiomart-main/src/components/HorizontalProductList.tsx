import {<PERSON><PERSON><PERSON><PERSON><PERSON>, Ji<PERSON><PERSON>ex<PERSON>} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {FlatList, Pressable, StyleSheet, View} from 'react-native';
import ItemCard, {ShimmerCard} from './ItemCard';
import {useState, useCallback, useEffect, useRef} from 'react';
import {shouldShowQuickLogo} from '../../../jiomart-home/src/utils/HomeSectionUtils';
import {useConfigFile} from '../../../jiomart-general/src/hooks/useJMConfig';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import JMServiceablityNetworkNewController from '../../../jiomart-networkmanager/src/JMNetworkController/JMServiceablityNetworkNewController';
import { ConfigInfo, DataResponse } from '../../../jiomart-networkmanager/src/models/home/<USER>';

interface HorizontalProductListProps {
  title: string;
  onSeeAllClick: () => void;
}

// Dummy API response
const dummyResponse = {
  status: 'success',
  page_no: 1,
  limit: '10',
  total_sku: 148,
  total_page: 15,
  products: [
    {
      product_code: 590003515,
      in_stock: 1,
      brand: 'Private Label',
      url_path: '/p/groceries/lentil-sprouts-200-g/590003515',
      image_url:
        'images/product/150x150/590003515/onion-1-kg-product-images-o590003515-p590003515-0-202408070949.jpg',
      display_name: 'Onion 1 kg',
      brand_id: 526,
      availability_status: 'A',
      image_path:
        '590003515/onion-1-kg-product-images-o590003515-p590003515-0-202408070949.jpg',
      category_level: {
        level4: ['Onion'],
        level1: ['Groceries'],
        level0: ['Category'],
        level3: ['Basic Vegetables'],
        level2: ['Fruits & Vegetables'],
      },
      food_type: 'Green Dot',
      alternate_product_code: '590003515',
      available_at_3p_seller: false,
      seller_ids: ['1'],
      seller_names: ['Reliance Retail'],
      category_level_id: {
        level4: [29169],
        level1: [2],
        level0: [1],
        level3: [28981],
        level2: [219],
      },
      vertical_code: 'GROCERIES',
      objectID: '590003515',
      mrp: 5,
      selling_price: 5,
      discount_pct: 0,
      seller_id: 1,
    },
    {
      product_code: 590003516,
      in_stock: 1,
      brand: 'Private Label',
      url_path: '/p/groceries/tapioca-1-kg/590003516',
      image_url:
        'images/product/150x150/590003516/potato-1-kg-product-images-o590003516-p590003516-0-202408070949.jpg',
      display_name: 'Potato 1 kg',
      brand_id: 526,
      availability_status: 'A',
      image_path:
        '590003516/potato-1-kg-product-images-o590003516-p590003516-0-202408070949.jpg',
      category_level: {
        level4: ['Potato'],
        level1: ['Groceries'],
        level0: ['Category'],
        level3: ['Basic Vegetables'],
        level2: ['Fruits & Vegetables'],
      },
      food_type: 'Green Dot',
      alternate_product_code: '590003516',
      available_at_3p_seller: false,
      seller_ids: ['1'],
      seller_names: ['Reliance Retail'],
      category_level_id: {
        level4: [29170],
        level1: [2],
        level0: [1],
        level3: [28981],
        level2: [219],
      },
      vertical_code: 'GROCERIES',
      objectID: '590003516',
      mrp: 35,
      selling_price: 35,
      discount_pct: 0,
      seller_id: 1,
    },
    {
      product_code: 590000189,
      in_stock: 1,
      brand: 'Private Label',
      url_path: '/p/groceries/okra-per-kg/590000189',
      image_url:
        'images/product/150x150/590000189/okra-250-g-product-images-o590000189-p590000189-0-202409171905.jpg',
      display_name: 'Okra 250 g',
      brand_id: 526,
      availability_status: 'A',
      image_path:
        '590000189/okra-250-g-product-images-o590000189-p590000189-0-202409171905.jpg',
      category_level: {
        level4: ['Okra (Bhindi)'],
        level1: ['Groceries'],
        level0: ['Category'],
        level3: ['Basic Vegetables'],
        level2: ['Fruits & Vegetables'],
      },
      food_type: 'Green Dot',
      alternate_product_code: '590000189',
      available_at_3p_seller: false,
      seller_ids: ['1'],
      seller_names: ['Reliance Retail'],
      category_level_id: {
        level4: [29178],
        level1: [2],
        level0: [1],
        level3: [28981],
        level2: [219],
      },
      vertical_code: 'GROCERIES',
      objectID: '590000189',
      mrp: 18,
      selling_price: 18,
      discount_pct: 0,
      seller_id: 1,
    },
    {
      product_code: 590001266,
      in_stock: 1,
      brand: 'Private Label',
      url_path: '/p/groceries/sweet-corn-1-pc/590001266',
      image_url:
        'images/product/150x150/590001266/sweet-corn-1-pc-approx-250-g-450-g-product-images-o590001266-p590001266-0-202409171907.jpg',
      display_name: 'Sweet Corn 1 pc (Approx 250 g - 450 g)',
      brand_id: 526,
      availability_status: 'A',
      image_path:
        '590001266/sweet-corn-1-pc-approx-250-g-450-g-product-images-o590001266-p590001266-0-202409171907.jpg',
      category_level: {
        level4: ['Corn'],
        level1: ['Groceries'],
        level0: ['Category'],
        level3: ['Basic Vegetables'],
        level2: ['Fruits & Vegetables'],
      },
      seller_ids: ['1'],
      food_type: 'Green Dot',
      alternate_product_code: '590001266',
      available_at_3p_seller: false,
      seller_names: ['Reliance Retail'],
      category_level_id: {
        level4: [29181],
        level1: [2],
        level0: [1],
        level3: [28981],
        level2: [219],
      },
      vertical_code: 'GROCERIES',
      objectID: '590001266',
      mrp: 16,
      selling_price: 16,
      discount_pct: 0,
      seller_id: 1,
    },
    {
      product_code: 590000259,
      in_stock: 1,
      brand: 'Private Label',
      url_path: '/p/groceries/cucumber-keera-kg/590000259',
      image_url:
        'images/product/150x150/590000259/cucumber-kheera-500-g-product-images-o590000259-p590000259-0-202411140941.jpg',
      display_name: 'Cucumber Kheera 1 kg',
      brand_id: 526,
      availability_status: 'A',
      image_path:
        '590000259/cucumber-kheera-500-g-product-images-o590000259-p590000259-0-202411140941.jpg',
      category_level: {
        level4: ['Cucumber'],
        level1: ['Groceries'],
        level0: ['Category'],
        level3: ['Basic Vegetables'],
        level2: ['Fruits & Vegetables'],
      },
      food_type: 'Green Dot',
      alternate_product_code: '590000259',
      available_at_3p_seller: false,
      seller_ids: ['1'],
      seller_names: ['Reliance Retail'],
      category_level_id: {
        level4: [29179],
        level1: [2],
        level0: [1],
        level3: [28981],
        level2: [219],
      },
      vertical_code: 'GROCERIES',
      objectID: '590000259',
      mrp: 60,
      selling_price: 60,
      discount_pct: 0,
      seller_id: 1,
    },
    {
      product_code: 590000245,
      in_stock: 1,
      brand: 'Private Label',
      url_path: '/p/groceries/mushroom-200-g-pack/590000245',
      image_url:
        'images/product/150x150/590000245/button-mushroom-200-g-product-images-o590000245-p590000245-0-202408070949.jpg',
      display_name: 'Button Mushroom 200 g',
      brand_id: 526,
      availability_status: 'A',
      image_path:
        '590000245/button-mushroom-200-g-product-images-o590000245-p590000245-0-202408070949.jpg',
      category_level: {
        level4: ['Exotic Vegetables'],
        level1: ['Groceries'],
        level0: ['Category'],
        level3: ['Premium Fruits & Vegetables'],
        level2: ['Fruits & Vegetables'],
      },
      food_type: 'Green Dot',
      alternate_product_code: '590000245',
      available_at_3p_seller: false,
      seller_ids: ['1'],
      seller_names: ['Reliance Retail'],
      category_level_id: {
        level4: [29100],
        level1: [2],
        level0: [1],
        level3: [28983],
        level2: [219],
      },
      vertical_code: 'GROCERIES',
      objectID: '590000245',
      mrp: 42,
      selling_price: 42,
      discount_pct: 0,
      seller_id: 1,
    },
    {
      product_code: 590002370,
      in_stock: 1,
      brand: 'Private Label',
      url_path: '/p/groceries/cabbage-1-pc/590002370',
      image_url:
        'images/product/150x150/590002370/cabbage-1-pc-approx-600-g-1000-g-product-images-o590002370-p590002370-0-202409171904.jpg',
      display_name: 'Cabbage 1 pc (Approx 600 g - 1000 g)',
      brand_id: 526,
      availability_status: 'A',
      image_path:
        '590002370/cabbage-1-pc-approx-600-g-1000-g-product-images-o590002370-p590002370-0-202409171904.jpg',
      category_level: {
        level4: ['Cabbage'],
        level1: ['Groceries'],
        level0: ['Category'],
        level3: ['Basic Vegetables'],
        level2: ['Fruits & Vegetables'],
      },
      food_type: 'Green Dot',
      alternate_product_code: '590002370',
      available_at_3p_seller: false,
      seller_ids: ['1'],
      seller_names: ['Reliance Retail'],
      category_level_id: {
        level4: [29175],
        level1: [2],
        level0: [1],
        level3: [28981],
        level2: [219],
      },
      vertical_code: 'GROCERIES',
      objectID: '590002370',
      mrp: 18,
      selling_price: 18,
      discount_pct: 0,
      seller_id: 1,
    },
    {
      product_code: 590000086,
      in_stock: 1,
      brand: 'Private Label',
      url_path: '/p/groceries/coconut-1-pc/590000086',
      image_url:
        'images/product/150x150/590000086/big-coconut-1-pc-approx-350-g-600-g-product-images-o590000086-p590000086-0-202408070949.jpg',
      display_name: 'Big Coconut 1 Pc ( Approx 350 g - 600 g)',
      brand_id: 526,
      availability_status: 'A',
      image_path:
        '590000086/big-coconut-1-pc-approx-350-g-600-g-product-images-o590000086-p590000086-0-202408070949.jpg',
      category_level: {
        level4: ['Other Vegetables'],
        level1: ['Groceries'],
        level0: ['Category'],
        level3: ['Roots, Herbs & Others'],
        level2: ['Fruits & Vegetables'],
      },
      food_type: 'Green Dot',
      alternate_product_code: '590000086',
      available_at_3p_seller: false,
      seller_ids: ['1'],
      seller_names: ['Reliance Retail'],
      category_level_id: {
        level4: [29189],
        level1: [2],
        level0: [1],
        level3: [28982],
        level2: [219],
      },
      vertical_code: 'GROCERIES',
      objectID: '590000086',
      mrp: 39,
      selling_price: 39,
      discount_pct: 0,
      seller_id: 1,
    },
    {
      product_code: 590000092,
      in_stock: 1,
      brand: 'Private Label',
      url_path: '/p/groceries/tomato-1-kg/590000092',
      image_url:
        'images/product/150x150/590000092/tomato-1-kg-product-images-o590000092-p590000092-0-202409171905.jpg',
      display_name: 'Tomato 1 kg',
      brand_id: 526,
      availability_status: 'A',
      image_path:
        '590000092/tomato-1-kg-product-images-o590000092-p590000092-0-202409171905.jpg',
      category_level: {
        level4: ['Tomato'],
        level1: ['Groceries'],
        level0: ['Category'],
        level3: ['Basic Vegetables'],
        level2: ['Fruits & Vegetables'],
      },
      seller_ids: ['1'],
      food_type: 'Green Dot',
      alternate_product_code: '590000092',
      available_at_3p_seller: false,
      seller_names: ['Reliance Retail'],
      category_level_id: {
        level4: [29171],
        level1: [2],
        level0: [1],
        level3: [28981],
        level2: [219],
      },
      vertical_code: 'GROCERIES',
      objectID: '590000092',
      mrp: 39,
      selling_price: 39,
      discount_pct: 0,
      seller_id: 1,
    },
  ],
};

// Dummy API call function
const fetchProducts = async (page: number): Promise<any[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Return dummy data
  return dummyResponse.products;
};

const serviciablityControllerNew = new JMServiceablityNetworkNewController();

const HorizontalProductList = (props: HorizontalProductListProps) => {
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const config = useConfigFile(
    JMConfigFileName.JMHomeDashboardConfigurationFileName,
  );
  const serviceabilityData = useRef<DataResponse | null>(null);
  const hcatConfigData = useRef<Record<string, ConfigInfo> | null>(null);

  const loadProducts = useCallback(async (pageNum: number) => {
    try {
      setLoading(true);
      const newProducts = await fetchProducts(pageNum);

      if (pageNum === 1) {
        setProducts(newProducts);
      } else {
        setProducts(prev => [...prev, ...newProducts]);
      }

      // Simulate end of data after 3 pages
      setHasMore(pageNum < 10);
    } catch (error) {
      console.error('Error fetching products:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchServiceabilityAndHcatConfigData = async () => {
    serviceabilityData.current = await serviciablityControllerNew.getCachedServiceabilityData();
    hcatConfigData.current = await serviciablityControllerNew.getCachedHcatConfigData();
  };

  // Load initial data
  useEffect(() => {
    loadProducts(1);
    fetchServiceabilityAndHcatConfigData();
  }, [loadProducts]);

  const handleLoadMore = () => {
    if (!loading && hasMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      loadProducts(nextPage);
    }
  };

  const renderFooter = () => {
    if (!loading) return null;
    return (
      <View style={styles.loaderContainer}>
        <JioSpinner />
      </View>
    );
  };

  const renderItem = ({item}: {item: any}) => {
    return (
      <ItemCard
        product={item}
        onProductPress={() => {}}
        disableWishlist={false}
        discount={`${item.discount_pct}% OFF`}
        discountBackgroundColor={'#E5F7EE'}
        discountColor={'sparkle_60'}
        disableAddToCart={!item.in_stock}
        addedToCart={false}
        showQcLogo={shouldShowQuickLogo(
          item,
          config?.showQuickLogoInProductCardDetails?.verticleAndSellerList ??
            [],
          serviceabilityData.current,
          hcatConfigData.current
        )}
        qcLogoUrl={
          config?.showQuickLogoInProductCardDetails?.quicklogoUrl ?? ''
        }
      />
    );
  };

  const renderShimmer = () => {
    return Array.from({length: 5}).map((_, index) => (
      <ShimmerCard key={`shimmer-${index}`} />
    ));
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <JioText
          text={props.title}
          color="black"
          appearance={JioTypography.HEADING_XXS}
          style={styles.title}
        />
        <Pressable
          onPress={() => {
            props.onSeeAllClick();
            console.log('See All');
          }}
          style={styles.seeAll}>
          <JioText
            text="See All"
            color="primary_60"
            appearance={JioTypography.BODY_XS_BOLD}
            style={styles.seeAllText}
          />
        </Pressable>
      </View>
      {loading && products.length === 0 ? (
        <View style={styles.shimmerContainer}>{renderShimmer()}</View>
      ) : (
        <FlatList
          data={products}
          renderItem={renderItem}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.flatListContent}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          ListFooterComponent={renderFooter}
        />
      )}
    </View>
  );
};

export default HorizontalProductList;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 24,
    backgroundColor: 'white',
    paddingBottom: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignContent: 'center',
  },
  title: {
    paddingLeft: 16,
    paddingBottom: 12,
    // fontWeight: '900',
  },
  seeAll: {
    paddingHorizontal: 16,
  },
  seeAllText: {
    fontWeight: '700',
  },
  flatListContent: {
    paddingLeft: 24,
    gap: 12,
  },
  loaderContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  shimmerContainer: {
    flexDirection: 'row',
    paddingLeft: 24,
    gap: 12,
  },
});
