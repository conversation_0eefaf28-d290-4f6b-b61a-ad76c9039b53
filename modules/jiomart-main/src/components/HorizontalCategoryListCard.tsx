import {FlatList, Pressable, StyleSheet, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import {getBaseURL} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';

interface HorizontalCategoryListCardProps {
  data: Array<any>;
  itemWidth?: number;
  title: string;
  titleSize?: JioTypography;
  titleTextAlign?: 'auto' | 'left' | 'right' | 'center' | 'justify' | undefined;
  backgroundColor?: string;
  padding: number;
  margin: number;
}

const renderItem = ({item, itemWidth}: {item: any; itemWidth: number}) => {
  return (
    <Pressable
      onPress={() => {}}
      style={[styles.itemContainer, {width: itemWidth}]}>
      <FastImage
        source={{
          uri: item.image,
          //   uri: `${getBaseURL()}/images/product/original/${item.image_path}`,
        }}
        style={[styles.image, {width: itemWidth, height: itemWidth}]}
        resizeMode={FastImage.resizeMode.cover}
      />
      <JioText
        appearance={JioTypography.BODY_XXS}
        text={item.title}
        maxLines={2}
        minLines={2}
        style={styles.text}
      />
    </Pressable>
  );
};

const HorizontalCategoryListCard = ({
  data,
  itemWidth = 60,
  title,
  titleSize,
  titleTextAlign,
  backgroundColor,
  padding,
  margin,
}: HorizontalCategoryListCardProps) => {
  return (
    <View
      style={{
        backgroundColor: 'white',
        borderBottomWidth: 1,
        borderColor: 'grey',
      }}>
      <View
        style={{
          backgroundColor: backgroundColor ? backgroundColor : 'white',
          padding: padding ? padding : 0,
          margin: margin ? margin : 0,
          borderRadius: margin ? 16 : 0,
        }}>
        <View style={styles.header}>
          <JioText
            text={title}
            color="black"
            appearance={titleSize}
            style={styles.title}
            textAlign={titleTextAlign}
          />
        </View>
        <FlatList
          data={data}
          renderItem={({item}) => renderItem({item, itemWidth})}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.flatListContent}
        />
      </View>
    </View>
  );
};

export default HorizontalCategoryListCard;

const styles = StyleSheet.create({
  flatListContent: {
    padding: 12,
  },
  itemContainer: {
    marginRight: 12,
  },
  image: {
    borderRadius: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    paddingTop: 5,
  },
  title: {
    padding: 8,
    fontWeight: '900',
    width: '100%',
  },
  text: {
    marginTop: 4,
    textAlign: 'center',
  },
});
