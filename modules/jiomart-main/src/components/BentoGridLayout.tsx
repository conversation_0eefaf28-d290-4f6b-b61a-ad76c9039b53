import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import React, {useCallback, useMemo} from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Pressable,
} from 'react-native';
import {SliderDetails} from '../../../jiomart-home/src/utils/HomePageTypes';
import {
  navBeanObj,
  NavigationType,
} from '../../../jiomart-common/src/JMNavGraphUtil';
import isEqual from 'lodash.isequal';

interface BentoGridLayoutProps {
  headerImage?: string;
  title?: string;
  images: SliderDetails[]; // expects at least 3 images
  imageWidth?: number;
  imageHeight?: number;
  backgroundColor?: string;
  onClick: any;
}

// Custom comparison function for React.memo
const areEqual = (
  prevProps: BentoGridLayoutProps,
  nextProps: BentoGridLayoutProps,
) => {
  return (
    prevProps.headerImage === nextProps.headerImage &&
    prevProps.title === nextProps.title &&
    isEqual(prevProps.images, nextProps.images) &&
    prevProps.imageWidth === nextProps.imageWidth &&
    prevProps.imageHeight === nextProps.imageHeight &&
    prevProps.backgroundColor === nextProps.backgroundColor &&
    prevProps.onClick === nextProps.onClick
  );
};

const BentoGridLayout: React.FC<BentoGridLayoutProps> = ({
  headerImage,
  title,
  images,
  imageWidth,
  imageHeight = 102,
  backgroundColor = '#fff',
  onClick,
}) => {
  // Memoize screen width calculation
  const screenWidth = useMemo(() => Dimensions.get('window').width, []);

  // Memoize calculated image width
  const calculatedImageWidth = useMemo(
    () => imageWidth || Math.floor((screenWidth - 48) / 2), // 48 for paddings/margins
    [imageWidth, screenWidth],
  );

  // Memoize handlePress function
  const handlePress = useCallback(
    (url: string) => {
      onClick(
        navBeanObj({
          source: '',
          destination: 'CommonWebViewScreen',
          actionType: 'T003',
          headerType: 9,
          headerVisibility: 2,
          navigationType: NavigationType.PUSH,
          actionUrl: url,
          loginRequired: false,
        }),
      );
    },
    [onClick],
  );

  // Memoize styles to prevent recreation
  const dynamicStyles = useMemo(
    () => ({
      container: [styles.container, {backgroundColor}],
      leftImage: [
        styles.leftImage,
        {width: calculatedImageWidth, height: imageHeight * 2 + 16},
      ],
      rightImage: [
        styles.rightImage,
        {width: calculatedImageWidth, height: imageHeight},
      ],
      rightImageWithMargin: [
        styles.rightImage,
        {
          width: calculatedImageWidth,
          height: imageHeight,
          marginTop: 8,
        },
      ],
    }),
    [backgroundColor, calculatedImageWidth, imageHeight],
  );

  if (!images || (Array.isArray(images) && images.length === 0)) {
    return null;
  }

  return (
    <View style={dynamicStyles.container}>
      {(headerImage || title) && (
        <View style={styles.headerContainer}>
          {headerImage ? (
            <Image
              source={{uri: headerImage}}
              style={styles.headerImage}
              resizeMode="cover"
            />
          ) : (
            <JioText
              text={title}
              color="black"
              appearance={JioTypography.HEADING_XS}
              style={styles.headerText}
              textAlign="left"
            />
          )}
        </View>
      )}
      <View style={styles.gridRow}>
        <Pressable
          onPress={() =>
            images?.[0]?.image && images[0].url && handlePress(images[0].url)
          }
          style={({pressed}) => [{opacity: pressed ? 0.7 : 1}]}>
          <Image
            source={{uri: images?.[0]?.image}}
            style={dynamicStyles.leftImage}
            resizeMode="cover"
          />
        </Pressable>
        <View style={styles.rightColumn}>
          <Pressable
            onPress={() =>
              images?.[1]?.image && images[1].url && handlePress(images[1].url)
            }
            style={({pressed}) => [{opacity: pressed ? 0.7 : 1}]}>
            <Image
              source={{uri: images?.[1]?.image}}
              style={dynamicStyles.rightImage}
              resizeMode="cover"
            />
          </Pressable>
          <Pressable
            onPress={() =>
              images?.[2]?.image && images[2].url && handlePress(images[2].url)
            }
            style={({pressed}) => [{opacity: pressed ? 0.7 : 1, marginTop: 8}]}>
            <Image
              source={{uri: images?.[2]?.image}}
              style={dynamicStyles.rightImageWithMargin}
              resizeMode="cover"
            />
          </Pressable>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    padding: 12,
    backgroundColor: '#fff',
  },
  headerContainer: {
    marginBottom: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerImage: {
    width: '100%',
    height: 54,
    backgroundColor: '#fff',
  },
  headerText: {
    // fontWeight: '900',
    width: '100%',
  },
  gridRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  leftImage: {
    borderRadius: 10,
    marginRight: 8,
    flexShrink: 0,
    backgroundColor: '#fff',
  },
  rightColumn: {
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
  rightImage: {
    borderRadius: 10,
    width: 120,
    height: 120,
    backgroundColor: '#fff',
  },
});

export default React.memo(BentoGridLayout, areEqual);
