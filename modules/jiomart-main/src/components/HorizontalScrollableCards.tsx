import {FlatList, StyleSheet, View, Pressable} from 'react-native';
import FastImage from 'react-native-fast-image';
import {getBaseURL} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import {rh, rw} from '../../.../../../jiomart-common/src/JMResponsive';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {SliderDetails} from '../../../jiomart-home/src/utils/HomePageTypes';
import {
  navBeanObj,
  NavigationType,
} from '../../../jiomart-common/src/JMNavGraphUtil';
import React, {useCallback, useMemo} from 'react';
import isEqual from 'lodash.isequal';

interface HorizontalScrollableCardsProps {
  title: string;
  images: SliderDetails[];
  imageHeight?: number;
  imageWidth?: number;
  onSeeAllClick?: () => void;
  onClick: any;
}

// Custom comparison function for React.memo
const areEqual = (
  prevProps: HorizontalScrollableCardsProps,
  nextProps: HorizontalScrollableCardsProps,
) => {
  return (
    prevProps.title === nextProps.title &&
    isEqual(prevProps.images, nextProps.images) &&
    prevProps.imageHeight === nextProps.imageHeight &&
    prevProps.imageWidth === nextProps.imageWidth &&
    prevProps.onSeeAllClick === nextProps.onSeeAllClick &&
    prevProps.onClick === nextProps.onClick
  );
};

// Memoized RenderItem component
const RenderItem = React.memo<{
  item: SliderDetails;
  index: number;
  imageHeight: number;
  imageWidth: number;
  onClick: any;
}>(
  ({item, index, imageHeight, imageWidth, onClick}) => {
    const handlePress = useCallback(() => {
      item.url &&
        onClick(
          navBeanObj({
            source: '',
            destination: 'CommonWebViewScreen',
            actionType: 'T003',
            headerType: 9,
            headerVisibility: 2,
            navigationType: NavigationType.PUSH,
            actionUrl: item.url,
            loginRequired: false,
          }),
        );
    }, [item.url, onClick]);

    const imageStyle = useMemo(
      () => [styles.image, {height: imageHeight, width: imageWidth}],
      [imageHeight, imageWidth],
    );

    return (
      <Pressable key={index} onPress={handlePress}>
        <FastImage
          source={{
            // uri: `${getBaseURL()}/images/product/original/${item}`,
            uri: item.image,
          }}
          style={imageStyle}
          resizeMode={FastImage.resizeMode.cover}
        />
      </Pressable>
    );
  },
  (prevProps, nextProps) => {
    return (
      isEqual(prevProps.item, nextProps.item) &&
      prevProps.index === nextProps.index &&
      prevProps.imageHeight === nextProps.imageHeight &&
      prevProps.imageWidth === nextProps.imageWidth &&
      prevProps.onClick === nextProps.onClick
    );
  },
);

RenderItem.displayName = 'RenderItem';

const HorizontalScrollableCards: React.FC<
  HorizontalScrollableCardsProps
> = props => {
  // Memoize default image dimensions
  const imageHeight = useMemo(
    () => props.imageHeight ?? 120,
    [props.imageHeight],
  );
  const imageWidth = useMemo(() => props.imageWidth ?? 120, [props.imageWidth]);

  // Memoize renderItem function for FlatList
  const renderItem = useCallback(
    ({item, index}: {item: SliderDetails; index: number}) => (
      <RenderItem
        item={item}
        index={index}
        imageHeight={imageHeight}
        imageWidth={imageWidth}
        onClick={props.onClick}
      />
    ),
    [imageHeight, imageWidth, props.onClick],
  );

  // Memoize keyExtractor
  const keyExtractor = useCallback(
    (item: SliderDetails, index: number) =>
      item.id?.toString() || item.url || index.toString(),
    [],
  );

  // Memoize onSeeAllPress
  const onSeeAllPress = useCallback(() => {
    props.onSeeAllClick?.();
    console.log('See All');
  }, [props.onSeeAllClick]);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <JioText
          text={props.title}
          color="black"
          appearance={JioTypography.HEADING_XXS}
          style={styles.title}
        />
        {props.onSeeAllClick && (
          <Pressable onPress={onSeeAllPress} style={styles.seeAll}>
            <JioText
              text="See All"
              color="primary_60"
              appearance={JioTypography.BODY_XS_BOLD}
              style={styles.seeAllText}
            />
          </Pressable>
        )}
      </View>
      <FlatList
        data={props.images}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.flatListContent}
        removeClippedSubviews={true}
        // maxToRenderPerBatch={5}
        // windowSize={10}
        initialNumToRender={3}
      />
    </View>
  );
};

export default React.memo(HorizontalScrollableCards, areEqual);

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    paddingVertical: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignContent: 'center',
  },
  title: {
    paddingLeft: 16,
    paddingBottom: 12,
    // fontWeight: '900',
  },
  seeAll: {
    paddingHorizontal: 16,
  },
  seeAllText: {
    fontWeight: '700',
  },
  flatListContent: {
    paddingHorizontal: 16,
    gap: 12,
  },
  image: {
    borderRadius: 12,
    backgroundColor: 'grey',
  },
});
