import {FlatList, StyleSheet, View, Pressable} from 'react-native';
import FastImage from 'react-native-fast-image';
import {getBaseURL} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import {rh, rw} from '../../../jiomart-common/src/JMResponsive';
import {JioText} from '@jio/rn_components';
import {JioTypography, ColorToken} from '@jio/rn_components/src/index.types';

interface TitleWord {
  text: string;
  color: keyof ColorToken;
}

interface HorizontalBrandListRoundedTopProps {
  title: string | TitleWord[];
  images: {
    brandName: string;
    brandImage: string;
    productImage: string;
  }[];
  onImagePress?: (imageUrl: string, index: number) => void;
  brandHeight: number;
  brandWidth: number;
  brandColor: string;
  productHeight: number;
  productWidth: number;
  productColor: string;
  borderColor: string;
  backgroundColor?: string;
  defaultTitleColor?: keyof ColorToken;
}

const renderItem = (
  {
    item,
    index,
  }: {
    item: {
      brandName: string;
      brandImage: string;
      productImage: string;
    };
    index: number;
  },
  onPress?: (url: string, idx: number) => void,
  brandHeight?: number,
  brandWidth?: number,
  productHeight?: number,
  productWidth?: number,
  borderColor?: string,
  brandColor?: string,
  productColor?: string,
) => {
  return (
    <Pressable
      onPress={() => onPress?.(item.brandImage, index)}
      style={[
        styles.itemView,
        {borderColor: borderColor, backgroundColor: productColor},
      ]}>
      <FastImage
        source={{
          // uri: `${getBaseURL()}/images/product/original/${item}`,
          uri: item?.brandImage,
        }}
        style={[
          styles.topImage,
          {
            height: brandHeight,
            width: brandWidth,
          },
        ]}
        resizeMode={FastImage.resizeMode.cover}
      />
      <FastImage
        source={{
          // uri: `${getBaseURL()}/images/product/original/${item}`,
          uri: item?.productImage,
        }}
        style={{
          height: productHeight,
          width: productWidth,
          backgroundColor: brandColor,
          borderBottomLeftRadius: 8,
          borderBottomRightRadius: 8,
        }}
        resizeMode={FastImage.resizeMode.cover}
      />
    </Pressable>
  );
};

const HorizontalBrandListRoundedTop = (
  props: HorizontalBrandListRoundedTopProps,
) => {
  const renderTitle = () => {
    if (Array.isArray(props.title)) {
      return (
        <View style={styles.titleContainer}>
          {props.title.map((word, index) => (
            <JioText
              key={index}
              text={word.text}
              color={word.color}
              appearance={JioTypography.HEADING_XS}
              style={styles.titleWord}
              textAlign="center"
            />
          ))}
        </View>
      );
    }
    return (
      <JioText
        text={props.title}
        color={props.defaultTitleColor || 'black'}
        appearance={JioTypography.HEADING_XS}
        style={styles.title}
        textAlign="center"
      />
    );
  };

  return (
    <View
      style={[
        styles.container,
        {backgroundColor: props.backgroundColor || 'white'},
      ]}>
      <View style={styles.header}>{renderTitle()}</View>
      <FlatList
        data={props.images}
        renderItem={item =>
          renderItem(
            item,
            props.onImagePress,
            props.brandHeight,
            props.brandWidth,
            props.productHeight,
            props.productWidth,
            props.borderColor,
            props.brandColor,
            props.productColor,
          )
        }
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.flatListContent}
      />
    </View>
  );
};
export default HorizontalBrandListRoundedTop;

const styles = StyleSheet.create({
  container: {
    paddingBottom: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignContent: 'center',
  },
  title: {
    padding: 22,
    fontWeight: '900',
    width: '100%',
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 22,
    width: '100%',
  },
  titleWord: {
    fontWeight: '900',
    marginHorizontal: 2,
  },
  flatListContent: {
    paddingHorizontal: 16,
    gap: 12,
  },
  topImage: {
    borderTopLeftRadius: 100,
    borderTopRightRadius: 100,
    zIndex: 1,
    marginBottom: -5,
  },
  itemView: {
    borderTopLeftRadius: 100,
    borderTopRightRadius: 100,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    borderWidth: 1,
    gap: -4,
    alignItems: 'center',
  },
});
