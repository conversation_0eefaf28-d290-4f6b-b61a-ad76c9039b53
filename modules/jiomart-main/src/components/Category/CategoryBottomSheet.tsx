import React, {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import {
  View,
  StyleSheet,
  Image,
  ImageSourcePropType,
  SafeAreaView,
} from 'react-native';
import JioText from '@jio/rn_components/src/components/JioText/JioText';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {useNavigation} from '@react-navigation/native';
import {rh, rw} from '../../../../jiomart-common/src/JMResponsive';
import Accordion, { AccordionPanel } from '../../../../jiomart-general/src/ui/Accordion/Accordion';
import ProductsBottomSheet from './ProductsBottomSheet';

type CategoryBottomSheetProps = {
  allCategory: any[];
  selectedL1Category: string;
  selectedL3Category: string;
  closeCatBtmSheet: () => void;
  onPressL3Category?: (data: any) => void;
};

export interface ScrollViewCategoryBottomSheet {
  scrollToTop: () => void;
}

type ImageType = ImageSourcePropType | {uri: string} | undefined;

function L2CategoryBottomSheetHeader(props) {
  return (
    <View style={styles.content_container}>
      <Image
        style={{
          width: rw(40),
          height: rw(40),
          borderRadius: 25,
        }}
        source={
          props.imageUrl
            ? props.imageUrl
            : 'https://cdn.pixelbin.io/v2/jiomart-fynd/jio-np/wrkr/jmrtz0/organization/64b003218f39a39ec141e0e1/theme/assets/jiomart-default-image.959a1b7a3b2d6bbecb905441f2832a89.png'
        }
      />
      <JioText
        ellipsizeMode="tail"
        maxLines={2}
        style={[
          {
            color: true ? '#141414' : '#000000A6',
          },
        ]}
        color="black"
        appearance={
          props.open ? JioTypography.BODY_XS_BOLD : JioTypography.BODY_XS
        }
        text={
          props.l2Category?.name.includes('::')
            ? props.l2Category?.name.split('::').pop()
            : props.l2Category?.name
        }
      />
    </View>
  );
}

const CategoryBottomSheet = forwardRef<
  ScrollViewCategoryBottomSheet,
  CategoryBottomSheetProps
>((props, ref) => {
  const {
    allCategory,
    selectedL1Category,
    selectedL3Category,
    onPressL3Category,
    closeCatBtmSheet,
  } = props;
  const navigation = useNavigation();
  const [openedIndex, setOpenedIndex] = useState<number | null>(null);
  const scrollViewRef = useRef(null);
  useImperativeHandle(ref, () => ({
    scrollToTop() {
      if (scrollViewRef.current) {
        scrollViewRef.current?.scrollTo({x: 0, y: 0, animated: true});
      }
    },
  }));

  const openPLP = useCallback(
    data => {
      closeCatBtmSheet?.();
      onPressL3Category?.(data);
    },
    [closeCatBtmSheet, onPressL3Category],
  );

  return (
    <SafeAreaView>
      {allCategory?.map?.((l1Category, l1index) => {
        if (l1Category.slug === selectedL1Category) {
          return (
            <Accordion
              style={{marginHorizontal: 24}}
              onChange={data => {
                setOpenedIndex(data);
              }}
              key={`l1-${l1Category?.slug}-${l1index}-acc`}>
              {l1Category?.childs != null && l1Category?.childs.length != 0
                ? l1Category?.childs.map((l2Category, l2index) => {
                    let isOpened: boolean;
                    const imageUrl: ImageType = {
                      uri: l2Category?.logo?.url
                        ? l2Category?.logo?.url
                        : 'https://cdn.pixelbin.io/v2/jiomart-fynd/jio-np/wrkr/jmrtz0/organization/64b003218f39a39ec141e0e1/theme/assets/jiomart-default-image.959a1b7a3b2d6bbecb905441f2832a89.png',
                    };
                    if (
                      l2Category?.childs &&
                      Array.isArray(l2Category?.childs) &&
                      l2Category?.childs.length > 0 &&
                      l2Category?.childs
                        .filter(l3Slug =>
                          l3Slug?.slug === selectedL3Category ? true : null,
                        )
                        .filter(removeNull => removeNull !== null)[0]
                    ) {
                      isOpened = true;
                    }

                    return (
                      <AccordionPanel
                        key={`l2-${l2Category?.slug}-${l2index}`}
                        open={openedIndex! ? l2index == openedIndex : isOpened}
                        accordionHeader={
                          <L2CategoryBottomSheetHeader
                            key={`l2-${l2Category?.slug}-${l2index}-header`}
                            imageUrl={imageUrl}
                            l2Category={l2Category}
                            open={
                              openedIndex != null
                                ? l2index == openedIndex
                                : isOpened
                            }
                          />
                        }>
                        {l2Category?.childs?.map(
                          (l3Category: any, index: number) => {
                            return (
                              <ProductsBottomSheet
                                key={`l3-${l3Category?.slug}-${index}`}
                                l3CategoryData={l3Category}
                                hideDivider={
                                  index == l2Category?.childs?.length - 1
                                }
                                selected={
                                  l3Category?.slug === selectedL3Category
                                }
                                title={
                                  l3Category?.name?.includes?.('::')
                                    ? l3Category?.name?.split('::')?.pop()
                                    : l3Category?.name ?? ''
                                }
                                onPress={() => {
                                  openPLP(l3Category);
                                }}
                              />
                            );
                          },
                        )}
                      </AccordionPanel>
                    );
                  })
                : null}
            </Accordion>
          );
        }
      })}
    </SafeAreaView>
  );
});

export default CategoryBottomSheet;

const styles = StyleSheet.create({
  content_container: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: rh(8),
  },
  container: {
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: rh(16),
  },
  title_container: {
    height: rh(40),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginHorizontal: rw(24),
  },
  headerText: {
    color: '#141414',
    fontSize: 16,
    fontWeight: '900',
    fontFamily: 'JioType-Medium',
  },
  closeBtn: {
    width: 40,
    height: 40,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    borderRadius: 20,
    marginRight: rw(12),
  },
});
