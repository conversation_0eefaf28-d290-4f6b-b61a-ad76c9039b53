import React, {useEffect, useState, useCallback, useMemo} from 'react';
import {JioText} from '@jio/rn_components';
import {JioTypography, ColorToken} from '@jio/rn_components/src/index.types';
import {
  navBeanObj,
  NavigationType,
} from '../../../jiomart-common/src/JMNavGraphUtil';
import {Dimensions, Pressable, StyleSheet, View} from 'react-native';
import {SliderDetails} from '../../../jiomart-home/src/utils/HomePageTypes';
import {Image} from 'react-native';
import WebView from 'react-native-webview';

interface GridCategoryCardProps {
  title?: string;
  banner?: string;
  onClick: any;
  items: SliderDetails[];
  columns?: number;
  paddingTop?: number;
  paddingBottom?: number;
  backgroundColor?: string;
  imageBackgroundColor?: string;
  borderBottomWidth?: number;
  margin?: number;
  borderRadius?: number;
  paddingHorizontal: number;
  imageGap: number;
  imageAspectRatio: number;
  bannerHeight?: number;
  imageBorderRadius?: number;
  itemTitleFontWeight?:
    | 'normal'
    | 'bold'
    | '100'
    | '200'
    | '300'
    | '400'
    | '500'
    | '600'
    | '700'
    | '800'
    | '900';
  itemTitleColor?: keyof ColorToken;
  imagePaddingTop?: number;
  imagePaddingRight?: number;
  imagePaddingBottom?: number;
  imagePaddingLeft?: number;
  bannerCornerRounded?: boolean;
  showGridTitle?: boolean;
  imageMarginTop?: number;
  imageMarginBottom?: number;
  sliceToFullRows: boolean;
}

// Memoized individual grid item component
interface GridItemProps {
  item: SliderDetails;
  styles: any;
  handlePress: (url: string) => void;
  showGridTitle: boolean;
  itemTitleColor: keyof ColorToken;
}

const GridItem = React.memo<GridItemProps>(
  ({item, styles, handlePress, showGridTitle, itemTitleColor}) => (
    <Pressable
      onPress={() => item.url && handlePress(item.url)}
      style={[styles.itemContainer, styles.itemContainerWithDimensions]}>
      <View style={styles.imageBox}>
        <Image
          source={{uri: item.image}}
          style={styles.image}
          resizeMode="contain"
        />
      </View>
      {item.name && showGridTitle && (
        <JioText
          text={item.name}
          style={styles.itemTitle}
          appearance={JioTypography.BODY_XXS}
          color={itemTitleColor}
          maxLines={2}
          minLines={2}
        />
      )}
    </Pressable>
  ),
  (prevProps, nextProps) => {
    // Only re-render if item data or essential props change
    return (
      prevProps.item.id === nextProps.item.id &&
      prevProps.item.image === nextProps.item.image &&
      prevProps.item.name === nextProps.item.name &&
      prevProps.item.url === nextProps.item.url &&
      prevProps.showGridTitle === nextProps.showGridTitle &&
      prevProps.itemTitleColor === nextProps.itemTitleColor &&
      prevProps.handlePress === nextProps.handlePress
    );
  },
);

// Helper function to check if URL is a GIF
const isGifUrl = (url: string): boolean => {
  return url?.toLowerCase().includes('.gif') || false;
};

const GridCategoryCard = React.memo(
  (props: GridCategoryCardProps) => {
    console.log('GridCategoryCard items:', props.items);
    const {
      columns = 3,
      paddingTop = 24,
      paddingBottom = 40,
      imageBackgroundColor = 'white',
      imageGap = 12,
      imageAspectRatio = 1.2,
      bannerHeight = 80,
      imageBorderRadius = 10,
      imageMarginTop = 12,
      imageMarginBottom = 12,
      itemTitleFontWeight = '500',
      itemTitleColor = 'primary_grey_80',
      imagePaddingTop = 0,
      imagePaddingRight = 0,
      imagePaddingBottom = 0,
      imagePaddingLeft = 0,
      bannerCornerRounded = false,
      showGridTitle = false,
      sliceToFullRows,
    } = props;

    // Memoize screen width to avoid recalculation on every render
    const screenWidth = useMemo(() => Dimensions.get('window').width, []);

    // Memoize item dimensions calculation
    const {itemWidth, itemHeight} = useMemo(() => {
      const calculatedItemWidth =
        (screenWidth -
          (props.margin !== undefined
            ? props.paddingHorizontal * 2 + props.margin * 2
            : props.paddingHorizontal * 2) -
          (columns - 1) * imageGap) /
        columns;
      const calculatedItemHeight = calculatedItemWidth * imageAspectRatio;

      return {
        itemWidth: calculatedItemWidth,
        itemHeight: calculatedItemHeight,
      };
    }, [
      screenWidth,
      props.margin,
      props.paddingHorizontal,
      columns,
      imageGap,
      imageAspectRatio,
    ]);

    const [dynamicBannerHeight, setDynamicBannerHeight] =
      useState(bannerHeight);
    useEffect(() => {
      if (props.banner) {
        Image.getSize(
          props.banner,
          (width, height) => {
            const availableWidth =
              screenWidth - (props.margin ? props.margin * 2 : 0);
            const calculatedHeight = (height / width) * availableWidth;
            setDynamicBannerHeight(calculatedHeight);
          },
          error => {
            setDynamicBannerHeight(bannerHeight);
          },
        );
      } else {
        setDynamicBannerHeight(bannerHeight);
      }
    }, [props.banner, screenWidth, props.margin]);

    // Memoize items to render calculation
    const itemsToRender = useMemo(() => {
      if (!props.items?.length) return [];

      return sliceToFullRows
        ? props.items.slice(
            0,
            Math.floor(props.items.length / columns) * columns,
          )
        : props.items;
    }, [props.items, sliceToFullRows, columns]);

    // Memoize styles to prevent recreation on every render
    const styles = useMemo(
      () =>
        StyleSheet.create({
          container: {
            flex: 1,
            paddingBottom: paddingBottom,
            borderRadius: props.borderRadius ? props.borderRadius : 0,
            overflow: 'hidden',
          },
          header: {
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignContent: 'center',
            paddingHorizontal: props.paddingHorizontal,
            paddingVertical: 10,
          },
          title: {
            // paddingLeft: 6,
            // fontWeight: '900',
          },
          banner: {
            width: '100%',
            height: dynamicBannerHeight,
            borderTopLeftRadius: props.borderRadius,
            borderTopRightRadius: props.borderRadius,
            borderBottomLeftRadius: bannerCornerRounded
              ? props.borderRadius
              : undefined,
            borderBottomRightRadius: bannerCornerRounded
              ? props.borderRadius
              : undefined,
          },
          gridContainer: {
            flexDirection: 'row',
            flexWrap: 'wrap',
            gap: imageGap,
          },
          itemContainer: {
            marginBottom: imageMarginBottom,
            marginTop: imageMarginTop,
          },
          imageBox: {
            paddingTop: imagePaddingTop,
            paddingRight: imagePaddingRight,
            paddingBottom: imagePaddingBottom,
            paddingLeft: imagePaddingLeft,
            borderRadius: imageBorderRadius,
            backgroundColor: imageBackgroundColor,
          },
          image: {
            width: '100%',
            height: '100%',
            borderRadius: imageBorderRadius,
            backgroundColor: imageBackgroundColor,
          },
          itemTitle: {
            textAlign: 'center',
            fontWeight: itemTitleFontWeight,
          },
          outerContainer: {
            backgroundColor: 'white',
            borderBottomWidth: props.borderBottomWidth,
            borderColor: '#00000020',
          },
          mainContainer: {
            paddingTop: paddingTop,
            backgroundColor: props.backgroundColor
              ? props.backgroundColor
              : 'white',
            margin: props.margin ? props.margin : 0,
            borderRadius: props.borderRadius ? props.borderRadius : 0,
          },
          gridContainerWithPadding: {
            paddingHorizontal: props.paddingHorizontal,
          },
          itemContainerWithDimensions: {
            width: itemWidth,
            height: itemHeight,
          },
        }),
      [
        paddingBottom,
        props.paddingHorizontal,
        dynamicBannerHeight,
        props.borderRadius,
        bannerCornerRounded,
        imageGap,
        imageMarginBottom,
        imageMarginTop,
        imagePaddingTop,
        imagePaddingRight,
        imagePaddingBottom,
        imagePaddingLeft,
        imageBorderRadius,
        imageBackgroundColor,
        itemTitleFontWeight,
        props.backgroundColor,
        props.margin,
        props.borderBottomWidth,
        paddingTop,
        itemWidth,
        itemHeight,
      ],
    );

    // Memoize handlePress to prevent recreation on every render
    const handlePress = useCallback(
      (url: string) => {
        // TODO: Replace with navigation or linking logic as needed
        console.log('Pressed URL:', url);
        props?.onClick(
          navBeanObj({
            source: '',
            destination: 'CommonWebViewScreen',
            actionType: 'T003',
            headerType: 9,
            headerVisibility: 2,
            navigationType: NavigationType.PUSH,
            actionUrl: url,
            loginRequired: false,
          }),
        );
      },
      [props.onClick],
    );

    // Early return if no items - memoized check
    if (!itemsToRender.length) {
      return null;
    }

    return (
      <View style={styles.outerContainer}>
        <View style={[styles.container, styles.mainContainer]}>
          {props.banner !== undefined ? (
            <View style={styles.header}>
              {/* Conditional rendering: WebView for GIFs, Image for others */}
              {isGifUrl(props.banner) ? (
                <View style={[styles.banner, {overflow: 'hidden'}]}>
                  <WebView
                    source={{uri: props.banner}}
                    style={styles.banner}
                    scrollEnabled={false}
                    showsHorizontalScrollIndicator={false}
                    showsVerticalScrollIndicator={false}
                    scalesPageToFit={true}
                    startInLoadingState={false}
                    javaScriptEnabled={false}
                  />
                </View>
              ) : (
                <Image
                  source={{
                    uri: props.banner,
                  }}
                  style={styles.banner}
                  resizeMode="contain"
                />
              )}
            </View>
          ) : props.title ? (
            <View style={styles.header}>
              <JioText
                text={props.title}
                color="black"
                appearance={JioTypography.HEADING_XXS}
                style={styles.title}
              />
            </View>
          ) : null}

          <View style={[styles.gridContainer, styles.gridContainerWithPadding]}>
            {itemsToRender.map(item => (
              <GridItem
                key={item.id}
                item={item}
                styles={styles}
                handlePress={handlePress}
                showGridTitle={showGridTitle}
                itemTitleColor={itemTitleColor}
              />
            ))}
          </View>
        </View>
      </View>
    );
  },
  (prevProps, nextProps) => {
    // Custom comparison function for React.memo
    // Only re-render if essential props have changed
    return (
      prevProps.title === nextProps.title &&
      prevProps.banner === nextProps.banner &&
      prevProps.items === nextProps.items &&
      prevProps.columns === nextProps.columns &&
      prevProps.paddingTop === nextProps.paddingTop &&
      prevProps.paddingBottom === nextProps.paddingBottom &&
      prevProps.backgroundColor === nextProps.backgroundColor &&
      prevProps.imageBackgroundColor === nextProps.imageBackgroundColor &&
      prevProps.borderBottomWidth === nextProps.borderBottomWidth &&
      prevProps.margin === nextProps.margin &&
      prevProps.borderRadius === nextProps.borderRadius &&
      prevProps.paddingHorizontal === nextProps.paddingHorizontal &&
      prevProps.imageGap === nextProps.imageGap &&
      prevProps.imageAspectRatio === nextProps.imageAspectRatio &&
      prevProps.bannerHeight === nextProps.bannerHeight &&
      prevProps.imageBorderRadius === nextProps.imageBorderRadius &&
      prevProps.itemTitleFontWeight === nextProps.itemTitleFontWeight &&
      prevProps.itemTitleColor === nextProps.itemTitleColor &&
      prevProps.imagePaddingTop === nextProps.imagePaddingTop &&
      prevProps.imagePaddingRight === nextProps.imagePaddingRight &&
      prevProps.imagePaddingBottom === nextProps.imagePaddingBottom &&
      prevProps.imagePaddingLeft === nextProps.imagePaddingLeft &&
      prevProps.bannerCornerRounded === nextProps.bannerCornerRounded &&
      prevProps.showGridTitle === nextProps.showGridTitle &&
      prevProps.imageMarginTop === nextProps.imageMarginTop &&
      prevProps.imageMarginBottom === nextProps.imageMarginBottom &&
      prevProps.sliceToFullRows === nextProps.sliceToFullRows &&
      prevProps.onClick === nextProps.onClick
    );
  },
);

export default GridCategoryCard;
