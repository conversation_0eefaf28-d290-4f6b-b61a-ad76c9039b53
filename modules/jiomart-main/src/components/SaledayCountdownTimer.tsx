import React, {useState, useEffect, useCallback, useMemo} from 'react';
import {StyleSheet, View, ImageBackground, Dimensions} from 'react-native';
import {JioText} from '@jio/rn_components';
import {
  JioTypography,
  type ColorToken,
} from '@jio/rn_components/src/index.types';
import FastImage from 'react-native-fast-image';
import {rh, rw} from '../../../jiomart-common/src/JMResponsive';
import isEqual from 'lodash.isequal';
import { getImageHeight } from '../../../jiomart-common/src/utils/JMImageUtility';

interface SaledayCountdownTimerProps {
  saleDateTime: string; // e.g., "2024-12-31T23:59:59"
  bannerImage?: string;
  bannerText?: string;
  bannerTextColor?: keyof ColorToken | undefined;
  margin?: number;
  backgroundColor?: string;
  borderRadius?: number;
}

interface TimeLeft {
  days?: number;
  hours?: number;
  minutes?: number;
  seconds?: number;
}

// Custom comparison function for React.memo
const areEqual = (
  prevProps: SaledayCountdownTimerProps,
  nextProps: SaledayCountdownTimerProps,
) => {
  return (
    prevProps.saleDateTime === nextProps.saleDateTime &&
    prevProps.bannerImage === nextProps.bannerImage &&
    prevProps.bannerText === nextProps.bannerText &&
    prevProps.bannerTextColor === nextProps.bannerTextColor &&
    prevProps.margin === nextProps.margin &&
    prevProps.backgroundColor === nextProps.backgroundColor &&
    prevProps.borderRadius === nextProps.borderRadius
  );
};

function use(data: any[]) {
  const [carouselHeight, setCarouselHeight] = useState<number>(0);

  useEffect(() => {
    if (!data || data.length === 0) return;

    const calculate = async () => {
      const heightPromises = data.map(async item => {
        try {
          if (item.slide_type_name === JMSliderTypeName.IMAGE && item.image) {
            return await getImageHeight(item.image);
          }
          if (
            item.slide_type_name === JMSliderTypeName.ANIMATION &&
            item.video_file
          ) {
            return await getAnimationHeight(item.video_file);
          }
          if (item.slide_type_name === JMSliderTypeName.JIO_ADS) {
            return getAdHeight(item?.img_alt, 20);
          }
        } catch (err) {
          console.warn('Skipping item with error:', err);
        }
        return 0;
      });

      const heights = await Promise.all(heightPromises);
      const validHeights = heights.filter(h => h > 0);
      if (validHeights.length > 0) {
        setCarouselHeight(Math.min(...validHeights));
      }
    };

    calculate();
  }, [data]);

  return carouselHeight;
}

const SaledayCountdownTimer: React.FC<SaledayCountdownTimerProps> = ({
  saleDateTime,
  bannerImage,
  bannerText,
  bannerTextColor,
  margin,
  backgroundColor,
  borderRadius = 18,
}) => {
  const calculateTimeLeft = useCallback(() => {
    const difference = +new Date(saleDateTime) - +new Date();
    let timeLeft: TimeLeft = {};

    if (difference > 0) {
      timeLeft = {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
        minutes: Math.floor((difference / 1000 / 60) % 60),
        seconds: Math.floor((difference / 1000) % 60),
      };
    }
    return timeLeft;
  }, [saleDateTime]);

  const [timeLeft, setTimeLeft] = useState<TimeLeft>(calculateTimeLeft());

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    return () => clearInterval(timer);
  }, [calculateTimeLeft]);

  // Memoize container style
  const containerStyle = useMemo(
    () => [
      styles.container,
      {
        margin: margin !== undefined && margin !== null ? margin : rw(12),
        borderRadius: borderRadius,
        height: getImageHeight(bannerImage ?? ''),
      },
    ],
    [margin, borderRadius, bannerImage],
  );

  // Memoize image style
  const imageStyle = useMemo(
    () => [styles.imageStyle, {borderRadius: borderRadius}],
    [borderRadius],
  );

  // Memoize timer label function
  const getTimerLabel = useCallback((interval: string) => {
    if (interval === 'hours') {
      return 'Hrs';
    } else if (interval === 'minutes') {
      return 'Mins';
    } else if (interval === 'seconds') {
      return 'Secs';
    } else {
      return interval.charAt(0).toUpperCase() + interval.slice(1);
    }
  }, []);

  // Memoize timer components
  const timerComponents = useMemo(() => {
    return Object.keys(timeLeft).map((interval, index) => {
      const value = timeLeft[interval as keyof TimeLeft];
      if (value === undefined) {
        return null;
      }

      return (
        <React.Fragment key={interval}>
          <View style={[styles.timeUnitContainer]}>
            <JioText
              text={value < 10 ? `0${value}` : String(value)}
              color="feedback_error_50"
              appearance={JioTypography.HEADING_XXS}
              style={styles.timeUnitValue}
            />
            <JioText
              text={getTimerLabel(interval)}
              color="primary_grey_80"
              appearance={JioTypography.BODY_XS}
              style={styles.timeUnitLabel}
            />
          </View>
          {index < Object.keys(timeLeft).length - 1 && (
            <JioText
              text=":"
              color="white"
              appearance={JioTypography.HEADING_XS}
              style={styles.separator}
            />
          )}
        </React.Fragment>
      );
    });
  }, [timeLeft, getTimerLabel]);


  return (
    <View style={{backgroundColor: backgroundColor}}>
      <ImageBackground
        source={{uri: bannerImage}}
        resizeMode="contain"
        style={containerStyle}
        imageStyle={imageStyle}>
        <View style={styles.overlayInner}>
          <JioText
            text={bannerText ? bannerText : ''}
            color={bannerTextColor}
            appearance={JioTypography.HEADING_XS}
            style={styles.bannerText}
          />
          <View style={styles.countdownContainer}>
            {timerComponents.length ? (
              timerComponents
            ) : (
              <JioText text=" " color="white" />
            )}
          </View>
        </View>
      </ImageBackground>
    </View>
  );
};

export default React.memo(SaledayCountdownTimer, areEqual);

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    // marginTop: rh(12),
  },
  imageStyle: {
    // borderRadius: 8,
  },
  overlayInner: {
    flex: 1,
    // justifyContent: 'space-between',
    alignItems: 'center',
    // backgroundColor: 'rgba(0,0,0,0.2)',
  },
  bannerText: {
    fontWeight: '900',
    marginHorizontal: 20,
    marginTop: 10,
    marginBottom: 10,
  },
  countdownContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: rw(3), // Minimized gap
    alignSelf: 'stretch',
    width: '100%',
    bottom: 5,
    position: 'absolute',
  },
  timeUnitContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  timeUnitValue: {
    fontWeight: 'bold',
  },
  timeUnitLabel: {
    // marginTop: rh(2),
  },
  separator: {
    fontWeight: 'bold',
    alignSelf: 'center',
  },
});
