import React, {useRef, useEffect, useState} from 'react';
import {View, Text, StyleSheet, FlatList, TouchableOpacity} from 'react-native';
import FastImage from 'react-native-fast-image';

interface AutoScrollingProductsProps {
  images: string[];
  imagesBottom?: string[];
  headerImage?: string;
  headerText?: string;
  backgroundColor?: string;
  imageWidth?: number;
  imageHeight?: number;
  scrollSpeed?: number;
  onImagePress?: (item: string, index: number, row: 'top' | 'bottom') => void;
}

const AutoScrollingProducts: React.FC<AutoScrollingProductsProps> = ({
  images,
  imagesBottom = [],
  headerImage,
  headerText,
  backgroundColor = 'white',
  imageWidth = 140,
  imageHeight = 170,
  scrollSpeed = 30,
  onImagePress,
}) => {
  const flatListRefTop = useRef<FlatList>(null);
  const flatListRefBottom = useRef<FlatList>(null);
  const [dataTop, setDataTop] = useState<string[]>([]);
  const [dataBottom, setDataBottom] = useState<string[]>([]);
  const [contentWidthTop, setContentWidthTop] = useState(0);
  const [contentWidthBottom, setContentWidthBottom] = useState(0);

  // Duplicate data for infinite scroll
  useEffect(() => {
    setDataTop([...images, ...images]);
  }, [images]);

  useEffect(() => {
    setDataBottom([...imagesBottom, ...imagesBottom]);
  }, [imagesBottom]);

  // Auto scroll logic for top row (left to right)
  useEffect(() => {
    let scrollValue = 0;
    let interval: any;
    if (dataTop.length > 0 && contentWidthTop > 0) {
      interval = setInterval(() => {
        scrollValue += 1;
        if (scrollValue > contentWidthTop / 2) {
          scrollValue = 0;
          flatListRefTop.current?.scrollToOffset({offset: 0, animated: false});
        } else {
          flatListRefTop.current?.scrollToOffset({
            offset: scrollValue,
            animated: false,
          });
        }
      }, scrollSpeed);
    }
    return () => clearInterval(interval);
  }, [dataTop, contentWidthTop, scrollSpeed]);

  // Auto scroll logic for bottom row (right to left)
  useEffect(() => {
    let scrollValue = 0;
    let interval: any;
    if (dataBottom.length > 0 && contentWidthBottom > 0) {
      interval = setInterval(() => {
        scrollValue += 1;
        if (scrollValue > contentWidthBottom / 2) {
          scrollValue = 0;
          flatListRefBottom.current?.scrollToOffset({
            offset: 0,
            animated: false,
          });
        } else {
          // Scroll from right to left
          flatListRefBottom.current?.scrollToOffset({
            offset: contentWidthBottom / 2 - scrollValue,
            animated: false,
          });
        }
      }, scrollSpeed);
    }
    return () => clearInterval(interval);
  }, [dataBottom, contentWidthBottom, scrollSpeed]);

  const renderItem =
    (row: 'top' | 'bottom') =>
    ({item, index}: {item: string; index: number}) =>
      (
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={() =>
            onImagePress?.(
              item,
              index % (row === 'top' ? images.length : imagesBottom.length),
              row,
            )
          }
          style={{width: imageWidth, height: imageHeight, marginRight: 12}}>
          <FastImage
            source={{uri: item}}
            style={{
              width: imageWidth,
              height: imageHeight,
              borderRadius: 12,
              backgroundColor: '#eee',
            }}
            resizeMode={FastImage.resizeMode.cover}
          />
        </TouchableOpacity>
      );

  return (
    <View style={[styles.container, {backgroundColor}]}>
      {(headerImage || headerText) && (
        <View style={styles.headerContainer}>
          {headerImage && (
            <FastImage
              source={{uri: headerImage}}
              style={styles.headerImage}
              resizeMode={FastImage.resizeMode.cover}
            />
          )}
          {headerText && <Text style={styles.headerText}>{headerText}</Text>}
        </View>
      )}
      <FlatList
        ref={flatListRefTop}
        data={dataTop}
        renderItem={renderItem('top')}
        keyExtractor={(_, idx) => `top-${idx}`}
        horizontal
        showsHorizontalScrollIndicator={false}
        scrollEnabled={false}
        getItemLayout={(_, index) => ({
          length: imageWidth + 12,
          offset: (imageWidth + 12) * index,
          index,
        })}
        contentContainerStyle={{alignItems: 'center', paddingHorizontal: 8}}
        onContentSizeChange={w => setContentWidthTop(w)}
        style={{marginBottom: 12}}
      />
      {imagesBottom.length > 0 && (
        <FlatList
          ref={flatListRefBottom}
          data={dataBottom}
          renderItem={renderItem('bottom')}
          keyExtractor={(_, idx) => `bottom-${idx}`}
          horizontal
          showsHorizontalScrollIndicator={false}
          scrollEnabled={false}
          getItemLayout={(_, index) => ({
            length: imageWidth + 12,
            offset: (imageWidth + 12) * index,
            index,
          })}
          contentContainerStyle={{alignItems: 'center', paddingHorizontal: 8}}
          onContentSizeChange={w => setContentWidthBottom(w)}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    paddingBottom: 12,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    // marginBottom: 8,
    // paddingHorizontal: 8,
  },
  headerImage: {
    width: '100%',
    height: 54,
    // marginRight: 8,
    // borderRadius: 8,
    // backgroundColor: '#eee',
  },
  headerText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#222',
  },
});

export default AutoScrollingProducts;
