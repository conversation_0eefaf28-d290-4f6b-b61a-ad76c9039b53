import React, {useCallback, useEffect, useState} from 'react';
import {View, StyleSheet, Pressable, Dimensions} from 'react-native';
import FastImage from 'react-native-fast-image';
import CustomCarousel from './CustomCarousel';
import {getBaseURL} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import {navigateTo} from '../../../jiomart-general/src/navigation/JMNavGraph';
import {
  navBeanObj,
  NavigationType,
} from '../../../jiomart-common/src/JMNavGraphUtil';
import {SliderDetails} from '../../../jiomart-home/src/utils/HomePageTypes';
import {JMSliderTypeName} from '../../../jiomart-home/src/types/JMHomeComponentType';
import JioAds from '../../../jiomart-general/src/JioAds/JioAds';
import {
  extractJioAdsData,
  getAdHeight,
} from '../../../jiomart-home/src/utils/HomeUtils';
import CustomMediaRendered from './CustomMediaRendered';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {checkAdStatusNB} from '../../../jiomart-general/src/bridge/JMRNBridge';
import isEqual from 'lodash.isequal';
import {
  getAnimationHeight,
  getImageHeight,
} from '../../../jiomart-common/src/utils/JMImageUtility';
import {getScreenDim} from '../../../jiomart-common/src/JMResponsive';
import JioMartShimmer from '../../../jiomart-general/src/ui/JioMartShimmer';
import {ShimmerKind} from '@jio/rn_components/src/index.types';
import { getScreenWidth } from '../../../jiomart-common/src/utils/JMCommonFunctions';

interface HeroTopCarouselProps {
  data: SliderDetails[];
  navigation: any;
  autoScroll?: boolean;
  showIndicator?: boolean;
  autoScrollTimer?: number;
  title?: string;
  peekValue?: number;
}

function useCarouselHeight(data: any[]) {
  const [carouselHeight, setCarouselHeight] = useState<number>(0);

  useEffect(() => {
    if (!data || data.length === 0) return;

    const calculate = async () => {
      const heightPromises = data.map(async item => {
        try {
          if (item.slide_type_name === JMSliderTypeName.IMAGE && item.image) {
            return await getImageHeight(item.image);
          }
          if (
            item.slide_type_name === JMSliderTypeName.ANIMATION &&
            item.video_file
          ) {
            return await getAnimationHeight(item.video_file);
          }
          if (item.slide_type_name === JMSliderTypeName.JIO_ADS) {
            return getAdHeight(item?.img_alt, 20);
          }
        } catch (err) {
          console.warn('Skipping item with error:', err);
        }
        return 0;
      });

      const heights = await Promise.all(heightPromises);
      const validHeights = heights.filter(h => h > 0);
      if (validHeights.length > 0) {
        setCarouselHeight(Math.min(...validHeights));
      }
    };

    calculate();
  }, [data]);

  return carouselHeight;
}

const HeroTopCarousel = ({
  data,
  navigation,
  autoScroll = false,
  showIndicator = false,
  autoScrollTimer = 3000,
  title = '',
  peekValue = 0,
}: HeroTopCarouselProps) => {
  const [filteredData, setFilteredData] = useState<SliderDetails[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const carouselHeight = useCarouselHeight(filteredData);

  const handlePress = (url: string) => {
    // TODO: Replace with navigation or linking logic as needed
    console.log('Pressed URL:', url);
    navigateTo(
      navBeanObj({
        source: '',
        destination: 'CommonWebViewScreen',
        actionType: 'T003',
        headerType: 9,
        headerVisibility: 2,
        navigationType: NavigationType.PUSH,
        loginRequired: false,
        actionUrl: `${getBaseURL()}/${url}`,
      }),
      navigation,
    );
  };

  const filterAdsFromRenderItemData = useCallback(
    async (items: SliderDetails[]) => {
      try {
        const results = await Promise.all(
          items.map(async item => {
            if (item?.slide_type_name === JMSliderTypeName.JIO_ADS) {
              const adsData = extractJioAdsData(item?.img_alt);
              const isValid = await checkAdStatusNB({
                adType: adsData.adType,
                adspotKey: adsData.adSpotId,
                adHeight: adsData.adHeight,
                adWidth: adsData.adWidth,
                adCustomHeight: adsData.adjHeight,
                adCustomWidth: 0,
              });
              return isValid ? item : null;
            }
            return item; // keep non-JioAds as is
          }),
        );

        // Now filter out nulls
        const filtered = results.filter(
          (item): item is SliderDetails => item !== null,
        );
        return filtered;
      } catch (error) {
        console.error('Error filtering ads from render item data:', error);
        throw error;
      }
    },
    [],
  );

  // Effect to handle async filtering of ads
  useEffect(() => {
    const processData = async () => {
      if (!data || data.length === 0) {
        setIsLoading(false);
        setHasError(false);
        setFilteredData([]);
        return;
      }

      try {
        setIsLoading(true);
        setHasError(false);
        const filtered = await filterAdsFromRenderItemData(data);
        setFilteredData(filtered);
        setHasError(false);
      } catch (error) {
        console.error('Failed to filter ads data:', error);
        setHasError(true);
        setFilteredData([]);
      } finally {
        setIsLoading(false);
      }
    };

    processData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  const renderItem = (item: SliderDetails) => {
    switch (item?.slide_type_name) {
      case JMSliderTypeName.JIO_ADS:
        var jioadData = extractJioAdsData(item?.img_alt);
        return (
          <JioAds
            style={{margin: 6}}
            {...jioadData}
            adjWidth={getScreenDim.width - 12 - peekValue * 2}
            adjHeight={carouselHeight - 12}
          />
        );
      case JMSliderTypeName.IMAGE:
        return (
          <Pressable
            style={[styles.imageContainer, {height: carouselHeight}]}
            onPress={() => item.url && handlePress(item.url)}>
            <FastImage
              source={{uri: item.image}}
              style={[styles.mediaContent, {height: carouselHeight - 12}]}
              resizeMode={FastImage.resizeMode.cover}
            />
          </Pressable>
        );
      case JMSliderTypeName.ANIMATION:
        return (
          <Pressable
            style={styles.imageContainer}
            onPress={() => item.url && handlePress(item.url)}>
            <View style={styles.mediaWrapper}>
              <CustomMediaRendered
                mediaUrl={`${getBaseURL()}/images/cms/aw_rbslider/slides/animations/${
                  item.video_file
                }`}
                customStyles={styles.mediaContent}
                imageResizeMode="contain"
              />
            </View>
          </Pressable>
        );
      default:
        return null;
    }
  };

  // Show shimmer while loading
  if (isLoading && data && data.length > 0) {
    return (
      <>
        {title ? (
          <View style={styles.header}>
            <JioText
              text={title}
              color="black"
              appearance={JioTypography.HEADING_XXS}
              style={styles.title}
            />
          </View>
        ) : null}
        <View
          style={[styles.container, {marginBottom: showIndicator ? 20 : 10}]}>
          <JioMartShimmer
            width={Dimensions.get('window').width - 20}
            height={179.2}
            kind={ShimmerKind.RECTANGLE}
            style={styles.shimmerContainer}
          />
        </View>
      </>
    );
  }

  // Don't render anything if there's an error, no data, or no filtered data
  if (
    carouselHeight === 0 ||
    hasError ||
    !filteredData ||
    (Array.isArray(filteredData) && filteredData.length === 0) ||
    filteredData.length === 0
  ) {
    return null;
  }

  return (
    <>
      {title ? (
        <View style={styles.header}>
          <JioText
            text={title}
            color="black"
            appearance={JioTypography.HEADING_XXS}
            style={styles.title}
          />
        </View>
      ) : null}
      <View style={[styles.container, {marginBottom: showIndicator ? 20 : 10}]}>
        <CustomCarousel
          autoScroll={autoScroll}
          showIndicator={showIndicator}
          items={filteredData}
          RenderItem={renderItem}
          autoScrollTimer={autoScrollTimer}
          peek={peekValue}
          customStyles={{
            itemList: {
              backgroundColor: 'white',
              height: carouselHeight,
              //gap: 10,
            },
            indicatorList: {
              ...styles.carouselIndicatorsView,
            },
            indicatorItemList: {
              ...styles.carouselIndicatorItem,
            },
          }}
        />
      </View>
    </>
  );
};

// Add React.memo for optimization
function areEqual(
  prevProps: HeroTopCarouselProps,
  nextProps: HeroTopCarouselProps,
) {
  return (
    isEqual(prevProps.data, nextProps.data) &&
    prevProps.autoScroll === nextProps.autoScroll &&
    prevProps.showIndicator === nextProps.showIndicator &&
    prevProps.autoScrollTimer === nextProps.autoScrollTimer
  );
}

export default React.memo(HeroTopCarousel, areEqual);

const styles = StyleSheet.create({
  container: {
    width: '100%',
    marginTop: 5,
  },
  imageContainer: {
    borderRadius: 16,
    padding: 6,
    overflow: 'hidden',
  },
  mediaContent: {
    borderRadius: 16,
  },
  mediaWrapper: {
    width: '100%',
    height: '100%',
    borderRadius: 16,
    overflow: 'hidden',
    backgroundColor: 'transparent',
  },
  carouselIndicatorsView: {
    //bottom: -7,
    paddingTop: 4,
  },
  carouselIndicatorItem: {},
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignContent: 'center',
  },
  title: {
    paddingLeft: 16,
    // paddingBottom: 12,
    // fontWeight: '900',
  },
  shimmerContainer: {
    borderRadius: 16,
    margin: 10,
  },
});
