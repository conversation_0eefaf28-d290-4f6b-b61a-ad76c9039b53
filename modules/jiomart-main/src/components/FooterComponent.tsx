import React from 'react';
import {JioIcon, JioText} from '@jio/rn_components';
import {
  IconKind,
  IconSize,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import {StyleSheet, View} from 'react-native';

interface FooterIcon {
  icon: string;
  text: string;
}

interface FooterComponentProps {
  icons: FooterIcon[];
  heading: string;
  subheading: string;
  infoBoxTitle: string;
  infoBoxText: string;
  quote: string;
  quoteAuthor: string;
}

const FooterComponent = ({
  icons,
  heading,
  subheading,
  infoBoxTitle,
  infoBoxText,
  quote,
  quoteAuthor,
}: FooterComponentProps) => {
  return (
    <View style={styles.container}>
      <JioText
        text={heading}
        appearance={JioTypography.HEADING_XXS}
        style={{fontWeight: 900}}
      />
      <JioText
        text={subheading}
        appearance={JioTypography.HEADING_M}
        style={{fontWeight: 900}}
      />
      <View style={styles.box}>
        <View style={styles.topBox}>
          {icons.map((item: any, index: number) => (
            <View
              key={index}
              style={{
                paddingHorizontal: 8,
                paddingVertical: 16,
                gap: 8,
                alignItems: 'center',
                width: '25%',
              }}>
              <JioIcon
                ic={item.icon}
                kind={IconKind.BACKGROUND}
                size={IconSize.LARGE}
              />
              <JioText
                text={item.text}
                appearance={JioTypography.BODY_XXS}
                style={{fontWeight: 500}}
                textAlign="center"
              />
            </View>
          ))}
        </View>
        <View style={styles.bottomBox}>
          <JioText
            text={infoBoxTitle}
            appearance={JioTypography.BODY_XXS_BOLD}
            textAlign="center"
            style={{fontWeight: 700}}
          />
          <JioText
            text={infoBoxText}
            appearance={JioTypography.BODY_XXS}
            style={{fontWeight: 500}}
            textAlign="center"
          />
        </View>
      </View>
      <JioText
        text={quote}
        appearance={JioTypography.BODY_XXS}
        style={{fontWeight: 500, marginTop: 18}}
        textAlign="center"
      />
      <JioText
        text={quoteAuthor}
        appearance={JioTypography.BODY_XXS_BOLD}
        textAlign="center"
        style={{fontWeight: 700}}
      />
    </View>
  );
};
export default FooterComponent;

const styles = StyleSheet.create({
  container: {
    padding: 16,
    gap: 12,
    marginBottom: 100,
  },
  box: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 16,
  },
  topBox: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  bottomBox: {
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 2,
    backgroundColor: '#E5F1F7',
    borderBottomStartRadius: 16,
    borderBottomEndRadius: 16,
  },
});
