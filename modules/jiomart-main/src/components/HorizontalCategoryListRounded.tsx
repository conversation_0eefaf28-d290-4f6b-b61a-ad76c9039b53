import {FlatList, StyleSheet, View, Pressable} from 'react-native';
import FastImage from 'react-native-fast-image';
import {getBaseURL} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import {rh, rw} from '../../.../../../jiomart-common/src/JMResponsive';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';

interface HorizontalCategoryListRoundedProps {
  title: string;
  images: string[];
  onImagePress?: (imageUrl: string, index: number) => void;
  imageHeight?: number;
  imageWidth?: number;
}

const renderItem = (
  {item, index}: {item: string; index: number},
  onPress?: (url: string, idx: number) => void,
  imageHeight?: number,
  imageWidth?: number,
) => {
  return (
    <Pressable onPress={() => onPress?.(item, index)}>
      <FastImage
        source={{
          // uri: `${getBaseURL()}/images/product/original/${item}`,
          uri: item,
        }}
        style={[styles.image, {height: imageHeight, width: imageWidth}]}
        resizeMode={FastImage.resizeMode.cover}
      />
    </Pressable>
  );
};

const HorizontalCategoryListRounded = (
  props: HorizontalCategoryListRoundedProps,
) => {
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <JioText
          text={props.title}
          color="black"
          appearance={JioTypography.HEADING_XXS}
          style={styles.title}
        />
      </View>
      <FlatList
        data={props.images}
        renderItem={item =>
          renderItem(
            item,
            props.onImagePress,
            props.imageHeight,
            props.imageWidth,
          )
        }
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.flatListContent}
      />
    </View>
  );
};
export default HorizontalCategoryListRounded;

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderColor: 'grey',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignContent: 'center',
  },
  title: {
    paddingLeft: 16,
    paddingBottom: 12,
    fontWeight: '900',
  },
  flatListContent: {
    paddingHorizontal: 16,
    gap: 12,
  },
  image: {
    borderRadius: 999,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
});
