import {<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ex<PERSON>} from '@jio/rn_components';
import WishlistToggleView from '../../../jiomart-general/src/ui/Wishlist/WishlistToggleView';
import {Image, Pressable, StyleSheet, Text, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import {rh, rw} from '../../.../../../jiomart-common/src/JMResponsive';
import {
  ButtonKind,
  ButtonSize,
  IconColor,
  JioColor,
  JioTypography,
  ShimmerKind,
} from '@jio/rn_components/src/index.types';
import JMTag from '../../../jiomart-general/src/ui/JMTag';
import {getBaseURL} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import AddCtaView from '../../../jiomart-general/src/ui/AddCta/AddCtaView';
import {isNullOrUndefinedOrEmpty} from '../../../jiomart-common/src/JMObjectUtility';
import {getProductImageUrl} from '../../../jiomart-home/src/utils/HomeSectionUtils';
import React, {useMemo} from 'react';
import {JMFoodType} from '../../../jiomart-home/src/types/JMHomeComponentType';
import {
  ActionType,
  NavigationType,
} from '../../../jiomart-common/src/JMNavGraphUtil';
import isEqual from 'lodash.isequal';

interface ItemCardProps {
  showQcLogo?: boolean;
  qcLogoUrl?: string;
  product: any;
  disableWishlist: boolean;
  discount?: string;
  discountBackgroundColor?: string;
  discountColor?: JioColor;
  disableAddToCart: boolean;
  addedToCart: boolean;
  padding?: number;
  onClick: any;
}
export const ShimmerCard = () => {
  return (
    <View style={styles.shimmerCard}>
      <JioShimmer
        width={160}
        height={120}
        kind={ShimmerKind.RECTANGLE}
        style={styles.shimmerImage}
      />
      <View style={styles.shimmerContent}>
        <JioShimmer
          width="90%"
          height={16}
          kind={ShimmerKind.PARAGRAPH}
          style={styles.shimmerTitle}
        />
        <JioShimmer
          width="60%"
          height={14}
          kind={ShimmerKind.PARAGRAPH}
          style={styles.shimmerPrice}
        />
        <JioShimmer
          width="40%"
          height={14}
          kind={ShimmerKind.PARAGRAPH}
          style={styles.shimmerDiscount}
        />
      </View>
    </View>
  );
};

const DEFAULT_IMAGE =
  'https://jiomart-sit.jio.com/assets/global/jiomart-default-image.png';

const ItemCard = ({
  showQcLogo = false,
  qcLogoUrl = '',
  product,
  disableWishlist,
  discount,
  discountBackgroundColor,
  discountColor,
  disableAddToCart,
  addedToCart,
  padding,
  onClick,
}: ItemCardProps) => {
  console.log('product---am---->', '');
  // State for image fallback
  const [imgSrc, setImgSrc] = React.useState(
    getProductImageUrl(product?.image_path),
  );
  React.useEffect(() => {
    setImgSrc(getProductImageUrl(product?.image_path));
  }, [product?.image_path]);
  return (
    <Pressable
      style={[styles.container, {padding: padding}]}
      onPress={() => {
        onClick({
          actionType: ActionType.OPEN_WEB_URL,
          destination: 'CommonWebViewScreen',
          headerVisibility: 2,
          navigationType: NavigationType.PUSH,
          loginRequired: false,
          actionUrl: `${getBaseURL()}${product?.url_path}`,
          headerType: 9,
          shouldShowDeliverToBar: true,
        });
      }}>
      <View style={styles.imageContainer}>
        {imgSrc === DEFAULT_IMAGE ? (
          <Image
            source={{uri: DEFAULT_IMAGE}}
            style={styles.image}
            resizeMode="contain"
          />
        ) : (
          <FastImage
            source={{uri: imgSrc || DEFAULT_IMAGE}}
            style={styles.image}
            resizeMode={FastImage.resizeMode.contain}
            onError={() => setImgSrc(DEFAULT_IMAGE)}
          />
        )}
        {!disableWishlist ? (
          <WishlistToggleView
            request={useMemo(
              () => ({uid: product?.product_code}),
              [product?.product_code],
            )}
            style={styles.wishlist}
          />
        ) : null}
        {product?.food_type === JMFoodType.VEG && (
          <JioIcon
            ic="IcVeg"
            color={IconColor.SUCCESS}
            style={{position: 'absolute', left: 4, bottom: 4}}
          />
        )}
        {product?.food_type === JMFoodType.NONVEG && (
          <JioIcon
            ic="IcNonVeg"
            color={IconColor.ERROR}
            style={{position: 'absolute', left: 4, bottom: 4}}
          />
        )}
      </View>
      <View style={styles.textContainer}>
        <JioText
          text={product?.display_name}
          style={styles.productName}
          maxLines={2}
          minLines={2}
          appearance={JioTypography.BODY_XXS}
        />
        <JioText
          text={`₹${product?.selling_price}`}
          appearance={JioTypography.BODY_S_BOLD}
        />
        <View style={{flexDirection: 'row', gap: 4, alignItems: 'center'}}>
          <JioText
            text={
              typeof product?.discount_pct === 'number' &&
              product?.discount_pct !== 0
                ? `₹${product?.mrp}`
                : ''
            }
            appearance={JioTypography.BODY_XXS}
            color="primary_grey_60"
            style={{textDecorationLine: 'line-through'}}
          />
          <JMTag
            color={
              typeof product?.discount_pct === 'number' &&
              product?.discount_pct !== 0
                ? (discountBackgroundColor as string)
                : 'white'
            }
            textColor={discountColor}
            text={
              typeof product?.discount_pct === 'number' &&
              product?.discount_pct !== 0
                ? `${Math.round(product?.discount_pct)}% OFF`
                : ' '
            }
          />
        </View>
      </View>
      {!disableAddToCart ? (
        <AddCtaView
          request={{
            uid: product?.product_code,
            // slug: slug,
            size: ``,
            meta: {vertical_code: product?.vertical_code},
            sellerId: product?.seller_id,
            minQty: product?.min_qty_in_order ?? 0,
            maxQty: product?.max_qty_in_order ?? 0,
          }}
          style={styles.addToCart}
          stretchButton={true}
          button={{style: {alignSelf: 'center'}}}
          styleForButtonIcon={{width: '100%'}}
        />
      ) : null}
      {showQcLogo && (
        <View style={styles.card}>
          <FastImage
            source={{
              uri: isNullOrUndefinedOrEmpty(qcLogoUrl)
                ? 'https://myjiostatic.cdn.jio.com/JioMart/Common/<EMAIL>'
                : qcLogoUrl,
            }}
            style={{width: '100%', height: '100%'}}
            resizeMode="contain"
          />
        </View>
      )}
    </Pressable>
  );
};

function areEqual(prevProps: ItemCardProps, nextProps: ItemCardProps) {
  return (
    prevProps.showQcLogo === nextProps.showQcLogo &&
    prevProps.qcLogoUrl === nextProps.qcLogoUrl &&
    isEqual(prevProps.product, nextProps.product) &&
    prevProps.disableWishlist === nextProps.disableWishlist &&
    prevProps.discount === nextProps.discount &&
    prevProps.discountBackgroundColor === nextProps.discountBackgroundColor &&
    prevProps.discountColor === nextProps.discountColor &&
    prevProps.disableAddToCart === nextProps.disableAddToCart &&
    prevProps.addedToCart === nextProps.addedToCart &&
    prevProps.padding === nextProps.padding &&
    prevProps.onClick === nextProps.onClick
  );
}

export default React.memo(ItemCard, areEqual);

const styles = StyleSheet.create({
  container: {
    width: rw(132),
    backgroundColor: 'white',
    borderRadius: 16,
    overflow: 'hidden',
  },
  wishlist: {
    position: 'absolute',
    right: 8,
    top: 8,
  },
  imageContainer: {
    width: '100%',
    height: rw(132),
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
    backgroundColor: '#F5F5F5',
  },
  textContainer: {
    gap: 4,
    paddingTop: 8,
  },
  productName: {
    height: 30, // This will limit the text to roughly 2 lines
  },
  addToCart: {
    marginTop: 8,
    // justifyContent: 'space-between',
  },
  shimmerCard: {
    width: rw(108),
    backgroundColor: 'white',
    borderRadius: 8,
    overflow: 'hidden',
    padding: 5,
  },
  shimmerImage: {
    width: '100%',
  },
  shimmerContent: {
    padding: 12,
    gap: 8,
  },
  shimmerTitle: {
    marginBottom: 4,
  },
  shimmerPrice: {
    marginBottom: 4,
  },
  shimmerDiscount: {
    marginBottom: 4,
  },
  card: {
    backgroundColor: '#E8F5E8', // Light green background
    borderRadius: 4,
    marginTop: 6,
    marginBottom: 0,
    alignItems: 'center',
    height: 24,
  },
  text: {},
});
