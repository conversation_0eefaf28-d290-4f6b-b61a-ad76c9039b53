{"name": "@jm/jiomart-main", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "ios:replica": "react-native run-ios --scheme 'replica'", "ios:prod": "react-native run-ios --scheme 'production'", "ios:sit": "react-native run-ios --scheme 'TestAppStaging'"}, "engines": {"node": ">=18"}, "description": "Library for Jio-Health Flow from DashboardUi branch", "main": "src/index", "author": "", "license": "UNLICENSED", "homepage": "http://#readme", "repository": {"type": "git", "url": ""}, "publishConfig": {"registry": ""}}