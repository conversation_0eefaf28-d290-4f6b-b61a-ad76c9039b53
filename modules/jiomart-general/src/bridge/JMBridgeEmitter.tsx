import React, {useEffect} from 'react';
import {NativeEventEmitter, NativeModules, Platform} from 'react-native';
import {JMListenerType, JMNativeDataHandler} from './JMBridgeConfig';
import {JMLogger} from '../../../jiomart-common/src/utils/JMLogger';
import {useGlobalState} from '../context/JMGlobalStateProvider';

const JMBridgeEmitterState = () => {
  const {JMEventEmitter} = NativeModules;
  const eventEmitter =
    Platform.OS === 'android'
      ? new NativeEventEmitter()
      : new NativeEventEmitter(JMEventEmitter);

  const {setDeeplinkData} = useGlobalState();
  useEffect(() => {
    if (eventEmitter) {
      eventEmitter.removeAllListeners(JMListenerType.ON_DATA_RECEIVED);
      const subscription = eventEmitter?.addListener(
        JMListenerType.ON_DATA_RECEIVED,
        handleNativeEventData,
      );
      return () => subscription?.remove();
    }
  }, []);

  function handleNativeEventData(data: any) {
    try {
      JMLogger.log('Received event data ', data);
      const parsedData =
        Platform.OS === 'android'
          ? JSON.parse(data['data']).nameValuePairs
          : JSON.parse(data);
      handleNativeDataBasedOnEnum(parsedData);
    } catch (error) {
      JMLogger.log('Error parsing native data:', error);
    }
  }

  function handleNativeDataBasedOnEnum(data: any) {
    const {param, enumValue} = data;
    if (enumValue) {
      switch (enumValue) {
        case JMNativeDataHandler.deeplink: {
          const paramObj = JSON.parse(param);
          const deeplinkData = {
            mUri: paramObj.mUri,
            payload: paramObj.payload,
          };
          if (paramObj.mUri || paramObj.payload) {
            setDeeplinkData(deeplinkData);
          }
          break;
        }

        default: {
          break;
        }
      }
    } else {
      JMLogger.log('Received event with missing or undefined enum value');
    }
  }

  return {};
};

export default JMBridgeEmitterState;
