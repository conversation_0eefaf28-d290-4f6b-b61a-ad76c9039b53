import {Platform} from 'react-native';
import {
  JioAdModule,
  JMGeneralPropertiesModule,
  JMNativeCacheModule,
  JMPaymentServiceProviderModule,
  JMRNAddressModule,
} from '.';
import {PlatformType} from '../../../jiomart-common/src/JMObjectUtility';
import type {JioADsData} from '../JioAds/types/JioAds';

export async function getReverseGeoCodeFromLatLongNB(
  latitude: number,
  longitude: number,
) {
  return await JMRNAddressModule.getReverseGeoCodeFromLatLong(
    latitude,
    longitude,
  );
}

export async function getDeviceInfo() {
  return await JMGeneralPropertiesModule.getDeviceInfo();
}

export async function changeWindowBackground() {
  if (Platform.OS === PlatformType.ANDROID)
    JMRNAddressModule.changeWindowBackground();
}

export async function setKeyboardResizeMode(flag: boolean) {
  return await JMGeneralPropertiesModule.setResizeMode(flag);
}

export async function decodeQRFromImage(imageUri: string) {
  return await JMGeneralPropertiesModule.decode(imageUri);
}

export async function checkAdStatusNB(params: JioADsData): Promise<boolean> {
  return await JioAdModule.checkAdStatus(params);
}
export async function getPspAppsList(params: any[]) {
  if (Platform.OS === 'ios') {
    return await JMPaymentServiceProviderModule.getPspAppList({
      upiIntentAppsArray: params,
    });
  } else {
    return await JMPaymentServiceProviderModule.getPspAppList();
  }
}

export async function openPspUpiApp(upiUrl: string) {
  return await JMPaymentServiceProviderModule.openPspUpiApp(upiUrl);
}

export class SharedPreferencesManager {
  static async getString(key: string, defaultValue = null) {
    try {
      if (Platform.OS === PlatformType.ANDROID) {
        return await JMNativeCacheModule.getString(key, defaultValue);
      } else {
        return await JMNativeCacheModule.getUserDefaultItem(key);
      }
    } catch (error) {
      console.error('Error getting string:', error);
      throw error;
    }
  }

  static async getDecryptedString(key: string, defaultValue = null) {
    try {
      if (Platform.OS === PlatformType.ANDROID) {
        return await JMNativeCacheModule.getDecryptedString(key, defaultValue);
      } else {
        return await JMNativeCacheModule.getCoreDataItem(key);
      }
    } catch (error) {
      console.error('Error getting string:', error);
      throw error;
    }
  }

  static async getBoolean(key: string, defaultValue = false) {
    try {
      if (Platform.OS === PlatformType.ANDROID) {
        return await JMNativeCacheModule.getBoolean(key, defaultValue);
      } else {
        return await JMNativeCacheModule.getUserDefaultItem(key);
      }
    } catch (error) {
      console.error('Error getting boolean:', error);
      throw error;
    }
  }

  static async clearAll() {
    try {
      if (Platform.OS === PlatformType.ANDROID) {
        return await JMNativeCacheModule.clearAll();
      } else {
        await JMNativeCacheModule.clearAllCoreData();
        return await JMNativeCacheModule.clearAllUserDefaultItem();
      }
    } catch (error) {
      console.error('Error clearing all:', error);
      throw error;
    }
  }
}

export default SharedPreferencesManager;
