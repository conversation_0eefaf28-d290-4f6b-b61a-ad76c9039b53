import {useCallback, useMemo, useRef, useState} from 'react';
import {AdEventName, type useJioAdsProps} from '../types/JioAds';

const useJioAds = (props: useJioAdsProps) => {
  const {
    onAdStatus,
    adMetaData,
    adHeight,
    adSpotId,
    adType,
    adWidth,
  } = props;

  const [adSuccess, setAdSuccess] = useState<boolean | null>(null);
  const adMetaKeyRef = useRef<string>('');
  const adMetaValueRef = useRef<string>('');

  if (adMetaData?.l1) {
    adMetaKeyRef.current = 'L1';
    adMetaValueRef.current = adMetaData?.l1;
  }
  if (adMetaData?.l2) {
    adMetaKeyRef.current = 'L2';
    adMetaValueRef.current = adMetaData?.l2;
  }
  if (adMetaData?.l3) {
    adMetaKeyRef.current = 'L3';
    adMetaValueRef.current = adMetaData?.l3;
  }
  if (adMetaData?.search_query) {
    adMetaKeyRef.current = 'search_keyword';
    adMetaValueRef.current = adMetaData?.search_query;
  }

  const adMetaDataVal = useMemo(() => {
    return {
      key: 'mobileweb',
      jm_meta: {
        [adMetaKeyRef.current]: adMetaValueRef.current,
      },
      [adMetaKeyRef.current]: adMetaValueRef.current,
    };
  }, [adMetaKeyRef, adMetaValueRef]);

  const handleAdEvent = useCallback(
    e => {
      console.log(
        'jio ads',
        'adSpotId ',
        adSpotId,
        'adType',
        adType,
        'adWidth',
        adWidth,
        'adHeight',
        adHeight,
        'event',
        e.nativeEvent.event,
      );
      switch (e.nativeEvent.event) {
        case AdEventName.adFailed:
          setAdSuccess(false);
          onAdStatus?.(false);
          break;
        case AdEventName.adPrepared:
          setAdSuccess(true);
          onAdStatus?.(true);
      }
    },
    [onAdStatus],
  );

  return {adSuccess, adMetaDataVal, handleAdEvent};
};

export default useJioAds;
