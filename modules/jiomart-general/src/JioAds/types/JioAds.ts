import type {StyleProp, ViewStyle} from 'react-native';

export interface JioAdsProps {
  adHeight: number;
  adWidth: number;
  adSpotId: string;
  adType: number;
  adjHeight: number;
  adjWidth?: number;
  adMetaData?: AdMetaData;
  style?: StyleProp<ViewStyle>;
  nativeStyle?: StyleProp<ViewStyle>;
  onAdStatus?: (res: boolean) => void;
  FooterComponent?: React.ReactNode;
  HeaderComponent?: React.ReactNode;
}

export const AdTypes = {
  dynamicDisplay: {id: 1, name: 'dynamicDisplay'},
  instreamVideo: {id: 2, name: 'instreamVideo'},
  interstitial: {id: 3, name: 'interstitial'},
  nativeContentStream: {id: 4, name: 'nativeContentStream'},
  customNative: {id: 5, name: 'custom'},
} as const;

export interface JioADsData {
  adType: number;
  adspotKey: string;
  adHeight: number;
  adWidth: number;
  adCustomWidth: number;
  adCustomHeight: number;
  adMetaData?: ProcessedAdMetaData;
}

// Native component props for iOS
export interface JioADsNativeComponentIOSProps {
  style?: StyleProp<ViewStyle>;
  data: JioADsData;
  onChange: (event: any) => void;
}

// Native component props for Android
export interface JioADsNativeComponentAndroidProps {
  style?: StyleProp<ViewStyle>;
  data: JioADsData;
  onChange: (event: any) => void;
}

export interface useJioAdsProps extends JioAdsProps {}

export enum AdEventName {
  adRecived = 'adRecived',
  adPrepared = 'adPrepared',
  adRender = 'adRender',
  adClicked = 'adClicked',
  adRefresh = 'adRefresh',
  adFailed = 'adFailed',
  adMediaEnd = 'adMediaEnd',
  adClosed = 'adClosed',
  adMediaStart = 'adMediaStart',
  adSkippable = 'adSkippable',
  adMediaExpand = 'adMediaExpand',
  adMediaCollapse = 'adMediaCollapse',
  adMediaPlaybackChange = 'adMediaPlaybackChange',
  adDataPrepared = 'adDataPrepared',
  adChange = 'adChange',
  mediationRequesting = 'mediationRequesting',
  mediationAd = 'mediationAd',
}

export interface AdMetaData {
  l1: string;
  l2: string;
  l3: string;
  search_query: string;
}

// Processed ad metadata structure used by the native component
export interface ProcessedAdMetaData {
  key: string;
  jm_meta: {
    [key: string]: string;
  };
  [key: string]: string | {[key: string]: string};
}
