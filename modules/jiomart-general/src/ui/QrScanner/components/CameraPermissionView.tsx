import {Pressable, StyleSheet, View} from 'react-native';
import {Jio<PERSON>utton, JioIcon, JioText} from '@jio/rn_components';
import {
  ButtonKind,
  ButtonSize,
  ButtonState,
  IconColor,
  IconSize,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import CustomMediaRendered from '../../CustomMediaRendered';
import {rw} from '../../../../../jiomart-common/src/JMResponsive';

interface CameraPermissionViewProps {
  onGrantPermission: () => void;
  qrScannerConfig: any;
  onClose?: () => void;
}

const CameraPermissionView = ({
  onGrantPermission,
  qrScannerConfig,
  onClose,
}: CameraPermissionViewProps) => {
  const insets = useSafeAreaInsets();

  return (
    <View style={styles.container}>
      <View
        style={[styles.closeContainer, {top: insets.top, right: insets.right}]}>
        <Pressable onPress={onClose} style={styles.closeText} hitSlop={12}>
          <JioIcon
            ic={qrScannerConfig?.closeIcon || 'IcClose'}
            color={IconColor.PRIMARY60}
            size={IconSize.MEDIUM}
          />
        </Pressable>
      </View>
      <CustomMediaRendered
        height={rw(120)}
        width={rw(120)}
        mediaUrl={qrScannerConfig?.cameraAccesUrl}
      />
      <JioText
        style={styles.title}
        textAlign="center"
        color="primary_grey_100"
        appearance={JioTypography.BODY_M}
        text={qrScannerConfig?.title}
      />
      <JioText
        style={styles.description}
        textAlign="center"
        color="primary_grey_80"
        appearance={JioTypography.BODY_XS}
        text={qrScannerConfig?.subTitle}
      />
      <JioButton
        style={styles.button}
        title={qrScannerConfig?.buttonText}
        size={ButtonSize.MEDIUM}
        kind={ButtonKind.PRIMARY}
        stretch
        state={ButtonState.NORMAL}
        onClick={onGrantPermission}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    backgroundColor: 'white',
  },
  closeContainer: {
    position: 'absolute',
    top: 0,
    right: 0,
    margin: 16,
    zIndex: 1,
  },
  title: {
    marginTop: 60,
  },
  description: {
    marginTop: 25,
  },
  button: {
    marginTop: 40,
  },
  closeText: {
    marginLeft: 'auto',
  },
});

export default CameraPermissionView;
