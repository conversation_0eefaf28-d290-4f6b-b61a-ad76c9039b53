import { Dimensions, StyleSheet, View } from 'react-native';

const { width, height } = Dimensions.get('window');
const SCAN_BOX_SIZE = width * 0.7;

const CameraOverlay = () => (
    <View style={styles.cameraOverlayContainer}>
        <View style={[styles.overlayBlock, { height: (height - SCAN_BOX_SIZE) / 2 }]} />
        <View style={{ flexDirection: 'row' }}>
            <View style={[styles.overlayBlock, { width: (width - SCAN_BOX_SIZE) / 2 }]} />
            <View style={styles.scanBox}>
                <View style={[styles.corner, styles.topLeft]} />
                <View style={[styles.corner, styles.topRight]} />
                <View style={[styles.corner, styles.bottomLeft]} />
                <View style={[styles.corner, styles.bottomRight]} />
            </View>
            <View style={[styles.overlayBlock, { width: (width - SCAN_BOX_SIZE) / 2 }]} />
        </View>
        <View style={[styles.overlayBlock, { height: (height - SCAN_BOX_SIZE) / 2 }]} />
    </View>
);

const styles = StyleSheet.create({
    cameraOverlayContainer: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: 'transparent',
    },
    overlayBlock: {
        backgroundColor: 'rgba(0, 0, 0, 0.6)',
        width: '100%',
    },
    scanBox: {
        width: SCAN_BOX_SIZE,
        height: SCAN_BOX_SIZE,
        backgroundColor: 'transparent',
    },
    corner: {
        position: 'absolute',
        width: 30,
        height: 30,
        borderColor: '#00AFFF',
    },
    topLeft: {
        top: 0,
        left: 0,
        borderLeftWidth: 4,
        borderTopWidth: 4,
    },
    topRight: {
        top: 0,
        right: 0,
        borderRightWidth: 4,
        borderTopWidth: 4,
    },
    bottomLeft: {
        bottom: 0,
        left: 0,
        borderLeftWidth: 4,
        borderBottomWidth: 4,
    },
    bottomRight: {
        bottom: 0,
        right: 0,
        borderRightWidth: 4,
        borderBottomWidth: 4,
    },
});

export default CameraOverlay;