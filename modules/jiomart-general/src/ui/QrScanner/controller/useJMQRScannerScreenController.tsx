import { PermissionsAndroid, Platform } from 'react-native';
import { launchImageLibrary } from 'react-native-image-picker';
import { addStringPref, getPrefString, removeStringPref } from '../../../../../jiomart-common/src/JMAsyncStorageHelper';
import { AsyncStorageKeys } from '../../../../../jiomart-common/src/JMConstants';
import { JMSharedViewModel } from '../../../../../jiomart-common/src/JMSharedViewModel';
import { JMConfigFileName } from '../../../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import JMUserApiNetworkController from '../../../../../jiomart-networkmanager/src/JMNetworkController/JMUserAPINetworkController';
import { decodeQRFromImage } from '../../../bridge/JMRNBridge';
import { useConfigFile } from '../../../hooks/useJMConfig';
import { JMQRScannerScreenProps } from '../types/JMQRScannerScreenType';

const useJMQRScannerScreenController = (
  props: JMQRScannerScreenProps & {
    handleScannedResult?: (result: string) => void;
  },
) => {
  const commanConfigData = useConfigFile(
    JMConfigFileName.JMSearchConfigurationFileName,
  );
  const qrScannerConfig = commanConfigData?.qrScannerConfig[0];
  console.log('qrScannerConfig', qrScannerConfig);
  const userApiNetworkController = new JMUserApiNetworkController();
  const requestGalleryPermission = async () => {
    if (Platform.OS === 'android') {
      const sdkVersion = Platform.constants?.Release || '12';

      // Use correct permission based on Android SDK version
      const permission =
        parseInt(sdkVersion) >= 13
          ? PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES
          : PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE;
      console.log('🚀 ~ requestGalleryPermission ~ permission:', permission);
      console.log('🚀 ~ requestGalleryPermission ~ sdkVersion:', sdkVersion);

      const granted = await PermissionsAndroid.request(permission, {
        title: 'Gallery Permission',
        message: 'App needs access to your photos to scan QR codes',
        buttonPositive: 'OK',
      });

      return granted === PermissionsAndroid.RESULTS.GRANTED;
    }
    return true;
  };

  const onPickImage = async () => {
    const permissionGranted = await requestGalleryPermission();
    if (!permissionGranted) {
      console.warn('Gallery permission not granted');
      return;
    }

    launchImageLibrary(
      {
        mediaType: 'photo',
        selectionLimit: 1,
      },
      async response => {
        if (response.didCancel || !response.assets?.[0]?.uri) {
          console.log('Image selection canceled or failed');
          return;
        }

        const imageUri = response.assets[0].uri;
        console.log('🚀 ~ launchImageLibrary ~ imageUri:', imageUri);

        try {
          const qrResult = await decodeQRFromImage(imageUri);
          console.log('✅ QR Code:', qrResult);
          if (props.handleScannedResult) {
            props.handleScannedResult(qrResult);
          }
        } catch (error) {
          console.log('QR decode failed', error);
        }
      },
    );
  };

  const storeIncentiveProcess = async (url: string) => {

    const storeIncentiveFlag = 1;//JioMartFlags.getIntegerByKey('storeIncentiveFlag');
    const multipleStoreIncentiveScanFlag = 1;//JioMartFlags.getIntegerByKey("multipleStoreIncentiveScanFlag")
    const key = 'store_id';//JioMartFlags.getStringByKey(MyJioConstants.STORE_INCENTIVE_CHECK_KEY);

    if (storeIncentiveFlag === 1) {
      const lowerUrl = url.toLowerCase();
      if (
        url.includes(`${key}=`) &&
        (lowerUrl.startsWith('https://') || lowerUrl.startsWith('http://'))
      ) {
        const queryParams = lowerUrl.split('&');

        for (const param of queryParams) {
          if (param.includes(`${key}=`)) {

            const storeId = param.replace(`${key}=`, '');
            const scanDate = new Date().toISOString();

            await addStringPref(AsyncStorageKeys.STORE_INCENTIVE_STORE_ID_DATA, storeId);
            await addStringPref(AsyncStorageKeys.STORE_INCENTIVE_SCAN_DATE, scanDate);

            const loggedIn = JMSharedViewModel.Instance.getLoggedInStatus();

            if (loggedIn) {
              const rrid = await getPrefString(AsyncStorageKeys.JIOMART_RRID, false);

              if (!rrid) {
                const sessionId = ''//decrypt(await AsyncStorage.getItem(MyJioConstants.JIOMART_USER_SESSION_ID) || '');
                const customerId = ''//decrypt(await AsyncStorage.getItem(MyJioConstants.JIOMART_CUSTOMER_ID) || '');

                const rridResponse = await userApiNetworkController.getRRID(sessionId, customerId);

                if (
                  rridResponse &&
                  rridResponse.status === 'SUCCESS' &&
                  rridResponse.result &&
                  rridResponse.result.h_rrid
                ) {
                  const rrid = rridResponse.result.h_rrid;

                  // Save RRID to AsyncStorage (or your app-specific storage wrapper)
                  await addStringPref(AsyncStorageKeys.JIOMART_RRID, rrid);

                  sendStoreScanGAEvent(rrid)
                }

              } else {
                if (multipleStoreIncentiveScanFlag === 1)
                  sendStoreScanGAEvent(rrid)
              }

            }
            break

          }
        }

      }
    }

  }

  const sendStoreScanGAEvent = async (rrid: string) => {
    // Set Firebase user ID
    // await analytics().setUserId(rrid);
    const storeId = await getPrefString(AsyncStorageKeys.STORE_INCENTIVE_STORE_ID_DATA, false);
    const referredDate = await getPrefString(AsyncStorageKeys.STORE_INCENTIVE_SCAN_DATE, false);
    const currentDate = new Date().toISOString();

    const extraEvents = {
      user_id: rrid,
      store_id: storeId,
      referred_date: referredDate,
      event_triggered_date: currentDate,
    };

    if (storeId) {
      // TODO: Trigger GA event (replace event name and parameters as per your tracking setup)
      // await analytics().logEvent('qr_scan_in_store', extraEvents);

      // Clear stored values
      removeStringPref(AsyncStorageKeys.STORE_INCENTIVE_STORE_ID_DATA);
      removeStringPref(AsyncStorageKeys.STORE_INCENTIVE_SCAN_DATE);
    }
  }

  return {
    ...props,
    onPickImage,
    qrScannerConfig,
    storeIncentiveProcess,
  };
};

export default useJMQRScannerScreenController;
