import React from 'react';
import {GestureDetector} from 'react-native-gesture-handler';
import useMicroTap from './hook/useMicroTap';
import Animated from 'react-native-reanimated';

interface MicroTapProps {
  children: React.ReactNode;
  color: string;
  borderRadius: number;
  padding?: number;
  paddingLeft?: number;
  paddingRight?: number;
  paddingTop?: number;
  paddingBottom?: number;
  paddingVertical?: number;
  paddingHorizontal?: number;
}

const MicroTap = ({
  children,
  color,
  borderRadius,
  padding,
  paddingBottom,
  paddingHorizontal,
  paddingLeft,
  paddingRight,
  paddingTop,
  paddingVertical,
}: MicroTapProps) => {
  if (color === null || color === undefined) {
    throw Error('invalid value for color it must be number');
  }
  const {animatedStyles, tap} = useMicroTap(color);
  return (
    <GestureDetector gesture={tap}>
      <Animated.View
        style={[
          {
            borderRadius,
            padding,
            paddingBottom,
            paddingHorizontal,
            paddingLeft,
            paddingRight,
            paddingTop,
            paddingVertical,
          },
          animatedStyles,
        ]}>
        {children}
      </Animated.View>
    </GestureDetector>
  );
};

export default MicroTap;
