import {Gesture} from 'react-native-gesture-handler';
import {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';

const useMicroTap = (backgroundColor: string) => {
  const pressed = useSharedValue(false);

  const tap = Gesture.Tap()
    .onBegin(() => {
      pressed.value = true;
    })
    .onFinalize(() => {
      pressed.value = false;
    });

  const animatedStyles = useAnimatedStyle(() => {
    return {
      backgroundColor: withTiming(
        pressed.value ? backgroundColor : 'transparent',
      ),
    };
  });

  return {tap, animatedStyles};
};

export default useMicroTap;
