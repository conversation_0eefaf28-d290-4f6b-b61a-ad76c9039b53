import {StyleSheet, View} from 'react-native';
import React from 'react';
import {<PERSON><PERSON><PERSON>utton, JioText} from '@jio/rn_components';
import {
  ButtonSize,
  JioTypography,
  type JioButtonProps,
  type JioTextProps,
} from '@jio/rn_components/src/index.types';
import {rh, rw} from '../../../jiomart-common/src/JMResponsive';
import CustomMediaRendered, {
  type CustomMediaRenderedProps,
} from './CustomMediaRendered';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

export interface NegativeScreenUIProps {
  image?: CustomMediaRenderedProps;
  title?: JioTextProps;
  subTitle?: JioTextProps;
  isButtonVisible?: boolean;
  shouldShowContentInCenter?: boolean;
  button?: JioButtonProps;
  onPress?: () => void;
  offset?: number;
}

const NegativeScreenUI = (props: NegativeScreenUIProps) => {
  const {
    image,
    title,
    subTitle,
    isButtonVisible,
    shouldShowContentInCenter = true,
    offset,
    button,
    onPress,
  } = props;
  const insets = useSafeAreaInsets();
  return (
    <View style={styles.container}>
      <View style={[shouldShowContentInCenter ? styles.center : styles.start]}>
        <View style={styles.content}>
          {image?.mediaUrl ? (
            <CustomMediaRendered
              customStyles={styles.image}
              width={rw(image?.width ?? 164)}
              height={rw(image?.height ?? 164)}
              {...image}
            />
          ) : null}

          {title ? (
            <JioText
              appearance={JioTypography.HEADING_XXS}
              color={'primary_grey_100'}
              maxLines={1}
              textAlign="center"
              {...title}
              style={styles.marginBottom12}
            />
          ) : null}
          {subTitle ? (
            <JioText
              appearance={JioTypography.BODY_XXS}
              color={'primary_grey_80'}
              textAlign="center"
              {...subTitle}
            />
          ) : null}
        </View>
      </View>
      {isButtonVisible ? (
        <View
          style={[
            styles.bottom,
            {marginBottom: offset && offset > 0 ? offset : insets.bottom},
          ]}>
          <JioButton
            title=""
            size={ButtonSize.LARGE}
            stretch
            onClick={onPress}
            {...button}
          />
        </View>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {backgroundColor: '#ffffff', flex: 1},
  content: {
    marginHorizontal: rw(24),
    alignItems: 'center',
  },
  start: {marginTop: rh(34)},
  center: {justifyContent: 'center', flex: 1},
  image: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  bottom: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    marginTop: 'auto',
  },
  marginBottom12: {
    marginBottom: 12,
  },
});

export default NegativeScreenUI;
