import {StyleSheet, View, type StyleProp, type ViewStyle} from 'react-native';
import React from 'react';
import {JioIcon, JioText} from '@jio/rn_components';
import {
  JioTypography,
  type JioIconProps,
  type JioTextProps,
} from '@jio/rn_components/src/index.types';
import {Pressable} from 'react-native-gesture-handler';

interface JMSearchSuggestionItemProps {
  style?: StyleProp<ViewStyle>;
  onPress?: () => void;
  icon?: JioIconProps;
  title?: JioTextProps;
  subTitle?: JioTextProps;
}

const JMSearchSuggestionItem = (props: JMSearchSuggestionItemProps) => {
  const {onPress, icon, title, subTitle, style} = props;
  return (
    <Pressable style={style} onPress={onPress}>
      <View style={styles.searchSuggestion}>
        {icon ? <JioIcon {...icon} /> : null}
        <View style={{rowGap: 4, flexShrink: 1}}>
          <JioText
            color={'primary_grey_100'}
            appearance={JioTypography.BODY_S}
            {...title}
          />
          <JioText
            color={'primary_grey_80'}
            appearance={JioTypography.BODY_XXS}
            style={{flexWrap: 'wrap'}}
            {...subTitle}
          />
        </View>
      </View>
    </Pressable>
  );
};

export default JMSearchSuggestionItem;

const styles = StyleSheet.create({
  searchSuggestion: {
    flexDirection: 'row',
    alignItems: 'center',
    columnGap: 8,
    flexShrink: 1,
  },
});
