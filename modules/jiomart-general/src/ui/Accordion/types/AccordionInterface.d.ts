import {StyleProp, ViewStyle} from 'react-native';

export interface AccordionProps {
  iconType?: 'arrow' | 'plus';
  allowMultiple?: boolean;
  children: React.ReactNode;
  onChange?: (expandedAccordionPanel: number) => void;
  ItemSeparatorComponent?: React.ReactNode;
  style?: StyleProp<ViewStyle>;
}

export interface AccordionPanelProps {
  accordionHeader?: React.ReactNode;
  children: React.ReactNode;
  open?: boolean;
  disable?: boolean;
  onPress?: () => void;
  style?: StyleProp<ViewStyle>;
}
