import {
  StyleSheet,
  View,
  ViewStyle,
  type StyleProp,
  type TextStyle,
} from 'react-native';
import React, {FC} from 'react';
import {JioText} from '@jio/rn_components';
import {
  JioTypography,
  type ColorToken,
} from '@jio/rn_components/src/index.types';
import FastImage from 'react-native-fast-image';
import { rw } from '../../../jiomart-common/src/JMResponsive';

interface QCTagProps {
  icon: React.JSX.Element;
  image: string;
  color: string;
  textColor: string;
  primaryText: string;
  secondaryText: string;
  style?: StyleProp<ViewStyle>;
  textstyle?: StyleProp<TextStyle>;
  maxLine?: number;
  minLines?: number;
  iconWidth?: number;
  iconHeight?: number;
}

const QCTag: FC<QCTagProps> = ({
  color,
  textColor = 'sparkle_70',
  primaryText,
  secondaryText,
  style,
  icon,
  image,
  textstyle,
  iconHeight = 10,
  iconWidth = 40,
}) => {
  return (
    <View style={[styles.tagContainer, style, {backgroundColor: color}]}>
      <View style={{flexDirection: 'row', alignItems: 'center'}}>
        {/* {React.isValidElement(icon) && React.cloneElement(icon)} */}
        <FastImage
          source={{
            uri: image,
          }}
          style={{
            height: rw(iconHeight),
            width: rw(iconWidth),
          }}
        />
        <JioText
          appearance={JioTypography.BODY_XXS_BOLD}
          color={textColor as keyof ColorToken}
          text={primaryText}
          style={[textstyle, {marginLeft: 1}]}
        />
      </View>
      <JioText
        appearance={JioTypography.BODY_XXS_BOLD}
        color={textColor as keyof ColorToken}
        text={secondaryText}
        style={textstyle}
      />
    </View>
  );
};

export default QCTag;

const styles = StyleSheet.create({
  tagContainer: {
    backgroundColor: '#E5F7EE',
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
    columnGap: 4,
    alignItems: 'center',
    flex: 1,
  },
});
