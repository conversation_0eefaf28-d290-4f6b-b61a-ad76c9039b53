import {
  StyleSheet,
  TouchableWithoutFeedback,
  Platform,
  StatusBar,
} from 'react-native';
import React, {useEffect, useRef} from 'react';
import Animated, {
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import {getScreenDim, rh, rw} from '../../../jiomart-common/src/JMResponsive';
import CustomMediaRendered, {
  type CustomMediaRenderedProps,
} from '../../../jiomart-general/src/ui/CustomMediaRendered';

export type JMLoaderProps = {
  visible?: boolean;
  imageWidth?: number;
  imageHeight?: number;
  media?: Omit<CustomMediaRenderedProps, 'width' | 'height'>;
  animationDuration?: number;
};

const JMLoader = (props: JMLoaderProps) => {
  const {
    visible = false,
    imageWidth = 80,
    imageHeight = 80,
    animationDuration = 500,
    media,
  } = props;

  const statusBarRef = useRef(null);

  const fadeValue = useSharedValue(0);

  const backDropStyle = useAnimatedStyle(() => ({
    opacity: interpolate(fadeValue.value, [0, 1], [0, 1]),
  }));

  const open = () => {
    'worklet';
    fadeValue.value = withTiming(1, {duration: animationDuration});
  };

  useEffect(() => {
    if (visible && media?.mediaUrl) {
      open();
    }
    return () => {
      statusBarRef.current?.setBackgroundColor?.('rgba(0, 120, 173, 1)', false);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible, media?.mediaUrl]);

  const styles = StyleSheet.create({
    modalBackground: {
      ...StyleSheet.absoluteFillObject,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      zIndex: 100000000,
    },
    position: {
      position: 'absolute',
    },
    animatedImageContainer: {
      position: 'absolute',
      zIndex: 100000002,
      top: getScreenDim?.height / 2 - rw(imageWidth) / 2,
      left: getScreenDim?.width / 2 - rh(imageHeight) / 2,
      alignItems: 'center',
      justifyContent: 'flex-end',
      backgroundColor: '#ffffff',
      borderRadius: 24,
      padding: 12,
    },
    animatedImage: {
      width: rw(imageWidth),
      height: rh(imageHeight),
    },
  });

  if (!(visible && media?.mediaUrl)) {
    return null;
  }

  return (
    <>
      {Platform.OS === 'android' ? (
        <StatusBar
          ref={statusBarRef}
          barStyle="light-content"
          backgroundColor="rgba(0,0,0,0.95)"
        />
      ) : null}
      <Animated.View style={[styles.modalBackground, backDropStyle]} />
      <TouchableWithoutFeedback style={styles.position}>
        <>
          {media?.mediaUrl && (
            <Animated.View style={[styles.animatedImageContainer]}>
              <CustomMediaRendered
                width={rw(imageWidth)}
                height={rh(imageHeight)}
                {...media}
                customStyles={styles.animatedImage}
              />
            </Animated.View>
          )}
        </>
      </TouchableWithoutFeedback>
    </>
  );
};

export default JMLoader;
