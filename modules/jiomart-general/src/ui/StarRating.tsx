import {
  StyleSheet,
  Pressable,
  View,
  type StyleProp,
  type ViewStyle,
} from 'react-native';
import React from 'react';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import StarIcon from './StarIcon';
import {rw} from '../../../jiomart-common/src/JMResponsive';
import { calculateRatingPercentage } from '../../../jiomart-common/src/utils/JMCommonFunctions';

export interface StarRatingProps {
  rate: number;
  noOfCustomer?: string;
  onPress?: () => void;
  style?: StyleProp<ViewStyle>;
  size?: number;
}

const ratingRange = [1, 2, 3, 4, 5];

const StarRating = (props: StarRatingProps) => {
  const {rate, noOfCustomer, size = 12, onPress, style} = props;

  return (
    <View style={styles.container}>
      <Pressable
        style={[styles.starWrapper, style]}
        onPress={onPress}
        disabled={!onPress}
        hitSlop={8}>
        {ratingRange.map((item, i) => {
          return (
            <StarIcon
              key={`star-${i}`}
              size={rw(size)}
              offset={calculateRatingPercentage(item, rate)}
            />
          );
        })}
      </Pressable>
      {noOfCustomer && (
        <JioText
          text={noOfCustomer}
          appearance={JioTypography.BODY_XXS_LINK}
          color={'primary_60'}
        />
      )}
    </View>
  );
};

export default StarRating;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    columnGap: 4,
    alignItems: 'center',
  },
  starWrapper: {flexDirection: 'row', columnGap: 2},
});
