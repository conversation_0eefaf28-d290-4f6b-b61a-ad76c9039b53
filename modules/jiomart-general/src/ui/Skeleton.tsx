import React, {useEffect, useCallback} from 'react';
import {
  View,
  Animated,
  StyleSheet,
  StyleProp,
  ViewStyle,
  DimensionValue,
} from 'react-native';
import {LinearGradient} from 'react-native-linear-gradient';
import {useTheme} from '@jio/rn_components/src/theme/theme';

export interface ShimmerProps {
  width: DimensionValue | number | string;
  height: DimensionValue | number | string;
  style?: StyleProp<ViewStyle>;
  animationDuration?: number;
  backgroundColor?: string;
  gradientColors?: string[];
}

const Skeleton: React.FC<ShimmerProps> = React.memo(
  ({
    width,
    height,
    style,
    animationDuration = 1000,
    backgroundColor,
    gradientColors,
  }) => {
    const theme = useTheme();
    const translateX = React.useRef(new Animated.Value(-350)).current;

    const startAnimation = useCallback(() => {
      Animated.loop(
        Animated.timing(translateX, {
          toValue: 350,
          useNativeDriver: true,
          duration: animationDuration,
        }),
      ).start();
    }, [translateX, animationDuration]);

    useEffect(() => {
      startAnimation();
      return () => {
        translateX.stopAnimation();
      };
    }, [startAnimation]);

    const colors = gradientColors ?? [
      theme.primary_grey_20,
      theme.primary_grey_40,
      theme.primary_grey_20,
    ];

    const containerStyle: ViewStyle = {
      overflow: 'hidden' as const,
      width,
      height,
      backgroundColor: backgroundColor ?? theme.primary_grey_20,
    };

    return (
      <View style={[containerStyle, style]}>
        <Animated.View style={[styles.gradient, {transform: [{translateX}]}]}>
          <LinearGradient
            style={styles.gradientContent}
            colors={colors}
            start={{x: 0.2, y: 0.2}}
            end={{x: 0.8, y: 0.8}}
            locations={[0.2, 0.5, 0.8]}
          />
        </Animated.View>
      </View>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
  gradient: {
    flex: 1,
  },
  gradientContent: {
    width: 350,
    height: '100%',
    opacity: 0.45,
  },
});

export default Skeleton;
