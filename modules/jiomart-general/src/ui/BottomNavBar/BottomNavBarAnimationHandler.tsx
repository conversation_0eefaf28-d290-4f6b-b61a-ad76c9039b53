import { useRef } from 'react';
import { useDispatch } from 'react-redux';
import { updateScrollPosition } from '../../../../jiomart-webmanager/src/webSlice';

const BottomNavBarAnimationHandler = () => {
  const dispatch = useDispatch();
  const lastScrollOffset = useRef(0);

  const handleBottomNavBarAnimation = (e: any) => {
    const currentOffset = e.nativeEvent.contentOffset.y;
    const scrollDirection = currentOffset > lastScrollOffset.current ? 1 : 0;

    if (currentOffset <= 0) {
      dispatch(updateScrollPosition(0));
      lastScrollOffset.current = currentOffset;
      return;
    }

    // Only update if the difference is significant enough to avoid jitter
    if (Math.abs(currentOffset - lastScrollOffset.current) > 2) {
      dispatch(updateScrollPosition(scrollDirection));
      lastScrollOffset.current = currentOffset;
    }
  };

  return {
    handleBottomNavBarAnimation
  }
};

export default BottomNavBarAnimationHandler;
