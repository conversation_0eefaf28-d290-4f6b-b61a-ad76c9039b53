import React from 'react';
import {JioIcon, JioText} from '@jio/rn_components';
import {
  IconColor,
  IconKind,
  IconSize,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import {useNavigation, useNavigationState} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useEffect, useRef, useState} from 'react';
import {Animated, Pressable, View} from 'react-native';
import {Easing} from 'react-native-reanimated';
import {useSelector} from 'react-redux';
import {navigateTo} from '../../navigation/JMNavGraph';
import BottomTabShimmer from './BottomTabShimmer';
import bottomTabRoute from './routes/bottomTabRoute';
import {styles} from './styles/CustomBottomNavBarStyle';
import {
  navBeanObj,
  NavigationType,
} from '../../../../jiomart-common/src/JMNavGraphUtil';
import {JMConfigFileName} from '../../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {getBaseURL} from '../../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import { useConfigFile } from '../../hooks/useJMConfig';

interface CustomBottomNavBarProps {
  loader?: boolean;
}

const CustomBottomNavBar = ({loader = false}: CustomBottomNavBarProps) => {
  // const [bottomNavBarConfig, setBottomNavBarConfig] = useState<any>();

  let currentRoute = bottomTabRoute.Home;
  useNavigationState(state => {
    currentRoute = state?.routes[state.index]?.params?.type;
  });

  const navigation = useNavigation<NativeStackNavigationProp<any>>();
  const bottomNavBarConfig = useConfigFile(JMConfigFileName.JMBottomNavBarConfigurationFileName);

  const handleTabPress = async (screen: any) => {
    if (currentRoute === screen.screenName) {
      return;
    }

    currentRoute = screen.screenName;

    if (screen.screenName === 'Home') {
      console.log(
        '🚀 ~ handleTabPress ~ screen.screenName:',
        screen.screenName,
      );
      console.log(
        '🚀 ~ handleTabPress ~ navigation:',
        navigation.getState().routes[0].state?.routes[0].params,
      );
      if (navigation.canGoBack()) navigation.popToTop();
    } else {
      try {
        navigateTo(
          navBeanObj({
            ...screen.cta,
            actionUrl: `${getBaseURL()}/${screen?.cta?.actionUrl}`,
            navigationType:
              NavigationType[
                (
                  screen.cta?.navigationType ?? NavigationType.PUSH
                ).toUpperCase() as keyof typeof NavigationType
              ],
          }),
          navigation,
        );
      } catch (error) {
        console.log('🚀 ~ handleTabPress ~ error:', error);
      }
    }
  };

  const scrollPosition = useSelector((state: any) => state.web.scrollPosition);

  const translateY = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(translateY, {
      toValue: scrollPosition === 1 ? 120 : 0,
      duration: 300,
      easing: Easing.ease,
      useNativeDriver: true,
    }).start();
  }, [scrollPosition]);

  return (
    // (webViewEventData?.isBnbVisible === 1)
    // ?
    //bottomTabs.includes(currentRoute) ? (
    bottomNavBarConfig ? (
      <Animated.View
        style={[
          styles.container,
          {
            transform: [{translateY}],
          },
        ]}>
        {bottomNavBarConfig.map(item =>
          loader ? (
            <BottomTabShimmer />
          ) : (
            <Pressable
              key={item.id}
              style={[styles.tab]}
              onPress={() => {
                console.log(`${item.title}`);
                // console.log(`${currentRoute}`);
                handleTabPress(item);
              }}>
              <View style={{justifyContent: 'center', alignItems: 'center'}}>
                <JioIcon
                  ic={item.icon}
                  color={
                    currentRoute === item.screenName
                      ? IconColor.SECONDARY
                      : IconColor.GREY80
                  }
                  kind={IconKind.DEFAULT}
                  size={IconSize.MEDIUM}
                />
              </View>
              <JioText
                text={item.title}
                appearance={JioTypography.BODY_XXS_BOLD}
                color={
                  currentRoute === item.screenName
                    ? 'primary_grey_100'
                    : 'primary_grey_80'
                }
              />
            </Pressable>
          ),
        )}
      </Animated.View>
    ) : null
    //) : null
    // : null
  );
};

export default CustomBottomNavBar;
