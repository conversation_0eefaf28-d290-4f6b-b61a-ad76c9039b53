import {View} from 'react-native';
import React from 'react';
import {JioText, useColor} from '@jio/rn_components';
import {JioTypography, type JioColor} from '@jio/rn_components/src/index.types';

export const enum DividerType {
  THIN = 1,
  SMALL = 2,
  MEDIUM = 4,
  LARGE = 8,
}

export const enum DividerGap {
  GAP2 = 2,
  GAP4 = 4,
  GAP8 = 8,
  GAP12 = 12,
  GAP16 = 16,
  GAP20 = 20,
  GAP24 = 24,
  GAP28 = 28,
  GAP32 = 32,
}

interface DividerProps {
  type: DividerType | number;
  top?: DividerGap | number | string;
  bottom?: DividerGap | number | string;
  vertical?: DividerGap | number | string;
  left?: DividerGap | number | string;
  right?: DividerGap | number | string;
  horizontal?: DividerGap | number | string;
  color?: JioColor;
  text?: string;
}

const Divider = (props: DividerProps) => {
  const {
    type = DividerType.THIN,
    top,
    left,
    right,
    bottom,
    vertical,
    horizontal,
    color,
    text,
  } = props;
  const dividerColor = useColor(color ?? 'primary_grey_40');

  if (text) {
    return (
      <View
        style={{
          flexDirection: 'row',
        }}>
        <View
          style={{
            height: type,
            flex: 1,
            backgroundColor: dividerColor,
            marginTop: top,
            marginBottom: bottom,
            marginHorizontal: horizontal,
            marginVertical: vertical,
            marginLeft: left,
            marginRight: right,
            alignSelf: 'center',
          }}
        />
        <JioText
          text={text}
          appearance={JioTypography.OVERLINE}
          color={color ?? 'primary_grey_60'}
          style={{
            marginTop: top,
            marginBottom: bottom,
            marginHorizontal: horizontal,
            alignSelf: 'center',
          }}
        />
        <View
          style={{
            height: type,
            flex: 1,
            alignSelf: 'center',
            backgroundColor: dividerColor,
            marginTop: top,
            marginBottom: bottom,
            marginHorizontal: horizontal,
            marginVertical: vertical,
            marginLeft: right,
            marginRight: left,
          }}
        />
      </View>
    );
  }

  return (
    <View
      style={{
        height: type,
        backgroundColor: dividerColor,
        marginTop: top,
        marginBottom: bottom,
        marginHorizontal: horizontal,
        marginVertical: vertical,
        marginLeft: left,
        marginRight: right,
      }}
    />
  );
};

export default Divider;
