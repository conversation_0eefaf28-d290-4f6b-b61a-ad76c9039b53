import {
  FlatList,
  StyleSheet,
  View,
  type StyleProp,
  type ViewStyle,
} from 'react-native';
import React from 'react';
import type {JioTextProps} from '@jio/rn_components/src/index.types';
import {JioText} from '@jio/rn_components';

interface JMTableProps {
  header?: JMTableHeaderProps[];
  data: JMTableBodyProps[][];
  footer?: JMTableFooterProps[];
  style?: StyleProp<ViewStyle>;
  rowStyle?: {
    [key: number]: StyleProp<ViewStyle>;
  };
  columnStyle?: {
    [key: number]: StyleProp<ViewStyle>;
  };
  cellStyle?: StyleProp<ViewStyle>;
  headerStyle?: StyleProp<ViewStyle>;
  footerStyle?: StyleProp<ViewStyle>;
  ItemSeparatorComponent?: React.ComponentType<any> | null | undefined;
}

export interface JMTableHeaderProps extends JioTextProps {
  style?: StyleProp<ViewStyle>;
}

export interface JMTableBodyProps extends JioTextProps {
  style?: StyleProp<ViewStyle>;
}

export interface JMTableFooterProps extends JioTextProps {
  style?: StyleProp<ViewStyle>;
}

const JMTable = (props: JMTableProps) => {
  const {
    header,
    data,
    footer,
    style,
    rowStyle,
    columnStyle,
    cellStyle,
    headerStyle,
    footerStyle,
    ItemSeparatorComponent,
  } = props;
  let table = [];
  if (header) {
    table.push(header);
  }
  table = [...table, ...data];
  if (footer) {
    table.push(footer);
  }

  const isHeader = !!(header && header?.length > 0);
  const isFooter = !!(footer && footer?.length > 0);
  const startRowIndex = isHeader ? 1 : 0;

  return (
    <View>
      <FlatList
        data={table}
        style={style}
        ItemSeparatorComponent={ItemSeparatorComponent}
        renderItem={({item, index}) => {
          if (isHeader && index === 0) {
            return (
              <View style={[styles.row, headerStyle]}>
                {item?.map((cell, index) => {
                  return (
                    <View
                      key={index}
                      style={[
                        styles.cell,
                        cell.style,
                        cellStyle,
                        columnStyle?.[index],
                      ]}>
                      <JioText {...cell} />
                    </View>
                  );
                })}
              </View>
            );
          }
          if (isFooter && index === table?.length - 1) {
            return (
              <View style={[styles.row, footerStyle]}>
                {item?.map((cell, index) => {
                  return (
                    <View
                      key={index}
                      style={[
                        styles.cell,
                        cell.style,
                        cellStyle,
                        columnStyle?.[index],
                      ]}>
                      <JioText {...cell} />
                    </View>
                  );
                })}
              </View>
            );
          }
          console.log('indes', index - startRowIndex);
          return (
            <View style={[styles.row, rowStyle?.[index - startRowIndex]]}>
              {item?.map((cell, index) => {
                return (
                  <View
                    key={index}
                    style={[
                      styles.cell,
                      cell.style,
                      cellStyle,
                      columnStyle?.[index],
                    ]}>
                    <JioText {...cell} />
                  </View>
                );
              })}
            </View>
          );
        }}
        keyExtractor={(_, index) => `row-${index}`}
      />
    </View>
  );
};

export default JMTable;

const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
  },
  cell: {},
});
