import { StyleSheet } from "react-native";
import { rh } from "../../../../../jiomart-common/src/JMResponsive";

export const styles = StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'flex-start',
      borderWidth:1,
      borderColor: "red"
    },
    tabContainer: {
      flexDirection: 'row',
      marginHorizontal: 24,
      marginTop: rh(4),
      marginBottom: rh(12),
    //   paddingVertical: 10,
      backgroundColor: '#8CC5DA',
      borderRadius: 48,
      overflow: 'hidden',
      position: 'relative',

    },
    tabOption: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 4,
    },
    slideIndicator: {
      borderWidth: 4,
      position: 'absolute',
      backgroundColor: '#ffff',
      borderRadius: 32,
      borderColor: '#8CC5DA',
    },
    content: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 20,
    },
    tabContent: {
      marginTop: 10,
      justifyContent: 'center',
      alignItems: 'center',
    },
    indicatorImage: {
      width: 66,
      height: 20,
      alignItems: 'center',
    },
    additionalContent: {
      marginTop: 30,
      padding: 10,
      backgroundColor: '#f0f0f0',
    },
  });
