import React from 'react';
import {View, Image, StyleSheet} from 'react-native';
import { rw, rh } from '../../../../jiomart-common/src/JMResponsive';

interface JMFastImageProps {
  imageUrl: string;
}

const JMFastImage = ({imageUrl}: JMFastImageProps) => {

  return (
    <Image
      style={{width: rw(70), height: rh(20)}}
      source={{
        uri: imageUrl,
      }}
      resizeMode="cover"
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: 50,
    height: 50,
    borderRadius: 10,
    borderWidth: 1,
  },
});

export default JMFastImage;
