import { NativeStackNavigationProp } from '@react-navigation/native-stack';

export interface SliderProps {
    onTabClick?: (option: any) => void;
    shouldEnableScrollAnimation? : boolean;
    navigation?: NativeStackNavigationProp<any>;
  }
  
  export interface SliderRef {
    onScroll?: (value: number) => void;
  }
  
  export interface UseSliderProps extends SliderProps {}
  export interface UseSliderRef extends SliderRef {}
  