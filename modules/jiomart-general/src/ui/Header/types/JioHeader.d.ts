import type {
  JioAvatarProps,
  JioColor,
  JioIconProps,
} from '@jio/rn_components/src/index.types';
import type {StyleProp, TextInputProps, ViewStyle} from 'react-native';
import type {NavigationBean} from '../../../../../jiomart-common/src/JMNavGraphUtil';
import type {CustomMediaRenderedProps} from '../../CustomMediaRendered';

export interface UseJioHeaderControllerProps
  extends JioHeaderProps,
    JMHeaderNavProps {}
export interface UseJioHeaderControllerRef extends JioHeaderRef {}

export interface JioHeaderProps extends NewJMHeaderProps {
  style?: {
    headerBackgroundColor?: JioColor;
    headerStyle?: StyleProp<ViewStyle>;
  };
  leftIconList?: JioHeaderIconList[];
  rightIconList?: JioHeaderIconList[];
  title?: string;
  subTitle?: string;
  headerTopComponent?: React.ReactNode;
  headerBottomComponent?: React.ReactNode;
  searchBar?: HeaderSearchBar;
  onPress?: (cta: NavigationBean) => void;
  customFunctionality?: {
    [key: string]: JioHeaderCustomFunctionality;
  };
}

interface JioHeaderCustomFunctionality
  extends Omit<JioHeaderIconBadgeAndNotify, 'onPress'> {
  disableDefaultCall?: boolean;
  onPress?: (cta: NavigationBean) => void;
}

export interface NewJMHeaderProps {
  backgroundColor?: string;
  headerIconsList?: HeaderIconsList;
  searchData?: SearchData;
}

export interface JMHeaderNavProps extends JioHeaderProps {
  data?: NewJMHeaderProps;
  navTitle?: string;
  searchValue?: string;
  searchTextHandler?: (text: string) => void;
  onSubmitHandler?: (text: string) => void;
}

interface SearchData {
  placeholder?: string;
  searchBarBackgroundColor?: JioColor;
  searchBar?: JioHeaderIconList;
  enable: boolean;
  enableAnimation: boolean;
  animationTexts?: string[];
}

interface HeaderIconsList {
  leftIconList?: JioHeaderIconList[];
  rightIconList?: JioHeaderIconList[];
}

interface CTA extends NavigationBean {
  navTitle?: string;
  headerVisibility?: number;
  source?: string;
  destination?: string;
  actionUrl?: string;
  actionType?: string;
  bundle?: string;
  type?: string;
  navigationType?: string;
  loginRequired?: boolean;
  headerType?: number;
  userJourneyRequiredState?: number;
}

export interface HeaderSearchBar extends JioHeaderSearchBarProps {
  enableSearchBar?: boolean;
  minimumCharForSearch?: number;
}

export interface JioHeaderIconListViewProps {
  iconList: JioHeaderIconList[];
  style?: StyleProp<ViewStyle>;

  onPress?: (cta: NavigationBean) => void;
  customFunctionality?: {
    [key: string]: JioHeaderCustomFunctionality;
  };
}

export interface JioHeaderIconList {
  icon?: JioHeaderIcon;
  avatar?: JioHeaderAvatar;
  image?: JioHeaderImage;
  cta?: CTA;
}

export interface JioHeaderRef {}

export interface JioHeaderIcon
  extends JioIconProps,
    JioHeaderIconBadgeAndNotify {}
export interface JioHeaderAvatar
  extends JioAvatarProps,
    JioHeaderIconBadgeAndNotify {}
export interface JioHeaderImage
  extends CustomMediaRenderedProps,
    JioHeaderIconBadgeAndNotify {
  width?: number;
  height?: number;
}
export interface JioHeaderIconBadgeAndNotify {
  style?: StyleProp<ViewStyle>;
  isNotify?: boolean;
  badge?: number;
  onPress?: () => void;
  disabled?: boolean;
  feedback?: boolean;
}

export interface JioHeaderBadgeProps {
  count?: number;
  style?: StyleProp<ViewStyle>;
}

export interface JioHeaderNotifyProps {
  style?: StyleProp<ViewStyle>;
}

export interface UseJioHeaderSearchBarProps extends JioHeaderSearchBarProps {}

export interface JioHeaderSearchBarProps extends SearchData {
  textInput?: TextInputProps;
  style?: StyleProp<ViewStyle>;
  isEditable?: boolean;
  onPress?: (cta: NavigationBean) => void;
  onSubmitHandler?: (text: string) => void;
  debounceDelaySearchBarClick?: number;
  leftIcon?: JioHeaderSearchIcon[];
  rightIcon?: JioHeaderSearchIcon[];
  enableAnimation?: boolean;
  animationTexts?: string[];
  fadeAnim?: any;
  translateYAnim?: any;
  currentIndex?: any;
}

export interface JioHeaderSearchBarIconListViewProps {
  icon?: JioHeaderSearchIcon[];
}

export interface JioHeaderSearchIcon
  extends Omit<JioHeaderIcon, 'isNotify' | 'badge'> {
  shouldHideIconOnFocus?: boolean;
  shouldHideIconOnText?: boolean;
}
