import type {JioHeaderProps} from './JioHeader';

export interface JioMartHeaderProps
  extends JioHeaderProps,
    JioMartDeliverToBar {
  headerType?: number;
  disableDefaultFunctionality?: {
    left?: number[];
    right?: number[];
    s_left?: number[];
    s_right?: number[];
  };
  customFunctionality?: {
    left?: CustomFuncHandler;
    right?: CustomFuncHandler;
    s_left?: CustomFuncHandler;
    s_right?: CustomFuncHandler;
  };
  hideIcon?: {
    left?: number[];
    right?: number[];
    s_left?: number[];
    s_right?: number[];
  };
  isDeliverToBarVisible?: (show: boolean) => void;
  onDeliverToBarPress?: () => void;
}


export interface JioMartDeliverToBar {
  isDeliverToBarShow?: boolean;
  onDeliverToBarRef?: (ref: any) => void;
}

export interface JioMartHeaderRef extends JioHeaderRef {
  push?: (newProps: UseJioMartHeaderProps) => void;
  pop?: () => void;
  reset?: (newProps: UseJioMartHeaderProps) => void;
  setHeaderType?: (headerType: number) => void;
  updateProp?: (updatedProps: Partial<UseJioMartHeaderProps>) => void;
  popLength?: (length: number) => void;
  size?: () => number;
  empty?: () => boolean;
  top?: () => UseJioMartHeaderProps;
  updateDeliverToBarShow?: (show: boolean) => void;
}

export interface UseJioMartHeaderProps extends JioMartHeaderProps {}
export interface UseJioMartHeaderRef extends JioMartHeaderRef {}

export interface CustomFuncHandler {
  [key: number]: CustomFunc;
}

export interface CustomFunc {
  onPress?: () => void;
}

export interface JioMartHeaderConfigProps {
  [key: string]: JioHeaderProps;
}
export interface UseJioMartMergeJioMartHeaderProps extends JioMartHeaderProps {}
