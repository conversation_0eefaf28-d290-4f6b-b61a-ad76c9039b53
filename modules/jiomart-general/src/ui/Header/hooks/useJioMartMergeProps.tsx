import {useCallback} from 'react';
import type {<PERSON><PERSON><PERSON><PERSON>er<PERSON>conList, JioHeaderSearchIcon} from '../types/JioHeader';
import type {
  JioMartHeaderConfigProps,
  JioMartHeaderProps,
} from '../types/JioMartHeader';

const useJioMartMergeProps = () => {
  const mergeIconProps = useCallback(
    <T extends JioHeaderIconList | JioHeaderSearchIcon>(
      iconList: T[],
      keyPos: string,
      disableDefaultFunctionality: any,
      customFunctionality: any,
    ): T[] => {
      return iconList?.map((item, index) => {
        const isDisabled =
          disableDefaultFunctionality?.[keyPos]?.includes(index);
        const onPressHandler = () => {
          if (!isDisabled) {
            item?.icon?.onPress?.();
            item?.avatar?.onPress?.();
          }
          customFunctionality?.[keyPos]?.[index]?.onPress?.();
        };

        //{"avatar": {"isNotify": false, "kind": "icon", "name": "undefined undefined", "onPress": [Function anonymous]}}

        return {
          ...item,
          icon: item.icon ? {...item.icon, onPress: onPressHandler} : undefined,
          avatar: item.avatar
            ? {...item.avatar, onPress: onPressHandler}
            : undefined,
        };
      });
    },
    [],
  );

  const filterHiddenIcons = useCallback(
    (iconList: JioHeaderIconList[], keyPos: string, hideIcon: any) =>
      iconList?.filter((_, index) => !hideIcon?.[keyPos]?.includes(index)),
    [],
  );

  const extractMergeProps = useCallback(
    (
      defaultProps: JioMartHeaderConfigProps,
      customProps: JioMartHeaderProps,
    ) => {
      const {
        disableDefaultFunctionality,
        hideIcon,
        customFunctionality,
        headerType,
      } = customProps;

      if (!headerType || headerType === 2) {
        return null;
      } // Early return

      const defaultHeaderProps = defaultProps[`header_${headerType}`];

      const leftIconList = mergeIconProps(
        filterHiddenIcons(
          defaultHeaderProps?.leftIconList || [],
          'left',
          hideIcon,
        ),
        'left',
        disableDefaultFunctionality,
        customFunctionality,
      );
      const rightIconList = mergeIconProps(
        filterHiddenIcons(
          defaultHeaderProps?.rightIconList || [],
          'right',
          hideIcon,
        ),
        'right',
        disableDefaultFunctionality,
        customFunctionality,
      );

      const leftIcon = mergeIconProps(
        defaultHeaderProps?.searchBar?.leftIcon || [],
        's_left',
        disableDefaultFunctionality,
        customFunctionality,
      );
      const rightIcon = mergeIconProps(
        defaultHeaderProps?.searchBar?.rightIcon || [],
        's_right',
        disableDefaultFunctionality,
        customFunctionality,
      );

      const onSearchBarClick = () => {
        defaultHeaderProps?.searchBar?.onSearchBarClick?.();
        customProps?.searchBar?.onSearchBarClick?.();
      };

      const title = defaultHeaderProps?.title === '' ? customProps?.title : '';
      const subTitle =
        defaultHeaderProps?.subTitle === '' ? customProps?.subTitle : '';

      return {
        ...defaultHeaderProps,
        leftIconList,
        rightIconList,
        title,
        subTitle,
        style: customProps?.style,
        headerTopComponent: customProps?.headerTopComponent,
        headerBottomComponent: customProps?.headerBottomComponent,
        searchBar: {
          ...defaultHeaderProps?.searchBar,
          textInput: {
            ...defaultHeaderProps?.searchBar?.textInput,
            ...customProps?.searchBar?.textInput,
          },
          onSearchBarClick,
          leftIcon,
          rightIcon,
        },
      };
    },
    [filterHiddenIcons, mergeIconProps],
  );

  return {extractMergeProps};
};

export default useJioMartMergeProps;
