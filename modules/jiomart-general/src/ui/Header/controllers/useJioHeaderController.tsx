import {useColor} from '@jio/rn_components';
import type {UseJioHeaderControllerProps} from '../types/JioHeader';

const useJioHeaderController = (props: UseJioHeaderControllerProps) => {
  const {style} = props;
  const backgroundColor = useColor(
    style?.headerBackgroundColor ?? 'primary_50',
  );
  return {
    ...props,
    style: {
      ...props?.style,
      headerBackgroundColor: backgroundColor,
    },
  };
};

export default useJioHeaderController;
