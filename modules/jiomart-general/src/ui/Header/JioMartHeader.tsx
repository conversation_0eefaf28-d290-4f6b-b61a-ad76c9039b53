import React from 'react';
import <PERSON><PERSON><PERSON>eader from './JioHeader';
import {JMHeaderNavProps} from './types/JioHeader';

export enum NavigationBeanType {
  BACK = 'back',
  SEARCH = 'search',
  CART = 'cart',
  AVATAR = 'avatar',
  SCANNER = 'qrscanner',
  WISHLIST = 'wishlist',
  ORDER_FILTER = 'order-filter',

}

const JioMartHeader = (props: JMHeaderNavProps) => {
  return <JioHeader {...props} />;
};

export default React.memo(JioMartHeader);
