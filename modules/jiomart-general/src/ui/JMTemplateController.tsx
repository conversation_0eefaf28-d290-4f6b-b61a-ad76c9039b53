import {NavigationBean} from '../../../jiomart-common/src/JMNavGraphUtil';
import {JMTemplateViewType} from '../../../jiomart-common/src/JMTemplateViewType';
import DiscoverMore from '../../../jiomart-search/src/components/DiscoverMore';
import MultiSearch from '../../../jiomart-search/src/components/MultiSearch';
import ProductGrid from '../../../jiomart-search/src/components/ProductGrid';
import RecentSearch from '../../../jiomart-search/src/components/RecentSearch';
import RecommendedProducts from '../../../jiomart-search/src/components/RecommendedProducts';
import SearchHorizontalList from '../../../jiomart-search/src/components/SearchHorizontalList';
import TopCategories from '../../../jiomart-search/src/components/TopCategories';

export function renderTemplate(
  props: any,
  index: number,
  onClick?: (navBean: NavigationBean) => void,
): React.JSX.Element {
  switch (props.viewType) {
    case JMTemplateViewType.SHOPPING_LIST_BLOCK:
      return (
        <MultiSearch key={index} template={props} onClick={props?.onClick} />
      );
    case JMTemplateViewType.RECENT_SEARCHES:
      return (
        <RecentSearch key={index} template={props} onClick={props?.onClick} />
      );
    case JMTemplateViewType.DISCOVER_MORE:
      return (
        <DiscoverMore key={index} template={props} onClick={props?.onClick} />
      );
    case JMTemplateViewType.POPULAR_CATEGORY:
      return (
        <TopCategories key={index} template={props} onClick={props?.onClick} />
      );
    case JMTemplateViewType.RECOMMENDED_PRODUCT:
      return (
        <RecommendedProducts
          key={index}
          template={props}
          onClick={props?.onClick}
        />
      );
    case JMTemplateViewType.SEARCH_HORIZONTAL:
      return (
        <SearchHorizontalList
          key={index}
          template={props}
          onClick={props?.onClick}
        />
      );
    case JMTemplateViewType.PRODUCT_GRID:
      return (
        <ProductGrid key={index} template={props} onClick={props?.onClick} />
      );
    default: {
      return <></>;
    }
  }
}
