import {
  CustomJioIcon,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  useTheme,
} from '@jio/rn_components';
import {
  Duration,
  IconColor,
  IconSize,
  JioSpinnerAppearance,
  JioSpinnerSize,
  JioTypography,
  SchematicState,
  ToastType,
  type JioToastProps,
} from '@jio/rn_components/src/index.types';
import React, {useEffect, useState} from 'react';
import {
  View,
  StyleSheet,
  Animated,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  Pressable,
} from 'react-native';

const screenHeight = Dimensions.get('screen').height;

function getToastIcon(semanticState: SchematicState) {
  if (semanticState === SchematicState.ERROR) {
    return 'IcErrorColored';
  } else if (semanticState === SchematicState.WARNING) {
    return 'IcWarningColored';
  } else if (semanticState === SchematicState.SUCCESS) {
    return 'IcSuccessColored';
  } else {
    return 'IcInfo';
  }
}
function getIconColor(semanticState: SchematicState) {
  switch (semanticState) {
    case SchematicState.ERROR:
      return IconColor.ERROR;
    case SchematicState.WARNING:
      return IconColor.WARNING;
    case SchematicState.SUCCESS:
      return IconColor.SUCCESS;
    default:
      return IconColor.PRIMARY;
  }
}
function getDurationValue(duration: Duration) {
  switch (duration) {
    case Duration.SHORT:
      return 3000;
    case Duration.LONG:
      return 7000;
    case Duration.PERSIST:
      return Number.MAX_SAFE_INTEGER;
    default:
      return 5000;
  }
}

export interface JMToastProps extends JioToastProps {
  showButton?: boolean;
  buttonText?: string;
  onClick?: () => void;
}

function JMToast({
  isVisible,
  message,
  subTitle,
  semanticState = SchematicState.ERROR,
  duration = Duration.MEDIUM,
  showClose = false,
  type = ToastType.SCHEMATIC,
  maxLines = 3,
  style,
  showButton,
  buttonText,
  onClick,
  onDismiss,
}: JMToastProps) {
  const theme = useTheme();
  const [slideAnimation] = useState(new Animated.Value(0));

  useEffect(() => {
    Animated.timing(slideAnimation, {
      toValue: isVisible ? 1 : 0,
      duration: 700,
      useNativeDriver: true,
    }).start();
  }, [slideAnimation, isVisible]);

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    if (isVisible) {
      timeoutId = setTimeout(() => {
        if (onDismiss) {
          onDismiss();
        }
      }, getDurationValue(duration));
    }
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isVisible, duration]);

  const getToastPrefix = (
    toastType: ToastType,
    semanticState: SchematicState,
  ) => {
    switch (toastType) {
      case ToastType.SCHEMATIC:
        return (
          semanticState && (
            <JioIcon
              ic={getToastIcon(semanticState)}
              size={IconSize.LARGE}
              color={getIconColor(semanticState)}
            />
          )
        );
      case ToastType.SPINNER:
        return (
          <JioSpinner
            size={JioSpinnerSize.SMALL}
            appearance={JioSpinnerAppearance.VIBRANT}
          />
        );
      default:
        null;
    }
  };
  return (
    <Animated.View
      style={{
        zIndex: 1,
        position: 'absolute',
        bottom: -screenHeight,
        left: 0,
        right: 0,
        transform: [
          {
            translateY: slideAnimation.interpolate({
              inputRange: [0, 1],
              outputRange: [screenHeight, -screenHeight],
            }),
          },
        ],
      }}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'position' : 'height'}>
        <View
          style={[
            styles.container,
            style,
            {backgroundColor: theme.primary_grey_80},
          ]}>
          {getToastPrefix(type, semanticState)}
          <View style={{gap: 4, flex: 1}}>
            <JioText
              style={[styles.message]}
              text={message}
              color={'primary_background'}
              appearance={JioTypography.BODY_S_BOLD}
              maxLines={maxLines}
            />
            <JioText
              style={[styles.subText]}
              text={subTitle}
              color={'primary_grey_20'}
              appearance={JioTypography.BODY_XS}
            />
          </View>
          {showClose && (
            <Pressable onPress={onDismiss}>
              <CustomJioIcon
                ic={'IcClose'}
                kind={'default'}
                color={'primary_20'}
                size={IconSize.SMALL}
              />
            </Pressable>
          )}
          {showButton ? (
            <Pressable
              style={styles.btnWrapper}
              onPress={onClick}>
              <JioText
                text={buttonText}
                appearance={JioTypography.BODY_XXS_LINK}
                color="primary_inverse"
              />
            </Pressable>
          ) : null}
        </View>
      </KeyboardAvoidingView>
    </Animated.View>
  );
}
const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 24,
    margin: 24,
    gap: 12,
  },
  message: {
    flexWrap: 'wrap',
  },
  subText: {
    flexWrap: 'wrap',
  },
  btnWrapper: {
    borderWidth: 1,
    paddingHorizontal: 16,
    paddingVertical: 4,
    borderRadius: 100,
    borderColor: '#B5B5B5',
    marginLeft: 'auto',
  },
});
export default JMToast;
