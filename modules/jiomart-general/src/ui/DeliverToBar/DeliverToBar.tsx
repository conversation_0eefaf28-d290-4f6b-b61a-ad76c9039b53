import {Image, Platform, Pressable, View} from 'react-native';
import React, {forwardRef} from 'react';
import {JioIcon, JioText} from '@jio/rn_components';
import {
  IconColor,
  IconSize,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import Animated from 'react-native-reanimated';
import type {
  DeliverToBarProps,
  DeliverToBarRef,
} from './types/DeliverToBarType';
import useDeliverToBar from './hooks/useDeliverToBar';
import {styles} from './styles/DeliverToBarStyle';
import {rw, rh} from '../../../../jiomart-common/src/JMResponsive';
import CustomMediaRendered from '../CustomMediaRendered';
import {isNullOrUndefinedOrEmpty} from '../../../../jiomart-common/src/JMObjectUtility';

const DeliverToBar = forwardRef<DeliverToBarRef, DeliverToBarProps>(
  (props: DeliverToBarProps, ref: React.Ref<DeliverToBarRef>) => {
    const {
      text,
      onPress,
      deliverToBarStyle,
      primary20,
      qcDetails,
      selectedOption,
      quickCommerceConfig,
      deliverToBarData,
      offsetAnim,
    } = useDeliverToBar(props, ref);
    const message = !isNullOrUndefinedOrEmpty(deliverToBarData?.qcMessage)
      ? deliverToBarData?.qcMessage
      : selectedOption === 'Quick'
      ? qcDetails?.qcMessage
      : qcDetails?.scheduledMessage ?? "Scheduled Delivery";

    return (
      <Animated.View style={[styles.negZindex, deliverToBarStyle]}>
        <Pressable style={[styles.container, {height: offsetAnim}]} onPress={onPress}>
          <View style={styles.deliveryInfoContainer}>
            <View style={styles.addressContainer}>
              <View style={styles.wrapper}>
                <JioText
                  text={quickCommerceConfig?.prefixTitle}
                  color={'primary_grey_100'}
                  appearance={JioTypography.BODY_XS_BOLD}
                  maxLines={1}
                />
                <JioText
                  text={text ?? ''}
                  color={'primary_grey_100'}
                  appearance={JioTypography.BODY_XS}
                  maxLines={1}
                  style={{flexShrink: 1}}
                />
              </View>
              {!isNullOrUndefinedOrEmpty(message) && (message != quickCommerceConfig?.scheduledDeliveryMessage || !isNullOrUndefinedOrEmpty(deliverToBarData?.qcMessage) || selectedOption !== 'Quick')  && (
                <View style={styles.quickDeliveryContainer}>
                  {(message != quickCommerceConfig?.scheduledDeliveryMessage) ? (
                    <>
                      <CustomMediaRendered
                        height={rw(12)}
                        width={rw(50)}
                        mediaUrl={quickCommerceConfig?.quickImageUrl}
                      />
                      <View style={styles.messageBox}>
                        <JioText
                          text={message}
                          color={'sparkle_70'}
                          appearance={JioTypography.BODY_XXS}
                          maxLines={1}
                        />
                      </View>
                    </>
                  ) : (
                    <JioText
                      text={message ?? ''}
                      color={'primary_50'}
                      appearance={JioTypography.BODY_XS}
                      maxLines={1}
                    />
                  )}
                </View>
              )}
            </View>
          </View>
          <JioIcon ic="IcChevronDown" color={IconColor.PRIMARY60} />
        </Pressable>
      </Animated.View>
    );
  },
);

export default React.memo(DeliverToBar);
