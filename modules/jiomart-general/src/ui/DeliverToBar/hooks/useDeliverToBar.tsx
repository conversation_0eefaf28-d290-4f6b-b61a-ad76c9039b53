import {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import type {
  UseDeliverToBarProps,
  UseDeliverToBarRef,
} from '../types/DeliverToBarType';
import {useColor} from '@jio/rn_components';
import {useImperativeHandle} from 'react';
import {useGlobalState} from '../../../context/JMGlobalStateProvider';
import {JMConfigFileName} from '../../../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {useConfigFile} from '../../../hooks/useJMConfig';
import {isNullOrUndefinedOrEmpty} from '../../../../../jiomart-common/src/JMObjectUtility';

const useDeliverToBar = (
  props: UseDeliverToBarProps,
  ref?: React.Ref<UseDeliverToBarRef>,
) => {
  const commanConfigData = useConfigFile(
    JMConfigFileName.JMCommonContentFileName,
  );
  const quickCommerceConfig = commanConfigData?.quickCommerceConfig;

  const primary20 = useColor('primary_20');
  const prevScrollY = useSharedValue(0);
  const isVisible = useSharedValue(true);

  const {qcDetails, selectedOption} = useGlobalState();
  const message = !isNullOrUndefinedOrEmpty(props?.deliverToBarData?.qcMessage)
  ? props?.deliverToBarData?.qcMessage
  : selectedOption === 'Quick'
  ? qcDetails?.qcMessage
  : qcDetails?.scheduledMessage ?? "Scheduled Delivery";
  const offsetAnim =
    !isNullOrUndefinedOrEmpty(message) &&
    (message != quickCommerceConfig?.scheduledDeliveryMessage || !isNullOrUndefinedOrEmpty(props?.deliverToBarData?.qcMessage) ||
      selectedOption !== 'Quick')
      ? 56
      : 40;
  const deliverToBarStyle = useAnimatedStyle(() => {
    const translateY = withTiming(isVisible.value ? 0 : -offsetAnim, {
      duration: 200,
    });

    const height = withTiming(isVisible.value ? offsetAnim : 0, {
      duration: 200,
    });

    const opacity = withTiming(Number(isVisible.value), {
      duration: 200,
    });

    return {
      transform: [{translateY}],
      height,
      opacity,
    };
  });

  useImperativeHandle(ref, () => ({
    onScroll: (value: any, forceVisible: boolean = false) => {
      if (value < 0) {
        return;
      }
      if(forceVisible){
        isVisible.value = true
      }
      else{
        const currentScrollY = value;
        const previousScrollY = prevScrollY.value;
        const scrollDiff = Math.abs(currentScrollY - previousScrollY);
  
        // Only trigger animation if scroll movement is substantial (15px threshold)
        // This prevents flickering during slow/gentle scrolling
        if (scrollDiff >= 60) {
          isVisible.value = currentScrollY <= previousScrollY;
          prevScrollY.value = currentScrollY;
        }
      }
    },
  }));

  return {
    ...props,
    primary20,
    deliverToBarStyle,
    qcDetails,
    selectedOption,
    quickCommerceConfig,
    offsetAnim,
  };
};

export default useDeliverToBar;
