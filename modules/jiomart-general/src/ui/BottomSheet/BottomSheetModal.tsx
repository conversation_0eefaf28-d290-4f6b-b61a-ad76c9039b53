import React, {
  useState,
  useCallback,
  forwardRef,
  useImperativeHandle,
  useEffect,
  useMemo,
  type ForwardedRef,
} from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableWithoutFeedback,
  KeyboardAvoidingView,
  Platform,
  AppState,
  Modal,
} from 'react-native';
import {
  GestureHandlerRootView,
  PanGestureHandler,
  ScrollView,
} from 'react-native-gesture-handler';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  runOnJS,
} from 'react-native-reanimated';
import type {BottomSheetProps} from './types/BottomSheetType';

const {height: SCREEN_HEIGHT} = Dimensions.get('window');
let MAX_HEIGHT = SCREEN_HEIGHT * 0.75; // Max height for dynamic sheet

export interface BottomSheetRef {
  open: () => void;
  close: () => void;
}

let bottomSheetRef: ForwardedRef<BottomSheetRef> | null = null;

const BottomSheetModal = forwardRef<BottomSheetRef, BottomSheetProps>(
  ({children, ...rest}: BottomSheetProps, ref) => {
    const {
      onBackDropClick,
      onDrag,
      isStatic = false,
      staticHeight = 300,
      disabledBackDropClick = false,
      disabledGesture = false,
      dragTime = 10,
      smoothTime = 350,
      maxHeightPercent = 75,
      enableKeyboarAvoidingView = true,
      dragThresholdPercent = 25,
      headerComponent,
      footerComponent,
      isStretchEnabled = false,
    } = rest;

    MAX_HEIGHT = SCREEN_HEIGHT * (maxHeightPercent / 100);

    const translateY = useSharedValue(SCREEN_HEIGHT); // Start with the sheet off-screen
    const [contentHeight, setContentHeight] = useState(0);
    const [visible, setVisible] = useState(false);
    const [isHeightMeasured, setIsHeightMeasured] = useState(false);

    const animatedStyle = useAnimatedStyle(() => ({
      transform: [{translateY: translateY.value}],
    }));

    const dynamicHeightStyle = useMemo(
      () =>
        isStatic
          ? {height: staticHeight}
          : {height: Math.min(contentHeight, MAX_HEIGHT)},
      [contentHeight, isStatic, staticHeight],
    );

    const open = useCallback(() => {
      try {
        if (bottomSheetRef && bottomSheetRef != ref) {
          bottomSheetRef?.current?.close();
        }
        bottomSheetRef = ref;
        setTimeout(() => {
          if (!visible) {
            setVisible(true);
          }
        }, smoothTime + 50);
      } catch (error) {}
    }, [ref, smoothTime, visible]);

    const close = useCallback(() => {
      translateY.value = withTiming(
        SCREEN_HEIGHT,
        {
          duration: smoothTime,
        },
        () => {
          runOnJS(setVisible)(false);
          runOnJS(setContentHeight)(0);
          runOnJS(setIsHeightMeasured)(false);
        },
      );
    }, [smoothTime, translateY]);

    const handleGesture = useCallback(
      (event: any) => {
        if (!disabledGesture && event.nativeEvent.translationY > 0) {
          translateY.value = event.nativeEvent.translationY;
        }
      },
      [disabledGesture, translateY],
    );

    const handleGestureEnd = useCallback(
      (event: any) => {
        if (!disabledGesture) {
          const dragThreshold = SCREEN_HEIGHT * (dragThresholdPercent / 100);

          if (event.nativeEvent.translationY < dragThreshold) {
            translateY.value = withTiming(0, {duration: dragTime});
          } else {
            translateY.value = withTiming(
              SCREEN_HEIGHT,
              {duration: smoothTime},
              () => {
                runOnJS(setVisible)(false);
                runOnJS(setIsHeightMeasured)(false);
                onDrag ? runOnJS(onDrag)() : null;
              },
            );
          }
        }
      },
      [
        disabledGesture,
        dragThresholdPercent,
        dragTime,
        onDrag,
        smoothTime,
        translateY,
      ],
    );

    useImperativeHandle(
      ref,
      () => ({
        open,
        close,
      }),
      [open, close],
    );
    useEffect(() => {
      if (visible && (isHeightMeasured || isStatic)) {
        translateY.value = withTiming(0, {
          duration: smoothTime,
        });
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [visible, isHeightMeasured, isStatic]);

    useEffect(() => {
      const handleAppStateChange = (nextAppState: string) => {
        if (Platform.OS === 'ios' && nextAppState === 'background') {
          bottomSheetRef?.current?.close();
        }
      };

      const subscription = AppState.addEventListener(
        'change',
        handleAppStateChange,
      );
      return () => subscription.remove();
    }, [close]);

    const styles = StyleSheet.create({
      modalBackground: {
        flex: 1,
        justifyContent: 'flex-end',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
      },
      bottomSheetContainer: {
        backgroundColor: '#fff',
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        width: '100%',
        maxHeight: MAX_HEIGHT,
        overflow: 'hidden',
      },
    });

    return (
      <Modal
        visible={visible}
        transparent
        animationType="fade"
        onRequestClose={() => {
          if (!disabledBackDropClick) {
            close();
            onBackDropClick?.();
          }
        }}
        presentationStyle={'overFullScreen'}
        statusBarTranslucent={!enableKeyboarAvoidingView}
        onDismiss={() => {
          console.log('dismiss bottomsheet');
        }}
        onShow={() => {
          console.log('on show bottomsheet');
        }}
        hardwareAccelerated>
        <GestureHandlerRootView>
          <TouchableWithoutFeedback
            onPress={() => {
              if (!disabledBackDropClick) {
                close();
                onBackDropClick?.();
              }
            }}>
            <View style={styles.modalBackground}>
              <TouchableWithoutFeedback>
                <KeyboardAvoidingView
                  behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                  enabled={enableKeyboarAvoidingView}>
                  <View>
                    <PanGestureHandler
                      onGestureEvent={handleGesture}
                      onEnded={handleGestureEnd}>
                      <Animated.View
                        style={[
                          styles.bottomSheetContainer,
                          animatedStyle,
                          dynamicHeightStyle,
                        ]}>
                        {React.isValidElement(headerComponent)
                          ? React.cloneElement(headerComponent)
                          : null}
                        {isStatic ? (
                          React.isValidElement(children) ? (
                            React.cloneElement(children, {...rest, close})
                          ) : null
                        ) : (
                          <ScrollView
                            bounces={false}
                            scrollEventThrottle={20}
                            nestedScrollEnabled
                            scrollEnabled={false}
                            showsHorizontalScrollIndicator={false}
                            showsVerticalScrollIndicator={false}
                            onContentSizeChange={(w, h) => {
                              if (isStretchEnabled) {
                                setContentHeight(h);
                              } else {
                                if (contentHeight < h) {
                                  setContentHeight(h);
                                }
                              }
                              setIsHeightMeasured(true);
                            }}>
                            {React.isValidElement(children)
                              ? React.cloneElement(children, {...rest, close})
                              : null}
                          </ScrollView>
                        )}
                        {React.isValidElement(footerComponent)
                          ? React.cloneElement(footerComponent)
                          : null}
                      </Animated.View>
                    </PanGestureHandler>
                  </View>
                </KeyboardAvoidingView>
              </TouchableWithoutFeedback>
            </View>
          </TouchableWithoutFeedback>
        </GestureHandlerRootView>
      </Modal>
    );
  },
);

export default BottomSheetModal;
