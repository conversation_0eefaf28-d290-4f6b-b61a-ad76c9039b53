import {Dimensions, Keyboard, Platform, StyleSheet, StatusBar} from 'react-native';
import type {UseBottomSheetViewControllerProps} from '../types/BottomSheetType.d';
import {
  interpolate,
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import { hideKeyboard } from '../../../../../jiomart-common/src/utils/JMCommonFunctions';
import { JMLogger } from '../../../../../jiomart-common/src/utils/JMLogger';

const {height: SCREEN_HEIGHT} = Dimensions.get('window');
let MAX_HEIGHT = SCREEN_HEIGHT * 0.75;

const useBottomSheetViewController = (
  props: UseBottomSheetViewControllerProps,
) => {
  const {
    visible,
    onBackDropClick,
    onDrag,
    isStatic = false,
    staticHeight = 300,
    disabledBackDropClick = false,
    disabledGesture = false,
    dragTime = 10,
    smoothTime = 350,
    maxHeightPercent = 75,
    enableKeyboarAvoidingView = false,
    dragThresholdPercent = 25,
    headerComponent,
    footerComponent,
    isStretchEnabled = false,
    enableScroll = false,
    offset = 0,
    minHeight = 200,
  } = props;

  MAX_HEIGHT = SCREEN_HEIGHT * (maxHeightPercent / 100);
  const translateY = useSharedValue(SCREEN_HEIGHT); // Start with the sheet off-screen
  const fadeValue = useSharedValue(0);
  const isKeybaordAppear = useSharedValue(false);
  const [contentHeight, setContentHeight] = useState(minHeight);
  const statusBarRef = useRef<StatusBar>(null);

  const backDropStyle = useAnimatedStyle(() => ({
    opacity: interpolate(fadeValue.value, [0, 1], [0, 1]),
  }));

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{translateY: translateY.value}],
  }));

  const dynamicHeightStyle = useMemo(
    () =>
      isStatic
        ? {height: staticHeight}
        : {height: Math.min(contentHeight, MAX_HEIGHT)},
    [contentHeight, isStatic, staticHeight],
  );

  const open = () => {
    try {
      translateY.value = withTiming(0, {
        duration: smoothTime,
      });
      fadeValue.value = withTiming(1, {duration: smoothTime});
    } catch (error) {}
  };

  const closeMethod = (callback?: () => void) => {
    'worklet';
    fadeValue.value = withTiming(0, {duration: smoothTime});
    translateY.value = withTiming(
      SCREEN_HEIGHT,
      {
        duration: smoothTime,
      },
      () => {
        callback ? runOnJS(callback)() : null;
        runOnJS(setContentHeight)(minHeight);
      },
    );
  };

  const close = (callback?: () => void) => {
    if(isKeybaordAppear){
      hideKeyboard()
      setTimeout(() => {
        closeMethod(callback);
      }, 50); 
    }
    else{
      closeMethod(callback);
    }
  };
  const handleGesture = (event: any) => {
    if (
      !disabledGesture &&
      !isKeybaordAppear.value &&
      event.nativeEvent.translationY > 0
    ) {
      translateY.value = event.nativeEvent.translationY;
    }
  };

  const handleGestureEnd = (event: any) => {
    if (!disabledGesture && !isKeybaordAppear.value) {
      const dragThreshold = SCREEN_HEIGHT * (dragThresholdPercent / 100);

      if (event.nativeEvent.translationY < dragThreshold) {
        translateY.value = withTiming(0, {duration: dragTime});
      } else {
        translateY.value = withTiming(
          SCREEN_HEIGHT,
          {duration: smoothTime},
          () => {
            onDrag ? runOnJS(onDrag)() : null;
          },
        );
      }
    }
  };

  const handleKeyboardShow = useCallback(
    (event: any) => {
      if (!enableKeyboarAvoidingView) return;
      try{
        const keyboardHeight = event?.endCoordinates?.height ?? 0;
  
        isKeybaordAppear.value = true;
    
        const delay = Platform.OS === 'android' ? 50 : 0;
    
        runOnJS(JMLogger.log)(`Keyboard show, height: ${keyboardHeight}`);
    
        setTimeout(() => {
          translateY.value = withTiming(-keyboardHeight, {
            duration: Platform.OS === 'android' ? 250 : event.duration,
          });
        }, delay);
      } catch(error){

      }

    },
    [enableKeyboarAvoidingView, isKeybaordAppear, translateY]
  );

  const handleKeyboardHide = (event: any) => {
    if (enableKeyboarAvoidingView) {
      isKeybaordAppear.value = false;
      translateY.value = withTiming(0, {
        duration: event.duration,
      });
    }
  };
  const calculateTop = (height: any) => {
    if (isStatic) {
      return SCREEN_HEIGHT - staticHeight;
    }
    return height > MAX_HEIGHT
      ? SCREEN_HEIGHT - MAX_HEIGHT
      : SCREEN_HEIGHT - height;
  };

  useEffect(() => {
    const showListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      handleKeyboardShow,
    );
    const hideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      handleKeyboardHide,
    );

    return () => {
      if (Platform.OS === 'android') {
        StatusBar.setBackgroundColor('rgba(0, 120, 173, 1)', false);
      }
      showListener.remove();
      hideListener.remove();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (visible) {
      open();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  const styles = StyleSheet.create({
    modalBackground: {
      ...StyleSheet.absoluteFillObject,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      bottom: offset,
      zIndex: 100000000,
    },
    bottomSheetContainer: {
      backgroundColor: '#fff',
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      width: '100%',
      maxHeight: MAX_HEIGHT,
      // overflow: 'hidden',
    },
    position: {
      position: 'absolute',
      left: 0,
      right: 0,
      bottom: offset,
      zIndex: 100000000 + 1,
    },
  });
  return {
    visible,
    contentHeight,
    isStatic,
    onBackDropClick,
    disabledBackDropClick,
    headerComponent,
    footerComponent,
    isStretchEnabled,
    enableScroll,
    backDropStyle,
    animatedStyle,
    dynamicHeightStyle,
    enableKeyboarAvoidingView,
    handleGesture,
    handleGestureEnd,
    calculateTop,
    styles,
    open,
    close,
    setContentHeight,
    statusBarRef,
  };
};

export default useBottomSheetViewController;
