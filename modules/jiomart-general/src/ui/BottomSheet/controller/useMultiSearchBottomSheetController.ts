import {useState} from 'react';
import {Dimensions, Keyboard} from 'react-native';
// import useToast from '../../../src/components/Toast/hook/useToast';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

const useMultiSearchBottomSheetController = () => {
  const [searchText, setSearchText] = useState('');
  const [isValidSearch, setIsValidSearch] = useState(true);

  const insets = useSafeAreaInsets();

//   const {showToast} = useToast();

  const handleTextChange = (inputText: string) => {
    let newInputText = inputText.replace(/^\s+/g, '');
    if (newInputText != inputText) {
      Keyboard.dismiss();
    }
    let isValid =
      newInputText === '' ||
      /^(?=.*[A-Za-z])[A-Za-z0-9, \n]*$/.test(newInputText);
    if (newInputText[0] === ',') {
      let newTrimmerData = newInputText.trim();
      if (newTrimmerData.length == 1) isValid = false;
    }
    setIsValidSearch(isValid);
    setSearchText(newInputText);
  };

  const windowValue = Dimensions.get('window').height;

  return {
    setSearchText,
    windowValue,
    handleTextChange,
    searchText,
    isValidSearch,
    setIsValidSearch,
    // showToast,
    insets,
  };
};

export default useMultiSearchBottomSheetController;
