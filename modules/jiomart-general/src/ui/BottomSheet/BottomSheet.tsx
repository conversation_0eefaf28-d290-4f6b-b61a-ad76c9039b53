import React, { useEffect } from 'react';
import { BackHandler, Platform, StatusBar, TouchableWithoutFeedback } from 'react-native';
import { PanGestureHandler, ScrollView } from 'react-native-gesture-handler';
import Animated from 'react-native-reanimated';
import useBottomSheetViewController from './controller/useBottomSheetViewController';
import type { BottomSheetProps } from './types/BottomSheetType';

const BottomSheet = ({ children, ...rest }: BottomSheetProps) => {
  const {
    visible,
    contentHeight,
    isStatic,
    onBackDropClick,
    disabledBackDropClick,
    headerComponent,
    footerComponent,
    isStretchEnabled,
    enableScroll,
    backDropStyle,
    animatedStyle,
    dynamicHeightStyle,
    handleGesture,
    handleGestureEnd,
    calculateTop,
    styles,
    close,
    setContentHeight,
    statusBarRef,
  } = useBottomSheetViewController(rest);

  useEffect(() => {
    const onBackPress = () => {
      if (visible) {
        if (!disabledBackDropClick) {
          close(onBackDropClick);
        }
        return true;
      }
      return false;
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      onBackPress,
    );

    return () => backHandler.remove();
  }, [visible, close, onBackDropClick, disabledBackDropClick]);

  // Only render BottomSheet when visible
  if (!visible) {
    return null;
  }
  return (
    <>
      {Platform.OS === 'android' ? (
        <StatusBar
          ref={statusBarRef}
          barStyle="light-content"
          backgroundColor="rgba(0,0,0,0.95)"
        />
      ) : null}
      <TouchableWithoutFeedback
        onPress={() => {
          if (!disabledBackDropClick) {
            close(onBackDropClick);
          }
        }}>
        <Animated.View style={[styles.modalBackground, backDropStyle]} />
      </TouchableWithoutFeedback>
      <TouchableWithoutFeedback
        style={[
          styles.position,
          {
            top: calculateTop(contentHeight),
          },
        ]}>
        <PanGestureHandler
          onGestureEvent={handleGesture}
          onEnded={handleGestureEnd}>
          <Animated.View
            style={[
              styles.bottomSheetContainer,
              styles.position,
              animatedStyle,
              dynamicHeightStyle,
            ]}>
            {React.isValidElement(headerComponent)
              ? React.cloneElement(headerComponent)
              : null}
            {isStatic ? (
              React.isValidElement(children) ? (
                React.cloneElement(children, { ...rest, close })
              ) : null
            ) : (
              <ScrollView
                bounces={false}
                scrollEventThrottle={16}
                nestedScrollEnabled
                scrollEnabled={enableScroll}
                keyboardShouldPersistTaps="always"
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                onContentSizeChange={(w, h) => {
                  if (isStretchEnabled) {
                    setContentHeight(h);
                  } else {
                    if (contentHeight < h) {
                      setContentHeight(h);
                    }
                  }
                }}>
                {React.isValidElement(children)
                  ? React.cloneElement(children, { ...rest, close })
                  : null}
              </ScrollView>
            )}
            {React.isValidElement(footerComponent)
              ? React.cloneElement(footerComponent)
              : null}
          </Animated.View>
        </PanGestureHandler>
      </TouchableWithoutFeedback>
    </>
  );
};

export default BottomSheet;
