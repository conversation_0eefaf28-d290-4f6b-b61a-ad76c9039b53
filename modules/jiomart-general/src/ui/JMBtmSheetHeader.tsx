import React, {FC} from 'react';
import {StyleSheet, View, type StyleProp, type ViewStyle} from 'react-native';
import {JioTypography} from '@jio/rn_components/src/components/JioText/JioText.types';
import JioText from '@jio/rn_components/src/components/JioText/JioText';
import {JioIcon} from '@jio/rn_components';
import {
  IconColor,
  IconKind,
  IconSize,
} from '@jio/rn_components/src/index.types';
import type {IconKey} from '@jio/rn_components/src/utils/IconUtility';
import {rh} from '../../../jiomart-common/src/JMResponsive';
import {Pressable} from 'react-native-gesture-handler';

interface JMBtmSheetHeaderProps {
  style?: StyleProp<ViewStyle>;
  icon?: {
    ic: string;
    color?: string;
    size?: string;
    kind?: string;
  };
  hideClose?: boolean;
  showIcon?: boolean;
  title: string;
  onPress?: () => void;
  onLeftIconPress?: () => void;
}

const JMBtmSheetHeader: FC<JMBtmSheetHeaderProps> = ({
  style,
  icon,
  showIcon = false,
  title,
  hideClose,
  onPress,
  onLeftIconPress,
}) => {
  return (
    <View style={[styles.headerContainer, style]}>
      {showIcon ? (
        <Pressable onPress={onLeftIconPress}>
          <JioIcon
            ic={icon?.ic as IconKey}
            kind={icon?.kind as IconKind}
            size={icon?.size as IconSize}
            color={icon?.color as IconColor}
          />
        </Pressable>
      ) : null}
      <JioText
        appearance={JioTypography.HEADING_XXS}
        color={'black'}
        text={title?.replace(/\s+/g, ' ')}
      />
      {!hideClose ? (
        <Pressable
          onPress={onPress}
          style={styles.closeText}
          hitSlop={12}>
          <JioIcon
            ic={'IcClose'}
            color={IconColor.PRIMARY60}
            size={IconSize.MEDIUM}
          />
        </Pressable>
      ) : null}
    </View>
  );
};

export default JMBtmSheetHeader;

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: 24,
    paddingVertical: rh(4),
    marginTop: rh(16),
    marginBottom: rh(8),
    columnGap: 12,
  },
  closeText: {
    marginLeft: 'auto',
  },
});
