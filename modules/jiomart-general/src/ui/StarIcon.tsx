import React from 'react';
import {Svg, Defs, LinearGradient, Stop, Path} from 'react-native-svg';

const StarIcon = ({offset = '0%', size = 14}) => {
  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <Defs>
        <LinearGradient id="grad" x1="0" y1="1" x2="1" y2="1">
          <Stop offset={offset} stopColor="#FFD947" />
          <Stop offset={offset} stopColor="#E0E0E0" />
        </LinearGradient>
      </Defs>
      <Path
        d="M19.3697 7.60947C19.2557 7.27681 19.0563 6.98217 18.7904 6.75405C18.5246 6.52594 18.2017 6.38337 17.8504 6.32635L13.6155 5.68003L11.7164 1.59303C11.5645 1.26988 11.3176 0.994241 11.0137 0.794644C10.7099 0.604551 10.3586 0.5 9.99773 0.5C9.6369 0.5 9.28557 0.604551 8.98172 0.794644C8.67787 0.984737 8.43099 1.26037 8.27907 1.59303L6.37999 5.68003L2.10708 6.32635C1.76524 6.38337 1.43291 6.52594 1.16704 6.75405C0.901166 6.98217 0.701764 7.27681 0.587819 7.60947C0.48337 7.93263 0.473875 8.2843 0.549838 8.61696C0.625801 8.94963 0.796717 9.26328 1.0341 9.5104L4.15808 12.723L3.41744 17.2757C3.36047 17.6274 3.40794 17.9885 3.54088 18.3117C3.67381 18.6349 3.9017 18.92 4.19606 19.1291C4.50941 19.3667 4.89872 19.4998 5.29752 19.5093C5.62986 19.5093 5.9527 19.4332 6.24706 19.2717L10.0357 17.1806L13.8244 19.2717C14.1092 19.4332 14.4416 19.5188 14.7739 19.5093C15.1727 19.5093 15.5525 19.3857 15.8754 19.1576C16.1602 18.9485 16.3881 18.6729 16.5305 18.3402C16.6635 18.0171 16.711 17.6559 16.654 17.3042L15.9133 12.7515L19.0373 9.53891C19.2652 9.28229 19.4076 8.95913 19.4741 8.61696C19.5311 8.2748 19.4931 7.92312 19.3697 7.60947Z"
        fill="url(#grad)"
      />
    </Svg>
  );
};

export default StarIcon;
