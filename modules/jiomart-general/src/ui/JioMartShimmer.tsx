import React, {useMemo} from 'react';
import {StyleSheet, useWindowDimensions} from 'react-native';
import {JioShimmerProps, ShimmerKind} from '@jio/rn_components/src/index.types';
import Skeleton from './Skeleton';

const JioMartShimmer: React.FC<JioShimmerProps> = React.memo(
  ({width, height, kind = ShimmerKind.RECTANGLE, style}) => {
    const {width: windowWidth} = useWindowDimensions();

    const dimensions = useMemo(() => {
      const getNumericValue = (
        value: string | number | null | undefined,
        dimension: number,
      ): number | string => {
        if (value === null || value === undefined) return 'auto';
        if (typeof value === 'number') return value;
        if (typeof value === 'string' && value.includes('%')) {
          return value;
        }
        return Number(value) || 'auto';
      };

      const numericWidth = getNumericValue(width, windowWidth);
      const numericHeight = getNumericValue(height, windowWidth);

      return {width: numericWidth, height: numericHeight};
    }, [width, height, windowWidth]);

    const shimmerStyle = useMemo(() => {
      switch (kind) {
        case ShimmerKind.CIRCLE:
          return [
            style,
            {
              borderRadius:
                typeof dimensions.width === 'number'
                  ? dimensions.width / 2
                  : undefined,
            },
          ];
        case ShimmerKind.SQUARE:
          return [style];
        case ShimmerKind.RECTANGLE:
          return [style];
        case ShimmerKind.HEADING:
          return [style, styles.heading];
        case ShimmerKind.PARAGRAPH:
          return [style, styles.paragraph];
        default:
          return [style];
      }
    }, [kind, style, dimensions.width]);

    return (
      <Skeleton
        width={dimensions.width}
        height={
          dimensions.height ??
          (kind === ShimmerKind.HEADING
            ? 32
            : kind === ShimmerKind.PARAGRAPH
            ? 16
            : 160)
        }
        style={shimmerStyle}
      />
    );
  },
);

const styles = StyleSheet.create({
  heading: {
    borderRadius: 32,
  },
  paragraph: {
    borderRadius: 32,
  },
});

export default JioMartShimmer;
