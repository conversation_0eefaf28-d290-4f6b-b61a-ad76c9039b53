import {
  ActivityIndicator,
  StyleSheet,
  Pressable,
  View,
} from 'react-native';
import React from 'react';
import {JioIcon, JioText} from '@jio/rn_components';
import {useColor} from '@jio/rn_components/src/theme/color/useColor';
import {
  IconColor,
  IconSize,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import type {AddCtaViewProps} from './types/AddCtaModel';

const LargeAddCtaView = (props: AddCtaViewProps) => {
  const borderColor = useColor('primary_grey_40');
  const backgroundColor = useColor('primary_grey_20');
  const addBackgroundColor = useColor('primary_50');
  const {inverse, style, disabled, title} = props;
  const {
    isProductInCart,
    loader,
    hideAddButton,
    initiateAddToCartHandle,
    updateCartProductCount,
  } = useAddCtaViewController(props);
  const activityIndicatorColor = useColor('primary_60');
  const inverseActivityIndicatorColor = useColor('primary_inverse');
  return (
    <View style={style}>
      {!isProductInCart?.quantity && !hideAddButton ? (
        <Pressable
          style={[
            styles.buttonWrapper,
            inverse
              ? {borderWidth: 0, backgroundColor: addBackgroundColor}
              : {borderColor: borderColor},
            disabled ? {opacity: 0.65} : {},
          ]}
          onPress={() => {
            initiateAddToCartHandle();
          }}
          disabled={disabled}>
          {loader ? (
            <ActivityIndicator
              color={
                inverse ? inverseActivityIndicatorColor : activityIndicatorColor
              }
            />
          ) : (
            <JioText
              text={title ?? 'Add to Cart'}
              appearance={JioTypography.BUTTON}
              color={inverse ? 'primary_inverse' : 'primary_60'}
            />
          )}
        </Pressable>
      ) : (
        <View
          style={[
            styles.stepperWrapper,
            {
              backgroundColor: backgroundColor,
            },
          ]}>
          <Pressable
            style={[
              styles.stepperMinusIconButton,
              {
                borderColor: borderColor,
              },
            ]}
            onPress={() => {
              updateCartProductCount(false);
            }}>
            <JioIcon
              ic={'IcMinus'}
              size={IconSize.MEDIUM}
              color={IconColor.PRIMARY60}
            />
          </Pressable>
          {loader ? (
            <ActivityIndicator color={activityIndicatorColor} />
          ) : (
            <JioText
              text={`${isProductInCart?.quantity}`}
              appearance={JioTypography.BUTTON}
              color={'primary_60'}
            />
          )}
          <Pressable
            style={[
              (isProductInCart?.moq?.maximum ??
                isProductInCart?.moq?.minimum) === isProductInCart?.quantity
                ? {opacity: 0.65}
                : null,
              styles.stepperIconButton,
              {
                backgroundColor: addBackgroundColor,
              },
            ]}
            onPress={() => {
              updateCartProductCount(true);
            }}
            disabled={
              (isProductInCart?.moq?.maximum ??
                isProductInCart?.moq?.minimum) === isProductInCart?.quantity
            }>
            <JioIcon
              ic={'IcAdd'}
              size={IconSize.MEDIUM}
              color={IconColor.INVERSE}
            />
          </Pressable>
        </View>
      )}
    </View>
  );
};

export default LargeAddCtaView;

const styles = StyleSheet.create({
  buttonWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    paddingVertical: 12,
    width: rw(150),
    height: rh(48),
    borderRadius: 1000,
    flexDirection: 'row',
  },
  stepperIconButton: {
    padding: 12,
    alignSelf: 'flex-start',
    borderRadius: 100,
  },
  stepperWrapper: {
    width: rw(150),
    height: rh(48),
    borderRadius: 100,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  stepperMinusIconButton: {
    padding: 12,
    borderWidth: 1,
    alignSelf: 'flex-start',
    borderRadius: 100,
    backgroundColor: '#fff',
  },
});
