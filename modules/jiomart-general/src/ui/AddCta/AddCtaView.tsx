import React from 'react';
import {AddCtaViewProps} from './types/AddCtaModel';
import {JioIcon, JioText} from '@jio/rn_components';
import {useColor} from '@jio/rn_components/src/theme/color/useColor';
import {
  IconColor,
  IconSize,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import {ActivityIndicator, StyleSheet, Pressable, View} from 'react-native';
import useAddCta from './hooks/useAddCta';

const AddCtaView = (props: AddCtaViewProps) => {
  const buttonBorderColor = useColor('primary_grey_40');
  const activityIndicatorColor = useColor('primary_60');
  // console.log('AddCtaView---am--->', props);
  const {productCart, loader, handleAddToCart, handleRemoveFromCart} =
    useAddCta(props);
  return (
    <View
      style={[
        styles.container,
        props.style,
        props?.stretchButton ? styles.stretch : {},
      ]}>
      {!productCart?.quantity ? (
        <Pressable
          style={[
            styles.buttonWrapper,
            props?.stretchButton ? styles.buttonStretch : {},
            props?.disableDefaultAddToCartFunc ? props.button?.style : {},
            {borderColor: buttonBorderColor},
          ]}
          onPress={handleAddToCart}
          onLongPress={() => {}}>
          <>
            <JioText
              text={
                props.disableDefaultAddToCartFunc
                  ? props?.button?.text?.text
                  : 'Add'
              }
              appearance={
                props.disableDefaultAddToCartFunc
                  ? props?.button?.text?.appearance
                  : JioTypography.BUTTON
              }
              color={
                props.disableDefaultAddToCartFunc
                  ? props?.button?.text?.color
                  : 'primary_60'
              }
              // {...props.button?.text}
            />
            <JioIcon
              ic={
                props.disableDefaultAddToCartFunc
                  ? props?.button?.icon?.ic
                  : 'IcAdd'
              }
              size={
                props.disableDefaultAddToCartFunc
                  ? props?.button?.icon?.size
                  : IconSize.SMALL
              }
              color={
                props.disableDefaultAddToCartFunc
                  ? props?.button?.icon?.color
                  : IconColor.PRIMARY60
              }
            />
          </>
        </Pressable>
      ) : (
        <View
          style={[
            styles.buttonIconWrapper,
            props.styleForButtonIcon,
            props?.stretchButton ? styles.flexEnd : {},
            loader ? {columnGap: 7.5} : {},
          ]}>
          <Pressable
            style={[styles.buttonIcon, {borderColor: buttonBorderColor}]}
            onPress={handleRemoveFromCart}
            activeOpacity={0.9}
            onLongPress={() => {}}>
            <JioIcon
              ic={'IcMinus'}
              size={IconSize.SMALL}
              color={IconColor.PRIMARY60}
            />
          </Pressable>
          {loader ? (
            <ActivityIndicator
              color={activityIndicatorColor}
              style={{marginHorizontal: -2}}
            />
          ) : (
            <JioText
              text={`${productCart?.quantity ?? ''}`}
              appearance={JioTypography.BODY_XS}
              color={'primary_grey_80'}
            />
          )}
          <Pressable
            style={[
              styles.buttonIcon,
              {borderColor: buttonBorderColor},
              productCart &&
              (productCart?.moq?.max ?? productCart?.moq?.min) ===
                productCart?.quantity
                ? {opacity: 0.5}
                : null,
            ]}
            onPress={handleAddToCart}
            onLongPress={() => {}}
            activeOpacity={0.9}>
            <JioIcon
              ic={'IcAdd'}
              size={IconSize.SMALL}
              color={IconColor.PRIMARY60}
            />
          </Pressable>
        </View>
      )}
    </View>
  );
};

export default React.memo(AddCtaView);

const styles = StyleSheet.create({
  container: {
    alignSelf: 'flex-end',
    marginTop: 'auto',
  },
  buttonWrapper: {
    paddingVertical: 4,
    paddingHorizontal: 16,
    borderWidth: 1,
    alignSelf: 'flex-start',
    borderRadius: 100,
    flexDirection: 'row',
    alignItems: 'center',
    columnGap: 8,
  },
  buttonIconWrapper: {
    flexDirection: 'row',
    columnGap: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonIcon: {
    padding: 8,
    borderWidth: 1,
    alignSelf: 'flex-start',
    borderRadius: 100,
  },
  stretch: {alignSelf: 'stretch'},
  buttonStretch: {alignSelf: 'stretch', justifyContent: 'space-between'},
  flexEnd: {alignSelf: 'flex-end'},
});
