import {StyleProp, ViewStyle} from 'react-native';
import type {
  JioIconProps,
  JioTextProps,
} from '@jio/rn_components/src/index.types';

export interface AddCtaViewProps {
  inverse?: boolean;
  stretchButton?: boolean;
  style?: StyleProp<ViewStyle>;
  styleForButtonIcon?: StyleProp<ViewStyle>;
  request?: {
    uid: number;
    slug?: string;
    size: string;
    product_group_tag?: any;
    meta: any;
    sellerId: string;
    storeId?: string;
    minQty: number;
    maxQty: number;
  };
  disableDefaultAddToCartFunc?: boolean;
  title?: string;
  disabled?: boolean;
  onPress?: () => void;
  button?: {
    text?: JioTextProps;
    icon?: JioIconProps;
    style?: StyleProp<ViewStyle>;
  };
}
