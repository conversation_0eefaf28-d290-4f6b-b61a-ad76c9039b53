
import MicroScale from '../Animation/Micro/MicroScale';
import CustomMediaRendered, {Kind} from '../CustomMediaRendered';
import {getSvgUrlData} from '../../../../jiomart-common/src/utils/JMCommonFunctions';
import { Pressable } from 'react-native';
import useWishlistToggleViewController from '../../../../jiomart-search/src/controller/useWishlistToggleViewController';
import { WishlistToggleProps } from '../../../../jiomart-search/src/types/WishlistToggleType';

const WishlistToggleView = (props: WishlistToggleProps) => {
  const {actionWishlist, style, wishlistToggle} =
    useWishlistToggleViewController(props);
  return (
    <Pressable onPress={actionWishlist} style={style} hitSlop={8}>
      <MicroScale scale={1.5}>
        <CustomMediaRendered
          kind={Kind.SVG}
          mediaUrl={getSvgUrlData('CardHeart')}
          width={24}
          height={24}
          fill={wishlistToggle ? '#FC6770' : '#fff'}
          strokeWidth={wishlistToggle ? '0' : '1'}
        />
      </MicroScale>
    </Pressable>
  );
};

export default WishlistToggleView;
