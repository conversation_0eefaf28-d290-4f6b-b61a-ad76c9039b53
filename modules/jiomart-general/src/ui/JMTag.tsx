import {
  StyleSheet,
  View,
  ViewStyle,
  type StyleProp,
  type TextStyle,
} from 'react-native';
import React, {FC} from 'react';
import {JioText} from '@jio/rn_components';
import {JioColor, JioTypography} from '@jio/rn_components/src/index.types';

export interface JMTagProps {
  icon?: React.JSX.Element;
  color: string;
  textColor?: JioColor;
  text: string;
  style?: StyleProp<ViewStyle>;
  textstyle?: StyleProp<TextStyle>;
  maxLine?: number;
  minLines?: number;
  appearance?: JioTypography;
}

const JMTag: FC<JMTagProps> = ({
  color,
  textColor = 'sparkle_60',
  appearance = JioTypography.BODY_XXS_BOLD,
  text,
  style,
  icon,
  textstyle,
  maxLine,
  minLines
}) => {
  return (
    <View style={[styles.tagContainer, style, {backgroundColor: color}]}>
      {React.isValidElement(icon) && React.cloneElement(icon)}
      <JioText
        appearance={appearance}
        text={text}
        color={textColor}
        style={textstyle}
        maxLines={maxLine}
        minLines={minLines}
      />
    </View>
  );
};

export default JMTag;

const styles = StyleSheet.create({
  tagContainer: {
    backgroundColor: '#E5F7EE',
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
    flexDirection: 'row',
    columnGap: 4,
    alignItems: 'center',
  },
});
