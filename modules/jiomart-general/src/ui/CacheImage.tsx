import React, {useState, useEffect, useRef} from 'react';
import {Image, View, ActivityIndicator, type ImageProps} from 'react-native';
import {
  DocumentDirectoryPath,
  downloadFile,
  exists,
  unlink,
  readDir,
} from 'react-native-fs';
import {createHash} from 'crypto';

// In-memory cache for quick access
const memoryCache = new Map();
const MAX_MEMORY_CACHE_SIZE = 50; // Max items in memory

// Cache configuration
const CACHE_DIR = `${DocumentDirectoryPath}/ImageCache`;
const MAX_CACHE_SIZE = 100 * 1024 * 1024; // 100MB
const CACHE_EXPIRY = 7 * 24 * 60 * 60 * 1000; // 7 days

class ImageCacheManager {
  constructor() {
    this.initializeCache();
  }

  async initializeCache() {
    try {
      const dirExists = await exists(CACHE_DIR);
      if (!dirExists) {
        await require('react-native-fs').mkdir(CACHE_DIR);
      }
    } catch (error) {
      console.error('Failed to initialize cache directory:', error);
    }
  }

  // Generate cache key from URL
  getCacheKey(url: any) {
    return createHash('md5').update(url).digest('hex');
  }

  // Get cached image path
  getCachePath(url: string) {
    const key = this.getCacheKey(url);
    return `${CACHE_DIR}/${key}`;
  }

  // Check if image exists in memory cache
  getFromMemoryCache(url: string) {
    const cached = memoryCache.get(url);
    if (cached && Date.now() - cached.timestamp < CACHE_EXPIRY) {
      return cached.path;
    }
    return null;
  }

  // Add to memory cache
  addToMemoryCache(url: string, path: string) {
    // Remove oldest items if cache is full
    if (memoryCache.size >= MAX_MEMORY_CACHE_SIZE) {
      const oldestKey = memoryCache.keys().next().value;
      memoryCache.delete(oldestKey);
    }

    memoryCache.set(url, {
      path,
      timestamp: Date.now(),
    });
  }

  // Check if image exists in disk cache
  async getFromDiskCache(url: string) {
    try {
      const cachePath = this.getCachePath(url);
      const fileExists = await exists(cachePath);

      if (fileExists) {
        // Check file age
        const stats = await require('react-native-fs').stat(cachePath);
        const age = Date.now() - new Date(stats.mtime).getTime();

        if (age < CACHE_EXPIRY) {
          this.addToMemoryCache(url, cachePath);
          return cachePath;
        } else {
          // File expired, remove it
          await unlink(cachePath);
        }
      }
    } catch (error) {
      console.error('Error checking disk cache:', error);
    }
    return null;
  }

  // Download and cache image
  async downloadAndCache(url: string) {
    try {
      const cachePath = this.getCachePath(url);

      const result = await downloadFile({
        fromUrl: url,
        toFile: cachePath,
        discretionary: true,
        cacheable: false,
      }).promise;

      if (result.statusCode === 200) {
        this.addToMemoryCache(url, cachePath);
        this.cleanupCache(); // Clean old files periodically
        return cachePath;
      }
    } catch (error) {
      console.error('Error downloading image:', error);
    }
    return null;
  }

  // Clean up old cache files
  async cleanupCache() {
    try {
      const files = await readDir(CACHE_DIR);
      let totalSize = 0;
      const fileStats = [];

      // Get file stats
      for (const file of files) {
        const stats = await require('react-native-fs').stat(file.path);
        totalSize += stats.size;
        fileStats.push({
          path: file.path,
          size: stats.size,
          mtime: new Date(stats.mtime).getTime(),
        });
      }

      // Remove expired files
      const now = Date.now();
      for (const file of fileStats) {
        if (now - file.mtime > CACHE_EXPIRY) {
          await unlink(file.path);
          totalSize -= file.size;
        }
      }

      // Remove oldest files if cache size exceeds limit
      if (totalSize > MAX_CACHE_SIZE) {
        const sortedFiles = fileStats
          .filter(f => now - f.mtime <= CACHE_EXPIRY)
          .sort((a, b) => a.mtime - b.mtime);

        for (const file of sortedFiles) {
          if (totalSize <= MAX_CACHE_SIZE) break;
          await unlink(file.path);
          totalSize -= file.size;
        }
      }
    } catch (error) {
      console.error('Error cleaning cache:', error);
    }
  }

  // Clear all cache
  async clearCache() {
    try {
      memoryCache.clear();
      const files = await readDir(CACHE_DIR);
      for (const file of files) {
        await unlink(file.path);
      }
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }

  // Get cached image or download
  async getCachedImage(url: string) {
    // Check memory cache first
    const memoryPath = this.getFromMemoryCache(url);
    if (memoryPath) {
      return memoryPath;
    }

    // Check disk cache
    const diskPath = await this.getFromDiskCache(url);
    if (diskPath) {
      return diskPath;
    }

    // Download and cache
    return await this.downloadAndCache(url);
  }
}

// Singleton instance
const cacheManager = new ImageCacheManager();

// Custom hook for image caching
const useImageCache = (url: string) => {
  const [cachedPath, setCachedPath] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const isMounted = useRef(true);

  useEffect(() => {
    if (!url) {
      setLoading(false);
      return;
    }

    const loadImage = async () => {
      try {
        setLoading(true);
        setError(null);

        const path = await cacheManager.getCachedImage(url);

        if (isMounted.current) {
          if (path) {
            setCachedPath(`file://${path}`);
          } else {
            setError('Failed to load image');
          }
          setLoading(false);
        }
      } catch (err: any) {
        if (isMounted.current) {
          setError(err?.message || 'Failed to load image');
          setLoading(false);
        }
      }
    };

    loadImage();

    return () => {
      isMounted.current = false;
    };
  }, [url]);

  return {cachedPath, loading, error};
};

export interface CachedImageProps extends ImageProps {
  source: {uri: string};
  style: any;
  placeholder?: any;
  loadingComponent?: any;
  errorComponent?: any;
}

// CachedImage component
const CachedImage = ({
  source,
  style,
  placeholder,
  loadingComponent,
  errorComponent,
  ...props
}: CachedImageProps) => {
  const {cachedPath, loading, error} = useImageCache(source?.uri);

  if (loading) {
    return (
      loadingComponent || (
        <View style={[style, {justifyContent: 'center', alignItems: 'center'}]}>
          <ActivityIndicator size="small" />
        </View>
      )
    );
  }

  if (error) {
    return (
      errorComponent || (
        <View style={[style, {justifyContent: 'center', alignItems: 'center'}]}>
          {placeholder && (
            <Image source={placeholder} style={style} {...props} />
          )}
        </View>
      )
    );
  }

  return <Image source={{uri: cachedPath ?? ''}} style={style} {...props} />;
};

// Export cache manager for manual operations
export {cacheManager, CachedImage, useImageCache};
export default CachedImage;
