import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';
import { RQKey } from '../../../jiomart-common/src/JMConstants';
import { JMDatabaseManager } from '../../../jiomart-networkmanager/src/db/JMDatabaseManager';

const useUserProfile = () => {
  const queryClient = useQueryClient();

  const saveUserData = (userData: any) => {
    queryClient.setQueryData([RQKey.USER_PROFILE], userData);
  };

  const { data: userData } = useQuery({
    queryKey: [RQKey.USER_PROFILE],
    queryFn: () => undefined, // Prevents API call
    enabled: false, // Prevents automatic fetch
    staleTime: Infinity, // Keeps data always fresh
    initialData: () => null, // Optional default
    retry: 0,
  });

  useEffect(() => {
    JMDatabaseManager.user.getUserDetails()?.then(value => {
      if (value) {
        saveUserData(JSON.parse(value));
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return { saveUserData, userData };
};

export default useUserProfile;
