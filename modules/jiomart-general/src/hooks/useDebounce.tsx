import {useRef} from 'react';

/**
 * React Hook version of debounce.
 */
const useDebounce = <T extends (...args: any[]) => void>(
  callback: T,
  delay: number = 500,
): ((...args: Parameters<T>) => void) => {
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const debounce = (...args: Parameters<T>): void => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  };

  return debounce;
};

/**
 * Standalone debounce function for use in non-React contexts.
 */
const debounce = <T extends (...args: any[]) => void>(
  callback: T,
  delay: number = 500,
): ((...args: Parameters<T>) => void) => {
  let timeout: ReturnType<typeof setTimeout> | null = null;

  return (...args: Parameters<T>): void => {
    if (timeout) {
      clearTimeout(timeout);
    }
    timeout = setTimeout(() => {
      callback(...args);
    }, delay);
  };
};

export default useDebounce;
export {debounce};
