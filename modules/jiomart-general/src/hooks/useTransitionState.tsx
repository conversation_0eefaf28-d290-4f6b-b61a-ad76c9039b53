import {useNavigation} from '@react-navigation/native';
import {useEffect, useState} from 'react';

const useTransitionState = () => {
  const [isTransitionComplete, setIsTransitionComplete] = useState(false);
  const navigation = useNavigation();

  useEffect(() => {
    const unsubscribe = navigation.addListener('transitionEnd', (e) => {
      setIsTransitionComplete(true);
    });

    return unsubscribe;
  }, [navigation]);

  return isTransitionComplete;
};
export default useTransitionState;
