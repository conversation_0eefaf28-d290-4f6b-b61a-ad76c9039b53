import {
  AnalyticsParams,
  EventTriggerChannel,
} from '../../../jiomart-common/src/AnalyticsParams';
import {GAModel} from '../../../jiomart-common/src/JMNavGraphUtil';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {
  JHExceptionLogger,
  JMLogger,
} from '../../../jiomart-common/src/utils/JMLogger';
// import analytics from '@react-native-firebase/analytics';

/**
 * Log an analytics event
 * @param name 40 character max alphanumeric and '_' only, starts with alpha
 * @param props keys 40 character max w/name content rules, values 100 character max
 */
const logAnalyticsEvent = (
  eventName: string,
  paramMap: Map<AnalyticsParams, string>,
) => {
  try {
    if (paramMap === null || paramMap === undefined) return;
    JMLogger.log(
      'logAnalyticsEvent eventName' +
        eventName +
        'paramMap ' +
        JSON.stringify(paramMap),
    );
    switch (JMSharedViewModel.Instance.eventTriggerChannel) {
      case EventTriggerChannel.FIREBASE: {
        (async () => {
          // await analytics().logEvent(
          //   categoryStringRefactor(eventName),
          //   paramMap,
          // );
        })();
        break;
      }
      case EventTriggerChannel.SENSE: {
        break;
      }
      case EventTriggerChannel.FIREBASE_AND_SENSE: {
        break;
      }
      default:
        break;
    }
  } catch (e) {
    JHExceptionLogger.log('Unable to tag analytics event:', e);
  }
};

const logScreenEvent = (screenName: string) => {
  try {
    JMLogger.log('logScreenEvent ' + screenName);
    if (
      screenName === null ||
      screenName === undefined ||
      screenName.length === 0
    )
      return;
    (async () => {
      await analytics().logScreenView({
        screen_name: screenName,
        screen_class: screenName,
      });
    })();
  } catch (e) {
    JHExceptionLogger.log('Unable to tag analytics screen event:', e);
  }
};

const logAnalyticsEventWithGAModel = (gaModel?: GAModel) => {
  try {
    if (gaModel === null || gaModel === undefined) return;
    JMLogger.log(
      'logAnalyticsEventWithGAModel gaModel ' + JSON.stringify(gaModel),
    );
    switch (JMSharedViewModel.Instance.eventTriggerChannel) {
      case EventTriggerChannel.FIREBASE: {
        logAnalyticsEventOnGoogle(gaModel);
        break;
      }
      case EventTriggerChannel.SENSE: {
        break;
      }
      case EventTriggerChannel.FIREBASE_AND_SENSE: {
        break;
      }
      default:
        break;
    }
  } catch (e) {
    JHExceptionLogger.log('Unable to tag analytics event:', e);
  }
};

const logAnalyticsEventOnGoogle = (gaModel: GAModel) => {
  try {
    JMLogger.log('logAnalyticsEventOnGoogle ' + gaModel);
    let eventMap = new Map<string, string>([]);
    eventMap.set(AnalyticsParams.ACTION, gaModel.action);
    eventMap.set(AnalyticsParams.LABEL, gaModel.label);
    eventMap.set(AnalyticsParams.GENERAL_DATA, gaModel.commonCustomDimesion);
    (async () => {
      await analytics().logEvent(
        categoryStringRefactor(gaModel.category),
        eventMap,
      );
    })();
  } catch (e) {
    JHExceptionLogger.log('Unable to tag analytics event:', e);
  }
};

function categoryStringRefactor(inputString: string | undefined): string {
  if (inputString === null || inputString === undefined) return '';
  let newString: string;
  if (inputString.includes(' ')) {
    newString = inputString.replace(/ /g, '_');
  } else {
    newString = inputString;
  }
  return newString;
}

/**
 * Log user id when user logs in
 * @param id
 */
const logUserId = async (id: string) => {
  try {
    await analytics().setUserId(id);
    JMLogger.log('logUserId:' + id);
  } catch (e) {
    JHExceptionLogger.log('Unable to tag analytics user id event:', e);
  }
};

/**
 * Set user properties
 * @param props keys 24 char max alphanumeric and '_' only starts alpha, values 26 char max
 */
const logUserProperties = async (props: {[key: string]: string | null}) => {
  try {
    await analytics().setUserProperties(props);
    console.log('params', props);
  } catch (e) {
    console.log('Unable to tag analytics user properties event:', e);
  }
};

/**
 * Clears all analytics data for this instance
 * from the device and resets the app instance ID
 */
const logSignOut = async () => {
  try {
    await analytics().resetAnalyticsData();
  } catch (e) {
    console.log(
      'Unable to tag analytics signout reset anlytics data event:',
      e,
    );
  }
};

export {logAnalyticsEvent, logAnalyticsEventWithGAModel, logScreenEvent};
