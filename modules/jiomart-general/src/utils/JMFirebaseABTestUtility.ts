import remoteConfig from '@react-native-firebase/remote-config';
import { JMLogger } from '../../../jiomart-common/src/utils/JMLogger';
//   await JMFirebaseABTestUtility.init();
//   const value = JMFirebaseABTestUtility.getParam('experiment_key');

class JMFirebaseABTestUtility {
  // Local map to store AB test params
  private static abParams: Record<string, any> = {};
  private static initialized = false;

  // Initialize Remote Config and fetch params
  static async init(): Promise<void> {
    JMLogger.log("JMFirebaseABTestUtility init called "+(this.initialized))
    if (this.initialized) return;
    try {
      JMLogger.log("JMFirebaseABTestUtility: setting config settings...");
      await remoteConfig().setConfigSettings({
        minimumFetchIntervalMillis: 3600000, // 1 hour
      });
      await remoteConfig().setDefaults({});
      await this.fetchAndActivate();
      this.initialized = true;
    } catch (error) {
      JMLogger.log("JMFirebaseABTestUtility init error: " + JSON.stringify(error));
    }
  }

  // Fetch and activate remote config, then store all params in abParams
  private static async fetchAndActivate(): Promise<void> {
    try {
      await remoteConfig().fetchAndActivate();
      const allKeys = remoteConfig().getAll();
      JMLogger.log("JMFirebaseABTestUtility fetchAndActivate allKeys "+JSON.stringify(allKeys))
      Object.keys(allKeys).forEach(key => {
        this.abParams[key] = allKeys[key]?.asString?.() ?? allKeys[key];
      });
    } catch (error) {
      JMLogger.log("JMFirebaseABTestUtility fetchAndActivate error "+JSON.stringify(error))
    }
  }

  // Get a parameter value by key
  static getParam<T = string>(key: string, defaultValue?: T): T | undefined {
    return (this.abParams[key] as T) ?? defaultValue;
  }

  // Optionally, force refresh params
  static async refresh(): Promise<void> {
    await this.fetchAndActivate();
  }
}

export default JMFirebaseABTestUtility; 