import { KeyboardAvoidingView, Platform } from "react-native";
import { KeyboardProps } from "../../../jiomart-common/src/JMScreenSlot.types";
import useCommonController from "./JMCommonController";

export const MoveKeyboardUpWithButton: React.FC<KeyboardProps> = ({
    children,
    nativeResize,
  }) => {
    if (nativeResize) {
      const {keyboardHeight} = useCommonController(true);
    }
    return (
      <KeyboardAvoidingView
        style={{flex: 1}}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        {children}
      </KeyboardAvoidingView>
    );
  };
  
  export const MoveKeyboardUpWithButtonInMultiForm: React.FC<KeyboardProps> = ({
    children,
    nativeResize,
  }) => {
    if (nativeResize) {
      const {keyboardHeight} = useCommonController(true);
    }
    return (
      <KeyboardAvoidingView
        style={{flex: 1}}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
        {children}
      </KeyboardAvoidingView>
    );
  };

  export const MoveKeyboardUpWithoutButton: React.FC<KeyboardProps> = ({
    children,
    nativeResize,
  iosInputFieldVisible,
  }) => {
    if (nativeResize) {
      const {keyboardHeight} = useCommonController(true);
    }
    if (Platform.OS === 'android') {
      return (
        <MoveKeyboardUpWithButton nativeResize={nativeResize}>
          {children}
        </MoveKeyboardUpWithButton>
      );
    } else {
      return (
        <KeyboardAvoidingView
          style={{flex: 1}}
          enabled={iosInputFieldVisible?true:false}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
          {children}
        </KeyboardAvoidingView>
      );
    }
  };