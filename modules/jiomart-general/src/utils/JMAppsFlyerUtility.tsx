import {JMLogger} from '../../../jiomart-common/src/utils/JMLogger';
import appsFlyer from 'react-native-appsflyer';
import {useGlobalState} from '../context/JMGlobalStateProvider';
import {isNullOrUndefinedOrEmpty} from '../../../jiomart-common/src/JMObjectUtility';
import { getAppsFlyerAppId, getAppsFlyerDevKey } from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';

const JMAppsFlyerUtility = () => {
  const {setDeeplinkData} = useGlobalState();
  const handleAppsFlyerDeeplink = async (deeplink: string) => {
    JMLogger.log(
      'handleAppsFlyerDeeplink ',
      'handleAppsFlyerDeeplink deeplink' + deeplink,
    );
    const deeplinkData = {
      mUri: deeplink,
      payload: '',
    };
    setDeeplinkData(deeplinkData);
  };
  const onDeepLinkCanceller = async () => {
    appsFlyer.onDeepLink(res => {
      JMLogger.log('appsFlyer onDeepLinkCanceller res' + JSON.stringify(res));
      if (res?.deepLinkStatus !== 'NOT_FOUND') {
        const deeplink = res?.data.deep_link_value;
        //   const mediaSrc = res?.data.media_source;
        if (!isNullOrUndefinedOrEmpty(deeplink)) {
          handleAppsFlyerDeeplink(deeplink);
        }
      }
    });
  };

  const initialiseAppsFlyer = () => {
    try {
      JMLogger.log('initialiseAppsFlyer', 'initialiseAppsFlyer');
      onDeepLinkCanceller();
      appsFlyer.initSdk(
        {
          devKey: getAppsFlyerDevKey(),
          isDebug: true,
          appId: getAppsFlyerAppId(),
          onInstallConversionDataListener: true,
          onDeepLinkListener: true,
        },
        result => {
          console.log(result);
        },
        error => {
          console.error(error);
        },
      );
    } catch (error) {
      JMLogger.log('appsFlyer initialisation error ' + error);
    }
  };
  const appsFlyerLogEvent = (eventName: string, eventValues: any) => {
    try {
      (async () => {
        appsFlyer.logEvent(
          eventName,
          eventValues,
          res => {
            JMLogger.log('appsFlyer event success ' + res);
          },
          err => {
            JMLogger.log('appsFlyer event error ' + err);
          },
        );
      })();
    } catch (error) {
      JMLogger.log('appsFlyer event error-- ' + error);
    }
  };

  return {
    initialiseAppsFlyer,
    appsFlyerLogEvent,
  };
};

export default JMAppsFlyerUtility;
