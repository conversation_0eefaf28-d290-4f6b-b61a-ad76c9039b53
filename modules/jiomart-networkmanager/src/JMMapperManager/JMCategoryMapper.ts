import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import {getBaseURL} from '../JMEnvironmentConfig';
import type {JMCategoriesModel} from '../models/Categories/JMCategoriesModel';

class JMCategoryMapper {
  static mapToUnifiedCategoriesResponse = (data: any): JMCategoriesModel[] => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return data || [];
      case AppSourceType.JM_BAU:
        return (
          data.data.map((category: any) => ({
            slug: category.url_path,
            name: category.name,
            banners: {
              portrait: {
                url: `${getBaseURL()}/` + category.thumbnail_image_path,
              },
            },
            department: [],
            childs: (category.sub_categories || []).map((subCat: any) => ({
              slug: subCat.url_path,
              name: subCat.name,
              logo: {
                url: `${getBaseURL()}/` + subCat.thumbnail_image_path,
              },
              department: [],
              childs: (subCat.sub_categories || []).map((subSubCat: any) => ({
                slug: subSubCat.url_path,
                name: subSubCat.name,
                logo: {
                  url: `${getBaseURL()}/` + subSubCat.thumbnail_image_path,
                },
                department: [],
              })),
            })),
          })) || []
        );
      default:
        return [];
    }
  };
}

export default JMCategoryMapper;
