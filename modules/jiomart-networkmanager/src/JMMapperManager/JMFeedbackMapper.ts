import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';

class JMFeedbackMapper {
  static mapToUnifiedUploadImageResponse = (data: any): any => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return this.generateUploadImageResponseFromJcp(data);
      case AppSourceType.JM_BAU:
        return this.generateUploadImageResponseFromBau(data);
      default:
        return this.generateUploadImageResponseFromBau(data);
    }
  };

  private static generateUploadImageResponseFromJcp = (data: any): any => {
    return data;
  };

  private static generateUploadImageResponseFromBau = (data: any): any => {
    return data;
  };

  static mapToUnifiedGenerateSignedUrlResponse = (data: any): any => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return this.generateGenerateSignedUrlResponseFromJcp(data);
      case AppSourceType.JM_BAU:
        return this.generateGenerateSignedUrlResponseFromBau(data);
      default:
        return this.generateGenerateSignedUrlResponseFromBau(data);
    }
  };

  private static generateGenerateSignedUrlResponseFromJcp = (data: any): any => {
    return data;
  };

  private static generateGenerateSignedUrlResponseFromBau = (data: any): any => {
    return data;
  };

  static mapToUnifiedSubmitReviewResponse = (data: any): any => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return this.generateSubmitReviewResponseFromJcp(data);
      case AppSourceType.JM_BAU:
        return this.generateSubmitReviewResponseFromBau(data);
      default:
        return this.generateSubmitReviewResponseFromBau(data);
    }
  };

  private static generateSubmitReviewResponseFromJcp = (data: any): any => {
    return data;
  };

  private static generateSubmitReviewResponseFromBau = (data: any): any => {
    return data;
  };

  static mapToUnifiedUpdateReviewResponse = (data: any): any => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return this.generateUpdateReviewResponseFromJcp(data);
      case AppSourceType.JM_BAU:
        return this.generateUpdateReviewResponseFromBau(data);
      default:
        return this.generateUpdateReviewResponseFromBau(data);
    }
  };

  private static generateUpdateReviewResponseFromJcp = (data: any): any => {
    return data;
  };

  private static generateUpdateReviewResponseFromBau = (data: any): any => {
    return data;
  };

  static mapToUnifiedFeedbackConfigResponse = (data: any): any => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return this.generateFeedbackConfigResponseFromJcp(data);
      case AppSourceType.JM_BAU:
        return this.generateFeedbackConfigResponseFromBau(data);
      default:
        return this.generateFeedbackConfigResponseFromBau(data);
    }
  };

  private static generateFeedbackConfigResponseFromJcp = (data: any): any => {
    return data;
  };

  private static generateFeedbackConfigResponseFromBau = (data: any): any => {
    return data;
  };

  static mapToUnifiedReviewListResponse = (data: any): any => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return this.generateReviewListResponseFromJcp(data);
      case AppSourceType.JM_BAU:
        return this.generateReviewListResponseFromBau(data);
      default:
        return this.generateReviewListResponseFromBau(data);
    }
  };

  private static generateReviewListResponseFromJcp = (data: any): any => {
    return data;
  };

  private static generateReviewListResponseFromBau = (data: any): any => {
    return data;
  };
}

export default JMFeedbackMapper;
