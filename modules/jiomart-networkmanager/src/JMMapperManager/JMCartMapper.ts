import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import type {JMCartModel} from '../models/Cart/JMCartModel';

class JMCartMapper {
  static mapToUnifiedCartResponse = (data: any): JMCartModel => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return this.generateCartResponseFromJcp(data);
      case AppSourceType.JM_BAU:
        return this.generateCartResponseFromBau(data);
      default:
        return this.generateCartResponseFromBau(data);
    }
  };

  private static generateCartResponseFromJcp = (data: any): any => {
    let cart: JMCartModel = {
      cart_id: data?.cart_id,
      success: data?.success,
      message: data?.message,
      items: data?.items?.map((item: any) => ({
        uid: item?.product?.uid,
        quantity: item?.quantity,
        moq: {
          min: item?.moq?.minimum ?? 1,
          max: item?.moq?.maximum,
          increment: item?.moq?.increment_unit ?? 1,
        },
        parent_item_identifiers: item?.parent_item_identifiers,
        identifiers: item?.identifiers,
        article_id: item?.article?.uid,
      })),
      cartCount:
        data?.items?.filter(
          (isParent: any) =>
            isParent?.parent_item_identifiers?.identifier === null,
        )?.length ?? 0,
    };
    return cart;
  };
  private static generateCartResponseFromBau = (data: any): any => {
    console.log('generateCartResponseFromBau', JSON.stringify(data));
    let cart: JMCartModel = {
      cart_id: data?.result?.cart?.id,
      success: data?.status,
      message: data?.message,
      items: data?.result?.cart?.lines.map((item: any) => ({
        uid: item?.product_code,
        quantity: item?.qty,
        moq: {
          min: item?.min_qty_in_order ?? 1,
          max: item?.max_qty_in_order,
          increment: item?.increment_unit ?? 1,
        },
      })),
      cartCount: data?.result?.cart?.lines?.length ?? 0,
    };
    return cart;
  };

  static mapToUnifiedAddToCart = (data: any) => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return data;
      case AppSourceType.JM_BAU:
        return data;
      default:
        return data;
    }
  };

  static mapToUnifiedRemoveFromCart = (data: any) => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return data;
      case AppSourceType.JM_BAU:
        return data;
      default:
        return data;
    }
  };
}

export default JMCartMapper;
