import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';

class JMOrderMapper {
  static mapToUnifiedRefundDetailsResponse = (data: any): any => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return this.generateRefundDetailsResponseFromJcp(data);
      case AppSourceType.JM_BAU:
        return this.generateRefundDetailsResponseFromBau(data);
      default:
        return this.generateRefundDetailsResponseFromBau(data);
    }
  };

  private static generateRefundDetailsResponseFromJcp = (data: any): any => {
    return data;
  };

  private static generateRefundDetailsResponseFromBau = (data: any): any => {
    return data;
  };

  static mapToUnifiedOrderResponse = (data: any): any => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return this.generateOrderResponseFromJcp(data);
      case AppSourceType.JM_BAU:
        return this.generateOrderResponseFromBau(data);
      default:
        return this.generateOrderResponseFromBau(data);
    }
  };

  private static generateOrderResponseFromJcp = (data: any): any => {
    return data;
  };

  private static generateOrderResponseFromBau = (data: any): any => {
    return data;
  };

  static mapToUnifiedRefundResponse = (data: any): any => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return this.generateRefundResponseFromJcp(data);
      case AppSourceType.JM_BAU:
        return this.generateRefundResponseFromBau(data);
      default:
        return this.generateRefundResponseFromBau(data);
    }
  };

  private static generateRefundResponseFromJcp = (data: any): any => {
    return data;
  };

  private static generateRefundResponseFromBau = (data: any): any => {
    return data;
  };
}

export default JMOrderMapper;
