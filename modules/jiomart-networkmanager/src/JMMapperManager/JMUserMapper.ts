import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';

class JMUserMapper {
  static mapToUnifiedUserDetailsResponse = (data: any) => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        const jcpData = data?.user;
        return {
          emails: [
            {
              email: jcpData.emails?.[0].email,
              verified: jcpData.emails?.[0].verified,
            },
          ],
          gender: jcpData.gender,
          first_name: jcpData.first_name,
          last_name: jcpData.last_name,
          phone_numbers: [
            {
              phone: jcpData?.phone_numbers?.[0]?.phone,
              verified: jcpData?.phone_numbers?.[0]?.verified,
            },
          ],
          full_name: jcpData.first_name + ' ' + jcpData.last_name,
        };
      case AppSourceType.JM_BAU:
        const bauData = data?.result?.your_details;
        return {
          emails: [
            {
              email: bauData.email,
              verified: bauData.email_verified,
            },
          ],
          gender: bauData.gender,
          first_name: bauData.firstname,
          last_name: bauData.lastname,
          preferred_billing_address: bauData.preferred_billing_address,
          preferred_shipping_address: bauData.preferred_shipping_address,
          phone_numbers: [
            {
              phone: bauData?.mobile_no,
              verified: bauData?.mobile_no_verified,
            },
          ],
          full_name: bauData.firstname + ' ' + bauData.lastname,
        };
      default:
        return [];
    }
  };
}

export default JMUserMapper;
