abstract class JMBaseHomeNetworkController {
  protected abstract checkServiceability(queryParams: {}): Promise<any>;
  protected abstract getMcatInventory(pincode:string): Promise<any>;
  protected abstract getSingleTopDeal(queryParams: {},customHeaders:{}): Promise<any>;
  protected abstract fetchProductDetailsFromAlgolia(bodyParams: any): Promise<any>;
}

export default JMBaseHomeNetworkController;
