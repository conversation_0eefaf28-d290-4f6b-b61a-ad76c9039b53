abstract class JMBaseAddressNetworkController {
  protected abstract fetchAddress(): Promise<any>;
  protected abstract fetchPincodeCity(pincode: string): Promise<any>;
  protected abstract defaultAddress(id: string, request: any): Promise<any>;
  protected abstract removeAddress(id: string): Promise<any>;
  protected abstract insertAddress(body: any): Promise<any>;
  protected abstract editAddress(id: string, request: any): Promise<any>;
}

export default JMBaseAddressNetworkController;
