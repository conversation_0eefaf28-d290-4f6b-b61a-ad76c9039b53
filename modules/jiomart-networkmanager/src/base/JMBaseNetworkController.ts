import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';

export interface JMBaseNetworkControllerOptions {
  appSource: AppSourceType;
}

abstract class JMBaseNetworkController {
  protected options?: JMBaseNetworkControllerOptions;

  constructor(options?: JMBaseNetworkControllerOptions) {
    this.options = {
      appSource: JMSharedViewModel.Instance.appSource,
      ...options,
    };
  }
}
export default JMBaseNetworkController;
