abstract class JMBaseServiceablityNetworkController {
  
  /**
   * Step 1: MStar Pin Info API
   * Retrieves pincode metadata and validation information
   * 
   * @param pincode - Target pincode for validation
   * @returns Promise with pincode metadata
   */
  protected abstract getPinMetadataInfo(pincode: string): Promise<any>;

  /**
   * Step 2: HCAT QC Config API
   * Fetches Quick Commerce configuration for the given pincode
   * Returns QC availability, suspect status, and polygon flags
   * 
   * @param pincode - Target pincode
   * @returns Promise with QC configuration object
   */
  protected abstract getHCATQCConfig(pincode: string): Promise<any>;

  /**
   * Step 3: Polygon/Jio-net API
   * Retrieves store details and polygon information for serviceability
   * Provides store_code, polygon_id, and serviceability data
   * 
   * @param lat - Latitude coordinate
   * @param lng - Longitude coordinate
   * @returns Promise with store and polygon details
   */
  protected abstract getPolygonStoreDetails(queryParams:{}): Promise<any>;

  /**
   * Step 4: MStar Region API
   * Fetches region code information for the location
   * 
   * @param locationData - Location details from previous steps
   * @returns Promise with region code information
   */
  protected abstract getMStarRegionCode(queryParams: {}): Promise<any>;

  /**
   * Step 5: Fynd Promise API
   * Calculates delivery promises and timing information
   * Combines data from previous steps to determine delivery windows
   * 
   * @param promiseParams - Parameters including store, pincode, coordinates, QC flags
   * @returns Promise with delivery promise details
   */
  protected abstract getFyndPromiseInfo(promiseParams: {}): Promise<any>;

  /**
   * Step 6: Set Delivery API
   * Configures distance settings for MStar systems
   * Sets delivery distance parameters based on promise calculations
   * 
   * @param distanceData - Distance and delivery configuration data
   * @returns Promise with delivery configuration status
   */
  protected abstract setDeliveryDistance(params: {}): Promise<any>;


  /**
   * This is a wrapper api for skipping step 4,5,6. 
   * This wrapper api calles these api internally and return us the merged response. 
   * So we can replace step 4,5,6 with this one api.
   * 
   */
  protected abstract getFyndPromiseInfoWrapper(params: {},headers:{}): Promise<any>;
}

export default JMBaseServiceablityNetworkController;
