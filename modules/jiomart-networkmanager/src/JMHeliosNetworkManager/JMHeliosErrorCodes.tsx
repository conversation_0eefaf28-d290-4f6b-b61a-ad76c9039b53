export enum JMHeliosErrorCodes {
  TOKEN_EXPIRED = 401,
  FORBIDDEN = 403,
  TOKEN_MISMATCH = 412,
  BAD_REQUEST = 400,
  UNPROCESSABLE_ENTITY = 422,
  FORCE_LOGOUT = 410,
  SERVER_ERROR = 500,
  NETWORK_ERROR = 'NETWORK_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  PARSE_ERROR = 'PARSE_ERROR',
  DATA_ERROR = 'DATA_ERROR',
  TOKEN_LIMIT_REACHED = 'TOKEN_LIMIT_REACHED',
  TOKEN_NOT_AVAILABLE = 'TOKEN_NOT_AVAILABLE',
}

export const JMHeliosErrorMessages = {
  TOKEN_EXPIRED: 'Token expired access. Please try again.',
  TOKEN_MISMATCH: 'Token mismatch. Please request a new token.',
  BAD_REQUEST: 'Invalid request. Please check your data.',
  FORCE_LOGOUT: 'Session expired. Please log in again.',
  SERVER_ERROR: 'Server error. Please try again later.',
  NETWORK_ERROR: 'No internet connection. Please check your network.',
  UNKNOWN_ERROR: 'An unknown error occurred. Please try again later.',
  PARSE_ERROR: 'Parse error. Please check response type and generic type',
  DATA_ERROR: 'Invalid data',
  INVALID_KEY: 'Invalid key provided',
  TOKEN_LIMIT_REACHED: 'Failed to get token after max retries',
  AUTHENTICATION_ERROR: 'Authentication Failed',
};

export interface JMHeliosError {
  code: JMHeliosErrorCodes;
  message: string;
}
