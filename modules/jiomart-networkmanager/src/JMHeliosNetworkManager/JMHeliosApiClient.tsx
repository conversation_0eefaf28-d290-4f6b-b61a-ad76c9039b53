import networkService from '../../../jiomart-common/src/JMNetworkConnectionUtility';
import {JHHeliosTokenService} from './JMHeliosTokenService';
import axios, {AxiosInstance, AxiosResponse} from 'axios';
import {
  JHHeliosLocalStorageConstants,
  JHHeliosNetworkRequestConstants,
} from './JMHeliosConstant';

import JHHeliosRequestHelper from './JMHeliosRequestHelper';
import {handleHeliosApiError} from './JMHeliosErrorHandler';
import {JHHeliosApiLogger} from './JMHeliosApiLogger';
import {isNullOrUndefinedOrEmpty} from '../../../jiomart-common/src/JMObjectUtility';
import {JHHeliosStorageService} from './JMHeliosStorageService';
import useDashboardApiNetworkState from '../JMNetworkController/JHDashboardNetworkController';
import {JMLogger} from '../../../jiomart-common/src/utils/JMLogger';
import {
  JMHeliosError,
  JMHeliosErrorCodes,
  JMHeliosErrorMessages,
} from './JMHeliosErrorCodes';

export class JHHeliosApiClient {
  private _instance: AxiosInstance;
  private tokenService: JHHeliosTokenService;

  constructor() {
    this._instance = axios.create();
    this.tokenService = new JHHeliosTokenService();
    this.setupInterceptors();
  }

  private setupInterceptors() {
    this._instance.interceptors.request.use(
      async config => {
        JHHeliosApiLogger.logRequest(config);
        return config;
      },
      error => {
        JHHeliosApiLogger.logError(error);
        return Promise.reject(handleHeliosApiError(error));
      },
    );

    this._instance.interceptors.response.use(
      (response: AxiosResponse<any>) => {
        JHHeliosApiLogger.logResponse(response);
        return response;
      },
      error => {
        JHHeliosApiLogger.logError(error);
        return Promise.reject(handleHeliosApiError(error));
      },
    );
  }
  public async request<T>(
    request: JHHeliosRequestHelper,
    retryCount: number = 0,
  ): Promise<{data: T; hashmap: string}> {
    try {
      if (!networkService.getConnectionStatus()) {
        throw {
          code: JMHeliosErrorCodes.NETWORK_ERROR,
          message: JMHeliosErrorMessages.NETWORK_ERROR,
        } as JMHeliosError;
      }
      if (request.getPlatform() === JMBaseUrlKeys.HELIOS) {
        const token =
          await useDashboardApiNetworkState().generateHeliosToken(false);
        if (!isNullOrUndefinedOrEmpty(token)) {
          await request.updateRequestDetails();
          const response = await this._instance.request<T>(request.build());
          return {
            data: response.data,
            hashmap: !isNullOrUndefinedOrEmpty(response.headers)
              ? response.headers['hashmap'] ?? ''
              : '',
          };
        } else {
          throw {
            code: JMHeliosErrorCodes.TOKEN_NOT_AVAILABLE,
            message: JMHeliosErrorMessages.SERVER_ERROR,
          } as JMHeliosError;
        }
      } else {
        await request.updateRequestDetails();
        const response = await this._instance.request<T>(request.build());
        return {
          data: response.data,
          hashmap: !isNullOrUndefinedOrEmpty(response.headers)
            ? response.headers['hashmap'] ?? ''
            : '',
        };
      }
    } catch (error: any) {
      if (
        error.code === JMHeliosErrorCodes.TOKEN_EXPIRED &&
        retryCount < JHHeliosNetworkRequestConstants.MAX_RETRIES
      ) {
        const heliosToken =
          await useDashboardApiNetworkState().generateHeliosToken(true);
        if (!isNullOrUndefinedOrEmpty(heliosToken)) {
          return this.request<T>(request, retryCount + 1);
        } else {
          throw error;
        }
      } else {
        throw error;
      }
    }
  }
}
