import {
  JMHeliosError,
  JMHeliosErrorMessages,
  JMHeliosErrorCodes,
} from './JMHeliosErrorCodes';

export const handleHeliosApiError = (error: any): JMHeliosError => {
  if (error.response) {
    const apiErrorData = error.response.data;
    console.log({apiErrorData});
    if (apiErrorData && apiErrorData.message && error.response.status) {
      return {
        code: error.response.status as JMHeliosErrorCodes,
        message: apiErrorData.message || JMHeliosErrorMessages.UNKNOWN_ERROR,
      };
    }
    const status = error.response.status;
    switch (status) {
      case JMHeliosErrorCodes.TOKEN_EXPIRED:
      case JMHeliosErrorCodes.FORBIDDEN:
        return {
          code: JMHeliosErrorCodes.TOKEN_EXPIRED,
          message: JMHeliosErrorMessages.TOKEN_EXPIRED,
        };
      case JMHeliosErrorCodes.TOKEN_MISMATCH:
        return {
          code: JMHeliosErrorCodes.TOKEN_MISMATCH,
          message: JMHeliosErrorMessages.TOKEN_MISMATCH,
        };
      case JMHeliosErrorCodes.BAD_REQUEST:
      case JMHeliosErrorCodes.UNPROCESSABLE_ENTITY:
        return {
          code: status,
          message: JMHeliosErrorMessages.BAD_REQUEST,
        };
      case JMHeliosErrorCodes.FORCE_LOGOUT:
        return {
          code: JMHeliosErrorCodes.FORCE_LOGOUT,
          message: JMHeliosErrorMessages.FORCE_LOGOUT,
        };
      case JMHeliosErrorCodes.SERVER_ERROR:
        return {
          code: JMHeliosErrorCodes.SERVER_ERROR,
          message: JMHeliosErrorMessages.SERVER_ERROR,
        };
      default:
        return {
          code: JMHeliosErrorCodes.UNKNOWN_ERROR,
          message: JMHeliosErrorMessages.UNKNOWN_ERROR,
        };
    }
  }
  // fallback for unhandled cases
  console.log(' --------- error message is not present --------- ', error);
  return {
    code: JMHeliosErrorCodes.UNKNOWN_ERROR,
    message: JMHeliosErrorMessages.UNKNOWN_ERROR,
  };
};
