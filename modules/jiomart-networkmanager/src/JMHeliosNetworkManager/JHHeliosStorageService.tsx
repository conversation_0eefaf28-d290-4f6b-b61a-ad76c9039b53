import {
  addStringPref,
  getPrefString,
} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {
  isNullOrUndefinedOrEmpty,
  NullableString,
} from '../../../jiomart-common/src/JMObjectUtility';
import { JMLogger } from '../../../jiomart-common/src/utils/JMLogger';
import {
  JMHeliosError,
  JMHeliosErrorCodes,
  JMHeliosErrorMessages,
} from './JMHeliosErrorCodes';

export class JHHeliosStorageService {
  static async setHeliosToken(
    key: NullableString,
    heliosToken: string,
  ): Promise<void> {
    try {
      if (isNullOrUndefinedOrEmpty(key)) {
        throw {
          code: JMHeliosErrorCodes.UNKNOWN_ERROR,
          message: JMHeliosErrorMessages.INVALID_KEY,
        } as JMHeliosError;
      }
      await addStringPref(key!, heliosToken);
    } catch {
      throw {
        code: J<PERSON><PERSON>eliosErrorCodes.UNKNOWN_ERROR,
        message: 'Error saving access token',
      } as JMHeliosError;
    }
  }

  static async getHeliosToken(key: NullableString): Promise<NullableString> {
    try {
      if (isNullOrUndefinedOrEmpty(key)) {
        throw {
          code: JMHeliosErrorCodes.UNKNOWN_ERROR,
          message: JMHeliosErrorMessages.INVALID_KEY,
        } as JMHeliosError;
      }
      return await getPrefString(key!);
    } catch (error) {
      JMLogger.log(`Error while fetching access token => ${error}`);
      return null;
    }
  }
}
