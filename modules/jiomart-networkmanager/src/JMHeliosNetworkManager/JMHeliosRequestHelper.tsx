import {AxiosRequestConfig} from 'axios';
from './JMEnvironmentConfig';

import {
  JHHeliosLocalStorageConstants,
  JHHeliosNetworkRequestConstants,
} from './JMHeliosConstant';
import {JHHeliosStorageService} from './JHHeliosStorageService';
import { JHHeliosDefaultBody, JMHeliosDefaultHeaders, JMHeliosHttpMethods, JMHeliosRequestConfig } from './JMHeliosRequestConfig';
import { isNullOrUndefinedOrEmpty } from '../../../jiomart-common/src/JMObjectUtility';
import { getJioMartEnvironmentConfig, JMBaseUrlKeys } from './JMEnvironmentConfig';

export default class JHHeliosRequestHelper {
  private config: AxiosRequestConfig = {};
  private _request: JMHeliosRequestConfig;
  constructor(request: JMHeliosRequestConfig) {
    this._request = request;
  }

  private async setupConfig() {
    this.config.url = this.getCompleteUrl();
    this.config.method = this.getHttpMethod();
    this.config.headers = await this.getHeaders();
    this.config.data = await this.getBodyParams();
    delete this.config.params;
    this.config.timeout = this.getTimeout();
  }

  async updateRequestDetails() {
    await this.setupConfig();
    return this;
  }

  build(): AxiosRequestConfig {
    if (!this.config.url || !this.config.method) {
      throw new Error(
        `Missing required fields (url => ${this.config.url}, method => ${this.config.method})`,
      );
    }
    return this.config;
  }

  private getBaseUrl() {
    const environmentConfigs = getJioMartEnvironmentConfig();
    if (environmentConfigs && environmentConfigs[this._request.baseUrl]) {
      return environmentConfigs[this._request.baseUrl];
    } else {
      console.error(
        `Base URL not found for Environment: ${environmentConfigs} and Service: ${this._request.baseUrl}`,
      );
      return undefined;
    }
  }

  private getCompleteUrl() {
    const baseUrl = this.getBaseUrl();
    if (baseUrl !== undefined && baseUrl !== null) {
      return `${baseUrl}${this._request.endpoint}`;
    }
  }

  private getHttpMethod() {
    switch (this._request.baseUrl) {
      case JMBaseUrlKeys.AWS_STORAGE:
        return JMHeliosHttpMethods.GET;
      default:
        return JMHeliosHttpMethods.POST;
    }
  }

  private async getHeaders() {
    let defaultHeaders = JMHeliosDefaultHeaders();
    const tokenParam = await this.getToken();
    if (!isNullOrUndefinedOrEmpty(tokenParam)) {
      defaultHeaders = {...defaultHeaders, ...tokenParam};
    }
    return {
      ...defaultHeaders,
      ...this._request.customHeaders,
    };
  }

  private async getToken() {
    switch (this._request.baseUrl) {
      case JMBaseUrlKeys.HELIOS:
        let token = await JHHeliosStorageService.getHeliosToken(
          JHHeliosLocalStorageConstants.HELIOS_TOKEN,
        );
        return {Authorization: `Bearer ${token}`};
      default:
        return null;
    }
  }

  private async getBodyParams() {
    let bodyParams = {...JHHeliosDefaultBody, ...this._request.body};
    return bodyParams;
  }

  private getTimeout() {
    switch (this._request.endpoint) {
      default:
        return JHHeliosNetworkRequestConstants.DEFAULT_TIME_OUT;
    }
  }

  getPlatform() {
    return this._request.baseUrl;
  }
}
