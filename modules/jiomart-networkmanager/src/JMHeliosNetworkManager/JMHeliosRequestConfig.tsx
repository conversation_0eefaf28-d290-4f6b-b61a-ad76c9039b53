import {Platform} from 'react-native';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {JMHeliosApiEndpointKeys} from './JMHeliosApiEndPoints';

export enum JMHeliosHttpMethods {
  GET = 'GET',
  POST = 'POST',
}

export enum JMHeliosHeaderContentType {
  ACEEPT = 'application/json, text/plain, */*',
  JSON = 'application/json',
  FORM_ENCODED = 'application/x-www-form-urlencoded',
  MULTIPART_FORM_DATA = 'multipart/form-data',
}

export const JMHeliosDefaultHeaders = (): {} => {
  return {
    Accept: JMHeliosHeaderContentType.ACEEPT,
    'Content-Type': JMHeliosHeaderContentType.JSON,
  };
};
export const JHHeliosDefaultBody = {
  os: Platform.OS,
  app_language: 'en_US',
  platform: JMSharedViewModel.Instance.appSource,
};

export interface JMHeliosRequestConfig {
  baseUrl: JHeliosBaseUrlKeys;
  endpoint: JMHeliosApiEndpointKeys;
  body: any | null;
  customHeaders: Record<string, string> | null;
  skip_encryption: boolean | false;
}

export class JMHeliosRequestConfigObject {
  static requestConfig(
    baseUrl: JMBaseUrlKeys = JMBaseUrlKeys.HELIOS,
    endpoint: JMHeliosApiEndpointKeys,
    body: any | null,
    customHeaders: Record<string, string> | null,
    skip_encryption = false,
  ): JMHeliosRequestConfig {
    return {
      baseUrl,
      endpoint,
      body,
      customHeaders,
      skip_encryption,
    };
  }
}
