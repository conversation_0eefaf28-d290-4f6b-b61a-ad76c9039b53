import {
  isDebugMode,
  isNullOrUndefinedOrEmpty,
} from '../../../jiomart-common/src/JMObjectUtility';
import {AxiosRequestConfig} from 'axios';

type logObject = any | null | undefined;
export class JHHeliosApiLogger {
  static logRequest(request: logObject, source: logObject = null) {
    if (!isDebugMode()) {
      return;
    }
    if (!isNullOrUndefinedOrEmpty(source)) {
      console.log(
        '\n\n',
        source,
        `busiCode API Request [HELIOS] (${new Date().toISOString()}): `,
      );
    } else {
      console.log(
        `\n\n Helios busiCode API Request [HELIOS] (${new Date().toISOString()}): `,
      );
    }
    const config = request as AxiosRequestConfig;
    console.log('URL => ', config.url);
    console.log('HTTP Method => ', config.method);
    console.log('Headers => ', config.headers);
    console.log('Params => ', JSON.stringify(config.data, null, 2));
  }

  static logResponse(response: logObject, source: logObject = null) {
    if (!isDebugMode()) {
      return;
    }
    if (!isNullOrUndefinedOrEmpty(source)) {
      console.info(
        '\n\n',
        source,
        `busiCode API Response [HELIOS] (${new Date().toISOString}):`,
        JSON.stringify(response, null, 2),
      );
    } else {
      console.info(
        `\n\n busiCode API [HELIOS] (${new Date().toISOString}): `,
        JSON.stringify(response, null, 2),
      );
    }
  }

  static logError(error: logObject, source: logObject = null) {
    if (!isDebugMode()) {
      return;
    }
    if (!isNullOrUndefinedOrEmpty(source)) {
      console.log(
        '\n\n',
        source,
        ` busiCode API Error [HELIOS] (${new Date().toISOString()}): `,
        JSON.stringify(error, null, 2),
      );
    } else {
      console.log(
        `\n\n busiCode API Error [HELIOS] (${new Date().toISOString()}): `,
        JSON.stringify(error, null, 2),
      );
    }
  }
}
