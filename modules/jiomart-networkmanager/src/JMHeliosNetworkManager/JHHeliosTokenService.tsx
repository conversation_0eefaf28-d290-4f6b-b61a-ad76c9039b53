import axios, {AxiosInstance, AxiosResponse} from 'axios';
import JHHeliosRequestHelper from './JMHeliosRequestHelper';
import {JMHeliosRequestConfigObject} from './JMHeliosRequestConfig';
import networkService from '@jhh/jio-health-common/src/JHNetworkConnectionUtility';
import {JHNetworkRequestConstants} from '../api/constants/JHNetworkRequestConstants';
import {JMBaseUrlKeys} from './JMEnvironmentConfig';
import {JHHeliosTokenEndpointKeys} from './JMHeliosApiEndPoints';
import {
  JMHeliosError,
  JMHeliosErrorCodes,
  JMHeliosErrorMessages,
} from './JMHeliosErrorCodes';
import {JHHeliosApiLogger} from './JMHeliosApiLogger';
import {handleHeliosApiError} from './JMHeliosErrorHandler';

export class JHHeliosTokenService {
  private _instance: AxiosInstance;

  constructor() {
    this._instance = axios.create();
    this.setupInterceptors();
  }

  private setupInterceptors() {
    this._instance.interceptors.request.use(
      async config => {
        JHHeliosApiLogger.logRequest(config);
        return config;
      },
      error => {
        JHHeliosApiLogger.logError(error);
        return Promise.reject(handleHeliosApiError(error));
      },
    );

    this._instance.interceptors.response.use(
      (response: AxiosResponse<any>) => {
        JHHeliosApiLogger.logResponse(response.data);
        return response.data;
      },
      error => {
        JHHeliosApiLogger.logError(error);
        return Promise.reject(handleHeliosApiError(error));
      },
    );
  }

  public async requestHeliosToken<T>(retryCount: number = 0): Promise<T> {
    const requestHelper = await this.getRequestHelper();
    return await this.requestWithRetry<T>(requestHelper, retryCount);
  }

  private async getRequestHelper() {
    const requestHelper = new JHHeliosRequestHelper(
      JMHeliosRequestConfigObject.requestConfig(
        JMBaseUrlKeys.HELIOS,
        JHHeliosTokenEndpointKeys.HELIOS_TOKEN,
        null,
        null,
        false,
      ),
    );
    return requestHelper;
  }

  // Handle token requests (with retries)
  private async requestWithRetry<T>(
    request: JHHeliosRequestHelper,
    retryCount: number = 0,
  ): Promise<T> {
    try {
      if (!networkService.getConnectionStatus()) {
        throw {
          code: JMHeliosErrorCodes.NETWORK_ERROR,
          message: JMHeliosErrorMessages.NETWORK_ERROR,
        } as JMHeliosError;
      }
      await request.updateRequestDetails();
      return await this._instance.request<T>(request.build());
    } catch (error) {
      if (retryCount < JHNetworkRequestConstants.MAX_RETRIES) {
        return this.requestWithRetry(request, retryCount + 1);
      } else {
        throw {
          code: JMHeliosErrorCodes.TOKEN_LIMIT_REACHED,
          message: JMHeliosErrorMessages.TOKEN_LIMIT_REACHED,
        } as JMHeliosError;
      }
    }
  }
}
