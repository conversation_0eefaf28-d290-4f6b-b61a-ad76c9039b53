import { JMSharedViewModel } from '../../../jiomart-common/src/JMSharedViewModel';
import { AppSourceType } from '../../../jiomart-common/src/SourceType';
import {getCurrentEnvironment, JMEnvironment} from '../JMEnvironmentConfig';

export const enum JMBaseUrlKeys {
  HELIOS = 'HELIOS',
  AWS_STORAGE = 'AWS_STORAGE',
}

export const enum JMConfigNames {
  CONFIG_FILES = 'config_files',
  IMAGES = 'images',
}

const environmentConfig = {
  [JMEnvironment.SIT]: {
    [JMBaseUrlKeys.AWS_STORAGE]: 'https://myjiostatic.cdn.jio.com',
  },
  [JMEnvironment.PROD]: {
    [JMBaseUrlKeys.AWS_STORAGE]: 'https://myjiostatic.cdn.jio.com',
  },
  [JMEnvironment.UAT]: {
    [JMBaseUrlKeys.AWS_STORAGE]: 'https://myjiostatic.cdn.jio.com',
  },
};
const awsConfigPath = {
  [JMEnvironment.SIT]: {
    [AppSourceType.JM_JCP]: {
      [JMConfigNames.CONFIG_FILES]: 'JioMart/JCP_RN/rn/sit/config_files',
    [JMConfigNames.IMAGES]: '',
    },
    [AppSourceType.JM_BAU]: {
      [JMConfigNames.CONFIG_FILES]: 'JioMart/BAU_RN/rn/sit/config_files',
     [JMConfigNames.IMAGES]: '',
    },
  },
  [JMEnvironment.PROD]: {
    [AppSourceType.JM_JCP]: {
      [JMConfigNames.CONFIG_FILES]: 'JioMart/JCP_RN/rn/prod/config_files',
    [JMConfigNames.IMAGES]: '',
    },
    [AppSourceType.JM_BAU]: {
      [JMConfigNames.CONFIG_FILES]: 'JioMart/BAU_RN/rn/prod/config_files',
     [JMConfigNames.IMAGES]: '',
    },
  },
};

const currentEnvironment = getCurrentEnvironment();

export const getHeliosCurrentEnv = () => {
  return currentEnvironment;
};


export const getAwsConfigPath = () => {
  const appSource = JMSharedViewModel.Instance.appSource;
  const envConfig = awsConfigPath[currentEnvironment];
  return envConfig?.[appSource as keyof typeof envConfig];
};

export const getJioMartEnvironmentConfig = () => {
  return environmentConfig[currentEnvironment];
};
