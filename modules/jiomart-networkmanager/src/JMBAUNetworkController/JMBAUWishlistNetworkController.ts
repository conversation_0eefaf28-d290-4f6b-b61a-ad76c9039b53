import {JMBAUWishlistEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {
  JMHttpMethods,
  JMRequestConfigObject,
} from '../api/helpers/JMRequestConfig';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import JMApiClient from '../api/service/JMApiClient';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';
import JMBaseWishlistNetworkController from '../base/JMBaseWishlistNetworkController';
import {Platform} from 'react-native';
import {JMDatabaseManager} from '../db/JMDatabaseManager';

class JMBAUWishlistNetworkController extends JMBaseWishlistNetworkController {
  private apiClient: JMApiClient = JMApiClient.getInstance;
  private baseUrl: JMBaseUrlKeys = JMBaseUrlKeys.BAU;
  constructor() {
    super();
  }

  protected fetchWishlist = async (): Promise<any> => {
    try {
      let address: any = await JMDatabaseManager.address.getDefaultAddress();
      address = JSON.parse(address);
      const pinCode = address?.pin;
      const formData = new FormData();
      formData.append('action', 'fetch');
      formData.append('productCode', '');
      formData.append('source', Platform.OS);
      formData.append('pin', pinCode);

      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUWishlistEndpointKeys.WISHLIST,
          JMHttpMethods.POST,
          formData,
          undefined,
          {['Content-Type']: 'multipart/form-data'},
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected addToWishlist = async (request: any): Promise<any> => {
    try {
      let address: any = await JMDatabaseManager.address.getDefaultAddress();
      address = JSON.parse(address);
      const pinCode = address?.pin;
      const {action = 'Add', productCode, pin = pinCode} = request;
      // const formData = new FormData();
      // formData.append('action', action);
      // formData.append('productCode', productCode);
      // formData.append('source', Platform.OS);
      // formData.append('pin', pin);
      const formData = `action=${action}&pin=${pin}&source=${Platform.OS}&productCode=${productCode}`;

      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUWishlistEndpointKeys.ADD_TO_WISHLIST,
          JMHttpMethods.POST,
          formData,
          undefined,
          {['Content-Type']: 'application/x-www-form-urlencoded'},
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected removeFromWishlist = async (request: any): Promise<any> => {
    try {
      let address: any = await JMDatabaseManager.address.getDefaultAddress();
      address = JSON.parse(address);
      const pinCode = address?.pin;
      const {action = 'Remove', productCode, pin = pinCode} = request;
      const formData = `action=${action}&pin=${pin}&source=${Platform.OS}&productCode=${productCode}`;

      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUWishlistEndpointKeys.ADD_TO_WISHLIST,
          JMHttpMethods.POST,
          formData,
          undefined,
          {['Content-Type']: 'application/x-www-form-urlencoded'},
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
}

export default JMBAUWishlistNetworkController;
