import {JMJCPQCEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {
  JMHttpMethods,
  JMRequestConfigObject,
} from '../api/helpers/JMRequestConfig';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import J<PERSON><PERSON><PERSON>lient from '../api/service/JMApiClient';
import JMB<PERSON><PERSON><PERSON>NetworkController from '../base/JMBaseQCNetworkController';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';

export default class JMJCPQCNetworkController extends JMBaseQ<PERSON>NetworkController {
  private apiClint: JMApiClient = JMApiClient.getInstance;
  private baseUrl: JMBaseUrlKeys = JMBaseUrlKeys.BAU;

  constructor() {
    super();
  }
  protected fetchQCDetails = async (): Promise<any> => {
  };
}
