import {
  JMHttpMethods,
  JMRequestConfigObject,
} from '../api/helpers/JMRequestConfig';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import JMApiClient from '../api/service/JMApiClient';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';
import JMBaseFeedbackNetworkController from '../base/JMBaseFeedbackNetworkController';
import {JMBAUFeedbackApiEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {Platform} from 'react-native';

class JMBAUFeedbackNetworkController extends JMBaseF<PERSON>backNetworkController {
  private apiClient: JMApiClient = JMApiClient.getInstance;
  private baseUrl: JMBaseUrlKeys = JMBaseUrlKeys.RATING_REVIEW;
  constructor() {
    super();
  }

  protected fetchReviewList = async (request: any) => {
    try {
      const {param, body} = request;

      const channel = `app-${Platform.OS}`;

      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUFeedbackApiEndpointKeys.REVIEW_LIST,
          JMHttpMethods.POST,
          body,
          {channel, ...param},
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
  protected fetchReviewValidationConfig = async (request: any) => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUFeedbackApiEndpointKeys.REVIEW_VALIDATION_CONFIG,
          JMHttpMethods.GET,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
  protected submitReview = async (request: any) => {
    try {
      const {body} = request;
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUFeedbackApiEndpointKeys.SUBMIT_REVIEW,
          JMHttpMethods.POST,
          body,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
  protected updateReview = async (request: any) => {
    try {
      const {param, body} = request;
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUFeedbackApiEndpointKeys.UPDATE_REVIEW?.replace(
            '{reviewid}',
            param?.id,
          ),
          JMHttpMethods.PUT,
          body,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
  protected generateSignedUrl = async (request: any) => {
    try {
      const {body} = request;
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUFeedbackApiEndpointKeys.GENERATE_SIGNED_URL,
          JMHttpMethods.POST,
          body,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
  protected uploadImage = async (request: any) => {
    try {
      const formData = new FormData();
      const images = {
        uri: request?.uri,
        name: request?.fileName,
        type: 'image/jpeg',
      };

      formData.append('files', images);
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUFeedbackApiEndpointKeys.UPLOAD_IMAGE,
          JMHttpMethods.POST,
          formData,
          undefined,
          {'Content-Type': 'multipart/form-data'},
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
  protected generateRatingAndReviewRequestBody = async (request: any) => {
    try {
      const {productCode = ''} = request;
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          JMBaseUrlKeys.BAU,
          JMBAUFeedbackApiEndpointKeys.RATING_AND_REVIEW_REQUEST_BODY,
          JMHttpMethods.GET,
          undefined,
          {product_code: productCode},
          {
            authorization: 'Basic am1faGVsbGNhdDpNYWtzajkyOVFqc2Rq',
            source: Platform.OS,
          },
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
}

export default JMBAUFeedbackNetworkController;
