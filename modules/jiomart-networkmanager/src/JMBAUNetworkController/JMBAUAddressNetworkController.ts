import {JMBAUAddressEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {
  JMHttpMethods,
  JMRequestConfigObject,
} from '../api/helpers/JMRequestConfig';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import J<PERSON><PERSON>Client from '../api/service/JMApiClient';
import JMBaseAddressNetworkController from '../base/JMBaseAddressNetworkController';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';

class JMBAUAddressNetworkController extends JMBaseAddressNetworkController {
  private apiClient: JMApiClient = JMApiClient.getInstance;
  private baseUrl: JMBaseUrlKeys = JMBaseUrlKeys.BAU;
  constructor() {
    super();
  }

  protected fetchAddress = async (): Promise<any> => {
    try {
      // return JSON.stringify(DUMY_DATA);

      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUAddressEndpointKeys.ADDRESS_LIST,
          JMHttpMethods.GET,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected removeAddress = async (address_id: string): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUAddressEndpointKeys.REMOVE_ADDRESS?.replace(
            '{address_id}',
            address_id,
          ),
          JMHttpMethods.GET,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected defaultAddress = async (
    address_id: string,
    body: any,
  ): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUAddressEndpointKeys.DEFAULT_BILLING_ADDRESS?.replace(
            '{address_id}',
            address_id,
          ),
          JMHttpMethods.GET,
          body,
        ),
      );
      let billingResponse = await this.apiClient.request(requestHelper);

      let shippingRequestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUAddressEndpointKeys.DEFAULT_SHIPPING_ADDRESS?.replace(
            '{address_id}',
            address_id,
          ),
          JMHttpMethods.GET,
          body,
        ),
      );
      let shippingResponse = await this.apiClient.request(
        shippingRequestHelper,
      );

      return shippingResponse;
    } catch (error) {
      throw error;
    }
  };
  protected editAddress = async (
    address_id: string,
    body: any,
  ): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUAddressEndpointKeys.EDIT_ADDRESS,
          JMHttpMethods.POST,
          body,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected insertAddress = async (body: any): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUAddressEndpointKeys.NEW_ADDRESS,
          JMHttpMethods.POST,
          body,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected fetchPincodeCity = async (pincode: string): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUAddressEndpointKeys.GET_PINCODE?.replace('{pincode}', pincode),
          JMHttpMethods.GET,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
}

export default JMBAUAddressNetworkController;
