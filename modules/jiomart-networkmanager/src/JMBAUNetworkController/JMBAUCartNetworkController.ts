import {JMBAUCartEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {
  JMHttpMethods,
  JMRequestConfigObject,
} from '../api/helpers/JMRequestConfig';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import J<PERSON><PERSON>Client from '../api/service/JMApiClient';
import JMB<PERSON><PERSON>artNetworkController from '../base/JMBaseCartNetworkController';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';
import type {
  JMBAUCartModel,
  JMBAUCreateCart,
} from '../models/Cart/JMBAUCartModel';
import {AxiosError} from 'axios';
import {getPrefString} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '../../../jiomart-common/src/JMConstants';
import {JMDatabaseManager} from '../db/JMDatabaseManager';

class JMBAUCartNetworkController extends JMBase<PERSON>artNetworkController {
  private apiClient: JMApiClient = JMApiClient.getInstance;
  private baseUrl: JMBaseUrlKeys = JMBaseUrlKeys.BAU;
  constructor() {
    super();
  }

  protected fetchCart = async (): Promise<any> => {
    try {
      const dbCartID = await JMDatabaseManager.cart.getDBCartID();
      if (dbCartID == 0) {
        return await this.createCart();
      }
      const n = await JMDatabaseManager.cart.getDBCartID();
      const params = {n};
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUCartEndpointKeys.CART_LIST,
          JMHttpMethods.GET,
          undefined,
          params,
        ),
      );
      return await this.apiClient.request<JMBAUCartModel, any>(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  private createCart = async (): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUCartEndpointKeys.CREATE_CART,
          JMHttpMethods.GET,
        ),
      );
      const response = await this.apiClient.request<any, JMBAUCreateCart>(
        requestHelper,
      );

      if (response?.status === 'success') {
        await JMDatabaseManager.cart.updateDBCart({
          cart_id: response?.result.cart_id,
        });
        await JMDatabaseManager.cart.setDBCartID(response?.result.cart_id);
        return await this.fetchCart();
      } else {
        throw new AxiosError('Create Cart Api Failed', '500');
      }
    } catch (error) {
      throw error;
    }
  };

  protected addToCart = async (request: any): Promise<any> => {
    try {
      const n = await JMDatabaseManager.cart.getDBCartID();
      const params = {n};

      const data: any = await getPrefString(
        AsyncStorageKeys.MCAT_INVENTORY_DATA,
      )
      const searchEndPointData = await getPrefString(
        AsyncStorageKeys.SEARCH_END_POINT_DETAILS,
      );
      const store_codes = data ? JSON.parse(data)?.inventoryData?.store_codes : 
      searchEndPointData ? JSON.parse(searchEndPointData)?.polygonStore?.store_codes : undefined


      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUCartEndpointKeys.ADD_TO_CART,
          JMHttpMethods.GET,
          undefined,
          {...request, ...params},
          {Storecode: store_codes},
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected removeFromCart = async (request: any): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUCartEndpointKeys.REMOVE_FROM_CART,
          JMHttpMethods.GET,
          undefined,
          request,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
}

export default JMBAUCartNetworkController;
