import { BuyAgain, BuyAgainItem } from '../../../jiomart-search/src/types/AutoCompleteResponse';
import {JMBAUHomeEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {
  JMHttpMethods,
  JMRequestConfigObject,
} from '../api/helpers/JMRequestConfig';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import JMApiClient from '../api/service/JMApiClient';
import JMBaseHomeNetworkController from '../base/JMBaseHomeNetworkController';
import { JMDatabaseManager } from '../db/JMDatabaseManager';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';

class JMBAUHomeNetworkController extends JMBaseHomeNetworkController {
  private apiClient: JMApiClient = JMApiClient.getInstance;
  private baseUrl: JMBaseUrlKeys = JMBaseUrlKeys.BAU;
  constructor() {
    super();
  }

  protected checkServiceability = async (queryParams: {}) => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUHomeEndpointKeys.CHECK_SERVICEABILITY,
          JMHttpMethods.GET,
          {},
          queryParams,
          {},
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected getMcatInventory = async (pincode: string) => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUHomeEndpointKeys.GET_MCAT_INVENTORY + `/${pincode}`,
          JMHttpMethods.GET,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected getSingleTopDeal = async (queryParams: {}, customHeaders: {}) => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUHomeEndpointKeys.GET_SINGLE_TOP_DEAL,
          JMHttpMethods.GET,
          {},
          queryParams,
          customHeaders,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected getHomepageData = async (queryParams: {}, customHeaders: {}) => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUHomeEndpointKeys.GET_HOMEPAGE_DATA,
          JMHttpMethods.GET,
          undefined,
          queryParams,
          customHeaders,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected getWidgets = async (queryParams: {}, customHeaders: {}) => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUHomeEndpointKeys.GET_WIDGETS,
          JMHttpMethods.GET,
          undefined,
          queryParams,
          customHeaders,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected getSingleWidget = async (queryParams: {}, customHeaders: {}) => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUHomeEndpointKeys.GET_SINGLE_WIDGET,
          JMHttpMethods.GET,
          undefined,
          queryParams,
          customHeaders,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected getCategoryDetails = async (body: {}, customHeaders: {}) => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUHomeEndpointKeys.GET_CATEGORY_DETAILS,
          JMHttpMethods.GET,
          {},
          body,
          customHeaders,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected fetchProductDetailsFromAlgolia = async (bodyParams: any) => {
    const body = {skuid: bodyParams};
    let data: any = await JMDatabaseManager.address.getDefaultAddress();
    const parseData = JSON.parse(data);
    const pincode = parseData?.pin;

    try {
      const customHeader = {
        pin: pincode,
      };

      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUHomeEndpointKeys.GET_ALGOLIA_PRODUCT_DETAILS,
          JMHttpMethods.POST,
          body,
          null,
          customHeader,
        ),
      );

      const buyAgainData = await this.apiClient.request(requestHelper);
      const tempData = this.transformRecommendedResponse(buyAgainData?.data);
      return {items: tempData} as BuyAgain;
    } catch (error) {
      throw error;
    }
  };

  private transformRecommendedResponse = (rawData: any[]): BuyAgainItem[] => {
    return rawData.map(item => ({
      url: item.url_path,
      type: '',
      name: item.display_name,
      slug: '',
      uid: Number(item.alternate_product_code),
      item_code: item.product_code,
      attributes: {
        'vertical-code': item.vertical_code,
      },
      medias: [
        {
          type: 'image',
          url: item.image,
          alt: '',
        },
      ],
      discount: `${item.discount_pct} % OFF`,
      seller_id: item.seller_id,
      price: {
        effective: {
          min: item.selling_price,
          max: item.selling_price,
        },
        marked: {
          min: item.mrp,
          max: item.mrp,
        },
      },
      sellable: item.in_stock > 0,
      discount_pct: item.discount_pct,
      min_qty_in_order: item.min_qty_in_order,
      max_qty_in_order: item.max_qty_in_order,
      selling_price: item.selling_price,
      availability_status: item.availability_status,
      category_level_id: item.category_level_id,
    }));
  };
}

export default JMBAUHomeNetworkController;
