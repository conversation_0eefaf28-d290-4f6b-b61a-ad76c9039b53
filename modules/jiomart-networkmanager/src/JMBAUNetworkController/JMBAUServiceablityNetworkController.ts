import axios from 'axios';
import {JMBAUHomeEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {
  JMHttpMethods,
  JMRequestConfigObject,
} from '../api/helpers/JMRequestConfig';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import JMApiClient from '../api/service/JMApiClient';
import JMBaseServiceablityNetworkController from '../base/JMBaseServiceablityNetworkController';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';
import {JMDatabaseManager} from '../db/JMDatabaseManager';

class JMBAUServiceablityNetworkController extends JMBaseServiceablityNetworkController {
  private apiClient: JMApiClient = JMApiClient.getInstance;
  private baseUrl: JMBaseUrlKeys = JMBaseUrlKeys.BAU;
  constructor() {
    super();
  }

  protected getPinMetadataInfo = async (pincode: string): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUHomeEndpointKeys.GET_PINCODE?.replace('{pincode}', pincode),
          JMHttpMethods.GET,
          // null,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected getHCATQCConfig = async (pincode: string): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUHomeEndpointKeys.HCAT_QC_CONFIG?.replace('{pincode}', pincode),
          JMHttpMethods.GET,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected getPolygonStoreDetails = async (queryParams: {}): Promise<any> => {
    try {
      // TODO : This needs to be made using JMRequestHelper
      //Example full url : https://api.tmsz0.de/service/public/geography/v1.0/stores/serviceable?lat=18.**********&lng=72.**********
      const url = `https://api.tmsz0.de${JMBAUHomeEndpointKeys.STORE_DETAILS}`;
      const response = await axios.get(url, {
        params: queryParams,
        headers: {
          'Content-Type': 'application/json',
          'account-token': '84abe8b4-1ba7-4c71-a28b-5828c9eb3d54',
        },
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  };

  protected getMStarRegionCode = async (queryParams: {}): Promise<any> => {
    //https://org-jiomart-sit-pvt.jio.com/mcat/rest/v1/5/pvt/mracer/get_region_code?vertical_code=groceries&store_code=5518
    try {
      // TODO : This needs to be made using JMRequestHelper
      //Example full url : https://org-jiomart-sit-pvt.jio.com/mcat/rest/v1/5/pvt/mracer/get_region_code?vertical_code=groceries&store_code=5518
      const url = `https://org-jiomart-sit-pvt.jio.com${JMBAUHomeEndpointKeys.GET_REGION_CODE}`;
      const response = await axios.get(url, {
        params: queryParams,
        headers: {
          'Content-Type': 'application/json',
        },
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  };

  protected getFyndPromiseInfo = async (promiseParams: {}): Promise<any> => {
    const headers = {
      'x-application-token': '*****************************',
      'x-oms-application-id': 'morningstar',
    };
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUHomeEndpointKeys.CHECK_PROMISE,
          JMHttpMethods.POST,
          promiseParams,
          null,
          headers,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected setDeliveryDistance = async (params: {}): Promise<any> => {
    try {
      // TODO : This needs to be made using JMRequestHelper
      //Example full url : https://org-jiomart-sit-pvt.jio.com/mst/rest/v1/5/pvt/set_delivery_distance?cart_id=12193&delivery_distance=432
      const url = `https://org-jiomart-sit-pvt.jio.com${JMBAUHomeEndpointKeys.SET_DELIVERY_DISTANCE}`;
      const headers = await this.getBauHeaderDetails();
      const response = await axios.get(url, {
        params: params,
        headers: {
          ...headers,
          'Content-Type': 'application/json',
        },
      });
      return response.data;
    } catch (error) {
      console.error('SetDeliveryDistance : ', error);
      throw error;
    }
  };

  private async getBauHeaderDetails() {
    const userSession = await JMDatabaseManager.user.getUserSession();
    if (userSession) {
      const userSessionData = JSON.parse(userSession);
      const headers = {
        authtoken: userSessionData?.id,
        userid: userSessionData?.customer_id,
      };
      return headers;
    } else {
      const guestUserSession =
        await JMDatabaseManager.user.getGuestUserSession();
      if (guestUserSession) {
        const userSessionData = JSON.parse(guestUserSession);
        const headers = {
          authtoken: userSessionData?.id ?? '',
          userid: userSessionData?.customer_id ?? '',
        };
        return headers;
      }
    }
  }

  protected getFyndPromiseInfoWrapper = async (
    params: {},
    headers: {},
  ): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUHomeEndpointKeys.CHECK_PROMISE_WRAPPER,
          JMHttpMethods.POST,
          params,
          null,
          headers,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
}

export default JMBAUServiceablityNetworkController;
