import {JMBAUOrderEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {
  JMRequestConfigObject,
  JMHttpMethods,
} from '../api/helpers/JMRequestConfig';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import J<PERSON>piClient from '../api/service/JMApiClient';
import JMBaseOrderNetworkController from '../base/JMBaseOrderNetworkController';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';

class JMBAUOrderNetworkController extends JMBaseOrderNetworkController {
  private apiClient: JMApiClient = JMApiClient.getInstance;
  private baseUrl: JMBaseUrlKeys = JMBaseUrlKeys.BAU;
  constructor() {
    super();
  }

  protected fetchOrderList = async (request: any): Promise<any> => {
    try {
      const {pageIndex, pageSize, timeFilter, orderStatusText} = request;
      const formData = new FormData();
      formData.append('pageIndex', String(pageIndex));
      formData.append('pageSize', String(pageSize));
      formData.append('timeFilter', String(timeFilter));
      formData.append('orderStatusText', String(orderStatusText));

      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUOrderEndpointKeys.ORDER_LIST,
          JMHttpMethods.POST,
          formData,
          undefined,
          {'Content-Type': 'multipart/form-data'},
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
  protected fetchRefundList = async (request: any): Promise<any> => {
    try {
      const {pageIndex, pageSize, timeFilter} = request;
      const formData = new FormData();
      formData.append('pageIndex', String(pageIndex));
      formData.append('pageSize', String(pageSize));
      if(timeFilter){
        formData.append('timeFilter', String(timeFilter));
      }
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUOrderEndpointKeys.REFUND_LIST,
          JMHttpMethods.POST,
          formData,
          undefined,
          {'Content-Type': 'multipart/form-data'},
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
  protected fetchRefundDetails = async (request: any): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMBAUOrderEndpointKeys.REFUND_DETAILS,
          JMHttpMethods.POST,
          undefined,
          request,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
}

export default JMBAUOrderNetworkController;
