import {isNullOrUndefinedOrEmpty} from '../../../jiomart-common/src/JMObjectUtility';
import {
  addStringPref,
  getPrefString,
  removeStringPref,
} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import type {JMAddressModel} from '../../../jiomart-common/src/uiModals/JMAddressModel';
import {AsyncStorageKeys} from '../../../jiomart-common/src/JMConstants';
import { JMSharedViewModel } from '../../../jiomart-common/src/JMSharedViewModel';
import { JMLogger } from '../../../jiomart-common/src/utils/JMLogger';

class JMAddressManager {
  static setIntialAddress = (address: JMAddressModel) => {
    getPrefString(AsyncStorageKeys.X_LOCATION_DETAIL)
      .then(val => {
        if (!val) {
          addStringPref(
            AsyncStorageKeys.X_LOCATION_DETAIL,
            JSON.stringify(address),
          );
        }
      })
      .catch(() => {
        addStringPref(
          AsyncStorageKeys.X_LOCATION_DETAIL,
          JSON.stringify(address),
        );
      });
  };

  static updateDBAddress = async (val: JMAddressModel) => {
    try {
      const existingData = await this.getDefaultAddress();
      const parsedExisting = existingData ? JSON.parse(existingData) : {};

      const updatedData = {...parsedExisting, ...val};

      await addStringPref(
        AsyncStorageKeys.X_LOCATION_DETAIL,
        JSON.stringify(updatedData),
      );
    } catch (error) {
      throw error;
    }
  };

  static getDefaultAddress = async () => {
    try {
      return await getPrefString(AsyncStorageKeys.X_LOCATION_DETAIL);
    } catch (error) {
      throw error;
    }
  };
  static getDefaultAddressModel = async (): Promise<JMAddressModel | null> => {
    try {
      const address = await JMDatabaseManager.address.getDefaultAddress();
      if (address) {
        return JSON.parse(address ?? '');
      }
      return null;
    } catch (error) {
      throw error;
    }
  };
  static getDBAddressList = async () => {
    try {
      return await getPrefString(AsyncStorageKeys.ADDRESS_LIST_DETAIL);
    } catch (error) {
      throw error;
    }
  };
  static updateAddressListInDB = async (val: JMAddressModel[]) => {
    await addStringPref(
      AsyncStorageKeys.ADDRESS_LIST_DETAIL,
      JSON.stringify(val),
    );
  };

  static REVERSE_GEOCODE_CACHE_KEY = 'REVERSE_GEOCODE_CACHE';
  static REVERSE_GEOCODE_CACHE_SIZE = 5;

  static getDistanceMeters(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371000; // Earth radius in meters
    const toRad = (deg: number) => deg * Math.PI / 180;
    const dLat = toRad(lat2 - lat1);
    const dLon = toRad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  static async getCachedReverseGeocode(lat: number, lon: number): Promise<any | null> {
    try {
      const cacheStr = await getPrefString(this.REVERSE_GEOCODE_CACHE_KEY);
      if (!cacheStr) return null;
      const cache = JSON.parse(cacheStr);
      if (!Array.isArray(cache)) return null;
      // Find with 50 meter distance
      const found = cache.find((item: any) =>
        this.getDistanceMeters(item.lat, item.lon, lat, lon) <= 50
      );
      return found ? found.address : null;
    } catch (e) {
      return null;
    }
  }

  static async setCachedReverseGeocode(lat: number, lon: number, address: any) {
    try {
      const cacheStr = await getPrefString(this.REVERSE_GEOCODE_CACHE_KEY);
      let cache = [];
      if (cacheStr) {
        try { cache = JSON.parse(cacheStr); } catch {}
      }
      if (!Array.isArray(cache)) cache = [];
      // Add new entry to front
      cache.unshift({ lat, lon, address });
      // Trim to max size
      if (cache.length > this.REVERSE_GEOCODE_CACHE_SIZE) {
        cache = cache.slice(0, this.REVERSE_GEOCODE_CACHE_SIZE);
      }
      await addStringPref(this.REVERSE_GEOCODE_CACHE_KEY, JSON.stringify(cache));
    } catch (e) {}
  }

  static async clearReverseGeocodeCache() {
    try {
      await removeStringPref(this.REVERSE_GEOCODE_CACHE_KEY);
    } catch (e) {}
  }
}

class JMUserManager {

  static isUserLoggedInFlag = (): boolean => {
      const loggedInUser = JMSharedViewModel.Instance.loggedInStatus;
      JMLogger.log("JMUserManager isUserLoggedInFlag ", "isUserLoggedInFlag "+loggedInUser)
      return loggedInUser;
  };

  static isUserLoggedIn = async () => {
    try {
      const userDetails = await this.getUserDetails();
      if (isNullOrUndefinedOrEmpty(userDetails)) {
        return false;
      }
      return true;
    } catch {
      return false;
    }
  };

  static getUserDetails = async() => {
    const userDetails = await getPrefString(AsyncStorageKeys.PROFILE_DETAILS);
    if (isNullOrUndefinedOrEmpty(userDetails)) {
      return null;
    }
    JMLogger.log("JMUserManager getUserDetails ", "getUserDetails "+JSON.stringify(userDetails))
    JMSharedViewModel.Instance.setLoggedInStatus(true);
    return userDetails;
  };

  static saveUserDetails = async (userDetails: string) => {
    if(!isNullOrUndefinedOrEmpty(userDetails)){
      await addStringPref(AsyncStorageKeys.PROFILE_DETAILS, userDetails);
      JMLogger.log("JMUserManager saveUserDetails ", "saveUserDetails true")
      JMSharedViewModel.Instance.setLoggedInStatus(true);
    }
  };
  
  static getUserSession = async() => {
    const userSession = await getPrefString(AsyncStorageKeys.USER_SESSION);
    if (isNullOrUndefinedOrEmpty(userSession)) {
      return null;
    }
    return userSession;
  };

  static saveUserSession = async (userSession: string) => {
    if(!isNullOrUndefinedOrEmpty(userSession)){
      await addStringPref(AsyncStorageKeys.USER_SESSION, userSession);
      JMLogger.log("JMUserManager saveUserSession ", "saveUserSession true")
      JMSharedViewModel.Instance.setLoggedInStatus(true);
    }
  };
  
  static getGuestUserSession = async () => {
    const guestUserSession = await getPrefString(AsyncStorageKeys.GUEST_USER_SESSION);
    if (isNullOrUndefinedOrEmpty(guestUserSession)) {
      return null;
    }
    return guestUserSession;
  };

  static saveGuestUserSession = async (userDetails: string) => {
    await addStringPref(AsyncStorageKeys.GUEST_USER_SESSION, userDetails);
  };
  
  
  static deleteUserDetails = () => {
    removeStringPref(AsyncStorageKeys.USER_DETAILS);
  };
  
  static deleteUserSession = () => {
    removeStringPref(AsyncStorageKeys.USER_SESSION);
  };
  
  static deleteGuestUserSession = () => {
    removeStringPref(AsyncStorageKeys.GUEST_USER_SESSION);
  };
  
  static deleteProfileDetails = () => {
    removeStringPref(AsyncStorageKeys.PROFILE_DETAILS)
  }


  static getCraUserSession = async () => {
    const craUserSession = await getPrefString(AsyncStorageKeys.CRA_USER_SESSION_DATA);
    if (isNullOrUndefinedOrEmpty(craUserSession)) {
      return null;
    }
    return craUserSession;
  }
}

class JMCartManager {
  static updateDBCart = async (val: any) => {
    try {
      const existingData = await this.readDBCart();
      const parsedExisting = existingData ? JSON.parse(existingData) : {};

      const updatedData = {...parsedExisting, ...val};

      await addStringPref(
        AsyncStorageKeys.CART_DATA,
        JSON.stringify(updatedData),
      );
    } catch (error) {
      throw error;
    }
  };

  static readDBCart = async () => {
    try {
      let data = await getPrefString(AsyncStorageKeys.CART_DATA);
      return data ? JSON.parse(data || '') : null;
    } catch (error) {}
  };

  static getDBCartID = async () => {
    try {
      let cartId = await getPrefString(AsyncStorageKeys.CART_ID_DATA);
      return cartId ? parseInt(cartId) : 0;
    } catch (error) {
      return 0
    }
  };

  static setDBCartID = async (cartId: number | string) => {
    try {
      addStringPref(AsyncStorageKeys.CART_ID_DATA, cartId.toString());
    } catch (error) {}
  };

  static deleteDBCart = async () => {
    try {
      await removeStringPref(AsyncStorageKeys.CART_DATA);
      await removeStringPref(AsyncStorageKeys.CART_ID_DATA);
    } catch (error) {}
  };
}

export class JMDatabaseManager {
  static address = JMAddressManager;
  static user = JMUserManager;
  static cart = JMCartManager;
}
