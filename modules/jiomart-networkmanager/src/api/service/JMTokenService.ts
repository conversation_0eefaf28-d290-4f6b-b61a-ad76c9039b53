import axios, {AxiosInstance, AxiosResponse, InternalAxiosRequestConfig} from 'axios';
import {JMApiLogger} from '../../models/JMApiLogger';
import { handleApiError } from '../utils/JMErrorHandler';
import JMRequestHelper from '../helpers/JMRequestHelper';
import networkService from '../../../../jiomart-common/src/JMNetworkConnectionUtility';
import { JMError, JMErrorCodes, JMErrorMessages } from '../utils/JMErrorCodes';
import { JMNetworkRequestConstants } from '../constants/JMNetworkRequestConstants';
import { addStringPref, getPrefString } from '../../../../jiomart-common/src/JMAsyncStorageHelper';
import JMDeviceInfoData from '../../../../jiomart-common/src/JMDeviceInfo';
import { AsyncStorageKeys } from '../../../../jiomart-common/src/JMConstants';
import { JMHttpMethods, JMRequestConfigObject } from '../helpers/JMRequestConfig';
import { JMBAURefreshTokenEndpointKeys, JMBAUUserApiEndpointKeys } from '../endpoints/JMApiEndpoints';
import { JMSharedViewModel } from '../../../../jiomart-common/src/JMSharedViewModel';
import { AppSourceType } from '../../../../jiomart-common/src/SourceType';
import { JMBaseUrlKeys } from '../../JMEnvironmentConfig';
import JMApiClient from './JMApiClient';
import axiosToCurl from '../utils/curl';
import { Platform } from 'react-native';
import { JMDatabaseManager } from '../../db/JMDatabaseManager';





export class JMTokenService {
  private static sharedInstance: JMTokenService;
  private _instance: AxiosInstance;
  private accountBaseUrl: JMBaseUrlKeys;

  private constructor() {
    this._instance = axios.create();
    this.setupInterceptors();
    this.accountBaseUrl = JMBaseUrlKeys.ACCOUNT;
  }

  public static getInstance(): JMTokenService {
    if (!JMTokenService.sharedInstance) {
        JMTokenService.sharedInstance = new JMTokenService();
    }
    return JMTokenService.sharedInstance;
  }

  private getCurl(request: InternalAxiosRequestConfig) {
    try {
      const curlCommand = axiosToCurl(request);
      console.log('------------ CURL ------------');
      console.log(curlCommand);
      console.log('------------ END CURL ------------');
    } catch (error) {}
  }
  
  private setupInterceptors() {
    this._instance.interceptors.request.use(
      async config => {
        this.getCurl(config);
        JMApiLogger.logRequest(config);
        return config;
      },
      error => {
        JMApiLogger.logError(error);
        return Promise.reject(handleApiError(error));
      },
    );

    this._instance.interceptors.response.use(
      (response: AxiosResponse<any>) => {
        JMApiLogger.logResponse(response.data);
        return response.data;
      },
      error => {
        JMApiLogger.logError(error);
        return Promise.reject(handleApiError(error));
      },
    );
  }



 

  public async request<T, M>(
    request: JMRequestHelper<M>,
    retryCount: number = 0,
  ): Promise<T> {
    try {
      if (!networkService.getConnectionStatus()) {
        throw {
          code: JMErrorCodes.NETWORK_ERROR,
          message: JMErrorMessages.NETWORK_ERROR,
        } as JMError;
      }
      await request.updateRequestDetails();
      return await this._instance.request(request.build());
    } catch (error: any) {
      if (retryCount < JMNetworkRequestConstants.REFRESH_TOKEN_MAX_RETRIES) {
        return this.request(request, retryCount + 1);
      } else {
        JMApiClient.getInstance.resetSharedInstanceData();
        throw {
            code: JMErrorCodes.TOKEN_LIMIT_REACHED,
            message: JMErrorMessages.TOKEN_LIMIT_REACHED,
          } as JMError;
      }
    }
  }

  protected JMBAURefreshTokenApi = async () => {
    try {
      const sessionData = await getPrefString(AsyncStorageKeys.CRA_USER_SESSION_DATA);
    const parsedSessionData = JSON.parse(sessionData)
    
    const body = {
      access_token: parsedSessionData?.access_token,
      refresh_token: parsedSessionData?.refresh_token
    }
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.accountBaseUrl,
          JMBAURefreshTokenEndpointKeys.REFRESH_TOKEN,
          JMHttpMethods.POST,
          JSON.stringify(body),
        ),
      );
      const updatedSessionData = await this.request(requestHelper);
      if(updatedSessionData && updatedSessionData?.success === true && updatedSessionData?.data){
        await addStringPref(AsyncStorageKeys.CRA_USER_SESSION_DATA, JSON.stringify(updatedSessionData?.data));
      } 
      return await updatedSessionData;
    } catch (error) {
      throw error;
    }
  }

  protected JMJCPRefreshTokenApi = async () => { 
    try {
    } catch (error) {
      throw error;
    }
  }

  public async callRefreshTokenApi(): Promise<any> {
    if(JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU){
      return await this.JMBAURefreshTokenApi();
    }
    else{
      return await this.JMJCPRefreshTokenApi();
    }
  }




  protected JMBAUGuestTokenApi = async () => {
    try {
      const params = {
        channel: `app-${Platform.OS}`,
      };
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          JMBaseUrlKeys.BAU,
          JMBAUUserApiEndpointKeys.CREATE_GUEST_USER_SESSION,
          JMHttpMethods.GET,
          undefined,
          params,
        ),
      );
      const guestSessionData: any = await this.request(requestHelper);
      if (guestSessionData.status === 'success') {
        const jsonString = JSON.stringify(guestSessionData.result.session);
        JMDatabaseManager.user.saveGuestUserSession(jsonString)
      }
      return await guestSessionData;
    } catch (error) {
      throw error;
    }
  }

  protected JMJCPGuestTokenApi = async () => { 
    try {
    } catch (error) {
      throw error;
    }
  }




  public async callGuestTokenApi(): Promise<any> {
    if(JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU){
      return await this.JMBAUGuestTokenApi();
    }
    else{
      return await this.JMJCPGuestTokenApi();
    }
  }

}
