const escapeQuotes = (val: string) => val.replace(/(\\|")/g, '\\$1');

const escapeSingleQuotes = (str: string) => str.replace(/'/g, `'\\''`);

const formatHeader = (name: string, val: any) =>
  ` -H "${name}: ${escapeQuotes(String(val))}"`;

const generateMethod = (method?: string) =>
  method ? ` -X ${method.toUpperCase()}` : '';

const generateHeaders = (headers: Record<string, any> = {}) => {
  let headerString = '';
  let isCompressed = false;

  Object.entries(headers).forEach(([name, val]) => {
    const lower = name.toLowerCase();
    if (lower === 'content-length') return;

    headerString += formatHeader(name, val);
    if (lower === 'accept-encoding') {
      isCompressed = true;
    }
  });

  return {headerString, isCompressed};
};

const generateBody = (data: any, method?: string) => {
  if (!data || method?.toUpperCase() === 'GET') return '';
  const raw = typeof data === 'string' ? data : JSON.stringify(data);
  return ` --data-binary '${escapeSingleQuotes(raw)}'`;
};

const generateCompressed = (isCompressed: boolean) =>
  isCompressed ? ' --compressed' : '';

const serializeParams = (params: Record<string, any>): string => {
  const searchParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      value.forEach(v => searchParams.append(`${key}[]`, String(v)));
    } else if (value !== undefined && value !== null) {
      searchParams.append(key, String(value));
    }
  });

  const result = searchParams.toString();
  return result ? `?${result}` : '';
};

/**
 * Converts an Axios request config to a cURL string
 * @param {import('axios').InternalAxiosRequestConfig} config
 * @returns {string}
 */
export const axiosToCurl = (config: any): string => {
  const {method, url, headers, data, params} = config;

  const queryString = params ? serializeParams(params) : '';
  const fullUrl = url + queryString;

  const {headerString, isCompressed} = generateHeaders(headers);

  return `curl '${fullUrl}'${generateMethod(
    method,
  )}${headerString}${generateBody(data, method)}${generateCompressed(
    isCompressed,
  )}`;
};

export default axiosToCurl;
