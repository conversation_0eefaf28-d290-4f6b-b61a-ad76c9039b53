import {
  isNullOrUndefinedOrEmpty,
  NullableString,
} from '../../../../jiomart-common/src/JMObjectUtility';
import * as CryptoJS from 'crypto-js';
export class Decoder {
  static getStringValue(encodedString: string): string {
    // Implement your decoding logic here
    // This example assumes Base64 decoding
    const buffer = Buffer.from(encodedString, 'base64');
    return buffer.toString('utf8');
  }
  
  static base64EncodeValue(contents: NullableString): NullableString {
    if (!contents) {
      return null;
    }
    const utf8Bytes = CryptoJS.enc.Utf8.parse(contents);
    const base64String = CryptoJS.enc.Base64.stringify(utf8Bytes);
    return base64String;
  }
  
  static base64DecodeValue(contents: NullableString): NullableString {
    if (!contents) {
      return null;
    }
    const decodedBytes = CryptoJS.enc.Base64.parse(contents);
    return CryptoJS.enc.Utf8.stringify(decodedBytes);
  }
}
