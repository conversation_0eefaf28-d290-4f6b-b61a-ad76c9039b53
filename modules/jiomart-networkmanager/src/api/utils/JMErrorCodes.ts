export enum JMErrorCodes {
  TOKEN_EXPIRED = 401,
  FORBIDDEN = 403,
  TOKEN_MISMATCH = 412,
  BAD_REQUEST = 400,
  UNPROCESSABLE_ENTITY = 422,
  FORCE_LOGOUT = 410,
  SERVER_ERROR = 500,
  NETWORK_ERROR = 'NETWORK_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  PARSE_ERROR = 'PARSE_ERROR',
  DATA_ERROR = 'DATA_ERROR',
  TOKEN_LIMIT_REACHED = 'TOKEN_LIMIT_REACHED',
  BRIDGE_DATA_ERROR = 'BRIDGE_DATA_ERROR',
  NETWORK_ERR_TIMEOUT = 'ERR_NETWORK',
}

export const JMErrorMessages = {
  TOKEN_EXPIRED: 'Token expired access. Please try again.',
  TOKEN_MISMATCH: 'Token mismatch. Please request a new token.',
  BAD_REQUEST: 'Invalid request. Please check your data.',
  FORCE_LOGOUT: 'Session expired. Please log in again.',
  SERVER_ERROR: 'Server error. Please try again later.',
  NETWORK_ERROR: 'No internet connection. Please check your network.',
  UNKNOWN_ERROR: 'An unknown error occurred. Please try again later.',
  PARSE_ERROR: 'Parse error. Please check response type and generic type',
  DATA_ERROR: 'Invalid data',
  INVALID_KEY: 'Invalid key provided',
  TOKEN_LIMIT_REACHED: 'Failed to get token after max retries',
  AUTHENTICATION_ERROR: 'Authentication Failed',
  BRIDGE_DATA_ERROR: 'Invalid data in bridge',
  NETWORK_ERR_TIMEOUT: 'Please check your connection and try again',
  CORPORATE_IDENTITY_UNLINKED: 'Associates partner not available.',
};

export interface JMError {
  code: JMErrorCodes;
  message: string;
  response: string;
}
