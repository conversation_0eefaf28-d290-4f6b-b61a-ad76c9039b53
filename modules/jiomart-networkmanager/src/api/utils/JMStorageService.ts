import {
  addStringPref,
  getPrefString,
  removePrefAllKeys,
} from '../../../../jiomart-common/src/JMAsyncStorageHelper';
import {
  isNullOrUndefinedOrEmpty,
  NullableNumber,
  NullableString,
} from '../../../../jiomart-common/src/JMObjectUtility';
import {JMLogger} from '../../../../jiomart-common/src/utils/JMLogger';

const valuesToNotClearOnLogout = {
  [JMConfigFileName.JMVersionFileName]: '',
  [JMConfigFileName.JMCommonContentFileName]: '',
  [JMConfigFileName.JMPLPConfigFileName]: '',
  [JMConfigFileName.JMGridPLPConfigurationFileName]: '',
  [JMConfigFileName.JMSearchPLPConfigurationFileName]: '',
  [JMConfigFileName.JMSearchConfigurationFileName]: '',
  [JMConfigFileName.JMProfileConfigurationFileName]: '',
  [JMConfigFileName.JMAddressConfigurationFileNAme]: '',
  [JMConfigFileName.JMPDPConfigurationFileName]: '',
  [JMConfigFileName.JMHeaderConfigurationFileName]: '',
  [JMConfigFileName.JMAllCategoriesConfigurationFileName]: '',
  [JMConfigFileName.JMDeeplinkConfigurationFileName]: '',
  [JMConfigFileName.JMBottomNavBarConfigurationFileName]: '',
  [JMConfigFileName.JMOrdersConfigurationFileName]: '',
  [JMConfigFileName.JMFeedbackConfigurationFileName]: '',
  [AsyncStorageKeys.X_LOCATION_DETAIL]: '',
  [AsyncStorageKeys.PERMISSION_DIALOG]: '',
  [AsyncStorageKeys.LOCATION_PERMISSION_GRANTED]: '',
  [AsyncStorageKeys.QUICK_BOTTOMSHEET_SHOWN]: '',
  [AsyncStorageKeys.ENABLE_LOCATION]: ''
};
import {JMLocalStorageConstants} from '../constants/JMNetworkRequestConstants';
import {JMConfigFileName} from '../../JMConfigFileManager/JMFileName';
import {JMSharedViewModel} from '../../../../jiomart-common/src/JMSharedViewModel';
import {JMError, JMErrorCodes, JMErrorMessages} from './JMErrorCodes';
import JMWebGlobalInfo from '../../../../jiomart-webmanager/src/WebGlobalInfo';
import {AsyncStorageKeys} from '../../../../jiomart-common/src/JMConstants';

export class JMStorageService {
  static async setAccessToken(
    accessToken: string,
    key: NullableString,
  ): Promise<void> {
    try {
      if (isNullOrUndefinedOrEmpty(key)) {
        throw {
          code: JMErrorCodes.UNKNOWN_ERROR,
          message: JMErrorMessages.INVALID_KEY,
        } as JMError;
      }
      await addStringPref(key!, accessToken);
    } catch {
      throw {
        code: JMErrorCodes.UNKNOWN_ERROR,
        message: 'Error saving access token',
      } as JMError;
    }
  }

  static async getAccessToken(key: NullableString): Promise<NullableString> {
    try {
      if (isNullOrUndefinedOrEmpty(key)) {
        throw {
          code: JMErrorCodes.UNKNOWN_ERROR,
          message: JMErrorMessages.INVALID_KEY,
        } as JMError;
      }
      return await getPrefString(key!);
    } catch (error) {
      JMLogger.log(`Error while fetching access token => ${error}`);
      return null;
    }
  }
  static async setClientPublicKey(
    clientPublicKey: bigint,
    key: NullableString,
  ): Promise<void> {
    try {
      if (isNullOrUndefinedOrEmpty(key)) {
        throw {
          code: JMErrorCodes.UNKNOWN_ERROR,
          message: JMErrorMessages.INVALID_KEY,
        } as JMError;
      }
      await addStringPref(key!, clientPublicKey.toString());
    } catch {
      throw {
        code: JMErrorCodes.UNKNOWN_ERROR,
        message: 'Error while saving client public key',
      } as JMError;
    }
  }

  static async getClientPublicKey(
    key: NullableString,
  ): Promise<NullableString> {
    try {
      if (isNullOrUndefinedOrEmpty(key)) {
        throw {
          code: JMErrorCodes.UNKNOWN_ERROR,
          message: JMErrorMessages.INVALID_KEY,
        } as JMError;
      }
      const clientPublicKey = await getPrefString(key!);
      return !isNullOrUndefinedOrEmpty(clientPublicKey)
        ? BigInt(clientPublicKey!).toString()
        : null;
    } catch (error) {
      JMLogger.log(`Error while fetching access token => ${error}`);
      return null;
    }
  }

  static async setRandomNumber(
    randomNumber: number,
    key: NullableString,
  ): Promise<void> {
    try {
      if (isNullOrUndefinedOrEmpty(key)) {
        throw {
          code: JMErrorCodes.UNKNOWN_ERROR,
          message: JMErrorMessages.INVALID_KEY,
        } as JMError;
      }
      await addStringPref(key!, randomNumber.toString());
    } catch {
      throw {
        code: JMErrorCodes.UNKNOWN_ERROR,
        message: 'Error while saving random no',
      } as JMError;
    }
  }

  static async getRandomNumber(key: NullableString): Promise<NullableNumber> {
    try {
      if (isNullOrUndefinedOrEmpty(key)) {
        throw {
          code: JMErrorCodes.UNKNOWN_ERROR,
          message: JMErrorMessages.INVALID_KEY,
        } as JMError;
      }
      const randomNo = await getPrefString(key!);
      return !isNullOrUndefinedOrEmpty(randomNo) ? parseInt(randomNo!) : null;
    } catch (error) {
      JMLogger.log(`Error while fetching random no => ${error}`);
      return null;
    }
  }

  // Clear all keys
  static async clearAllKeys(): Promise<void> {
    try {
      await Promise.all(
        Object.keys(valuesToNotClearOnLogout).map(async key => {
          const value = await getPrefString(key);
          if(value){
            valuesToNotClearOnLogout[
              key as keyof typeof valuesToNotClearOnLogout
            ] = value as string;
          }
        }),
      );
    } catch (error) {
      JMLogger.log('clearAllKeys error' + error);
    }
    try {
      await removePrefAllKeys();
      JMSharedViewModel.Instance.resetViewModelData();
      JMWebGlobalInfo.Instance.setWebUserLogout(true);
    } catch (error) {
      JMLogger.log('clearAllKeys keys:', error);
    }
    try {
      await Promise.all(
        Object.entries(valuesToNotClearOnLogout).map(async ([key, value]) => {
          await addStringPref(key, value);
        }),
      );
    } catch (error) {
      JMLogger.log('clearAllKeys entries added' + error);
    }
  }

  static async setAppLanguage(language: string): Promise<void> {
    try {
      if (isNullOrUndefinedOrEmpty(JMLocalStorageConstants.APP_LANGUAGE)) {
        throw {
          code: JMErrorCodes.UNKNOWN_ERROR,
          message: JMErrorMessages.INVALID_KEY,
        } as JMError;
      }
      await addStringPref(JMLocalStorageConstants.APP_LANGUAGE!, language);
    } catch {
      throw {
        code: JMErrorCodes.UNKNOWN_ERROR,
        message: 'Error while saving app language',
      } as JMError;
    }
  }

  static async getAppLanguage(): Promise<NullableString> {
    try {
      if (isNullOrUndefinedOrEmpty(JMLocalStorageConstants.APP_LANGUAGE)) {
        throw {
          code: JMErrorCodes.UNKNOWN_ERROR,
          message: JMErrorMessages.INVALID_KEY,
        } as JMError;
      }
      return await getPrefString(JMLocalStorageConstants.APP_LANGUAGE!);
    } catch (error) {
      JMLogger.log(`Error while fetching app language => ${error}`);
      return null;
    }
  }
}
