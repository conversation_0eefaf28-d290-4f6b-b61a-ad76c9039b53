import {
  isNullOrUndefinedOrEmpty,
  NullableString,
} from '../../../../jiomart-common/src/JMObjectUtility';
import {
  decryptData,
  encryptData,
  encryptPrimitive,
} from '../dhAlgo/utils/JHCryptUtility';
import {JMStorageService} from './JMStorageService';
import {JMLogger} from '../../../../jiomart-common/src/utils/JMLogger';
import {JMTokenKeyFetcher} from '../helpers/JMTokenKeyFetcher';
import {JMDatabaseManager} from '../../db/JMDatabaseManager';
import {JMBaseUrlKeys} from '../../JMEnvironmentConfig';

export const encryptedData = async (
  data: any,
  platform = JMBaseUrlKeys.LEGACY,
  customSecretKey?: string,
  excludeKeysForEncryption?: string[],
  multipartPayload?: FormData,
): Promise<any> => {
  const secretKey = customSecretKey
    ? customSecretKey
    : await JMStorageService.getSecretKey(
        JMTokenKeyFetcher.getKeyForSecretKey(platform),
      );
  JMLogger.log(
    'fetchLegacyAccessToken encryptedData ' +
      secretKey +
      ' platform ' +
      platform,
  );
  if (isNullOrUndefinedOrEmpty(secretKey)) {
    return null;
  }
  JMLogger.log(platform === JMBaseUrlKeys.LEGACY ? data : JSON.stringify(data));
  let unEncryptedParams: {[key: string]: any} = {};
  if (excludeKeysForEncryption) {
    excludeKeysForEncryption.forEach(key => {
      unEncryptedParams[key] = data[key];
    });
  }

  if (multipartPayload) {
    return encryptDataForMultipart(
      multipartPayload,
      data,
      platform,
      secretKey,
      unEncryptedParams,
    );
  }

  return platform === JMBaseUrlKeys.LEGACY
    ? {...(await encryptData(data, secretKey!)), ...unEncryptedParams}
    : encryptPrimitive(data, secretKey!);
};

/**
 * @param multipartPayload : if this param is not undefined/null then its a multipart request.
 * We dont encrypt the actual multipart param.
 * All the other params that are needed to be encrypted will be encrypeted first and then added to the
 * multipartPayload for the multipart request.
 */
const encryptDataForMultipart = async (
  multipartPayload: FormData,
  data: any,
  platform = JMBaseUrlKeys.LEGACY,
  secretKey: NullableString,
  unEncryptedParams: {[key: string]: any},
): Promise<any> => {
  const encryptedBodyParams =
    platform === JMBaseUrlKeys.LEGACY
      ? {...(await encryptData(data, secretKey!)), ...unEncryptedParams}
      : encryptPrimitive(data, secretKey!);

  // Loop through all properties of the data object
  Object.entries(encryptedBodyParams).forEach(([key, value]) => {
    multipartPayload.append(key, value);
  });
  JMLogger.log(
    'Form-data params after encryption :' + JSON.stringify(multipartPayload),
  );
  return multipartPayload;
};

export const decryptedData = async (
  data: any,
  platform = JMBaseUrlKeys.LEGACY,
): Promise<any> => {
  const secretKey = await JMStorageService.getSecretKey(
    JMTokenKeyFetcher.getKeyForSecretKey(platform),
  );
  JMLogger.log(
    'fetchLegacyAccessToken decryptedData ' +
      secretKey +
      ' platform ' +
      platform,
  );
  if (isNullOrUndefinedOrEmpty(secretKey)) {
    return null;
  }
  const response = decryptData(data, secretKey!);
  JMLogger.log(response);
  return response;
};

export const encryptedDataWithAuthKey = async (data: string): Promise<any> => {
  const user = await JMDatabaseManager.getUser();
  if (isNullOrUndefinedOrEmpty(user)) {
    return null;
  }
  return encryptPrimitive(data, user!.auth_key!);
};
