import {isNullOrUndefinedOrInvalidNumber} from '../../../../jiomart-common/src/JMObjectUtility';
import {JMTokenKeyFetcher} from '../helpers/JMTokenKeyFetcher';
import {JMError, JMErrorCodes} from './JMErrorCodes';
import {JMStorageService} from './JMStorageService';
import {JMNetworkRequestConstants} from '../constants/JMNetworkRequestConstants';
import {JMBaseUrlKeys} from '../../JMEnvironmentConfig';

export class JMTokenValidity {
  static isTokenValid = async (platform = JMBaseUrlKeys.BIFROST) => {
    if (platform === JMBaseUrlKeys.LEGACY) {
      throw {
        code: JMErrorCodes.UNKNOWN_ERROR,
        message: 'No need to check ttl for legacy apis',
      } as JMError;
    }
    const ttlKey = JMTokenKeyFetcher.getKeyForTTL(platform);
    const ttl = await JMStorageService.getTTL(ttlKey);
    if (!isNullOrUndefinedOrInvalidNumber(ttl)) {
      return false;
    }
    const currentTime = new Date().getTime();
    return (
      ttl! - currentTime > JMNetworkRequestConstants.TTL_EXPIRATION_THRESHOLD
    );
  };
}
