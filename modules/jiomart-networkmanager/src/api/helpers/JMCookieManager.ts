import CookieManager, {type Cookies} from '@react-native-cookies/cookies';
import {getBaseURL} from '../../JMEnvironmentConfig';

export default class JMCookieManager {
  private static getDomain() {
    return getBaseURL();
  }

  static async setCookies(
    cookies: Cookies,
    options: Partial<{
      url: string;
      forceUpdate: boolean;
    }> = {},
  ) {
    const defaultDomain = options?.url ? options?.url : this.getDomain();
    const existingCookies = await CookieManager.get(defaultDomain);

    for (const [name, config] of Object.entries(cookies)) {
      const existing = existingCookies[name];

      const shouldUpdate =
        options.forceUpdate ||
        !existing?.value ||
        existing.value !== config.value;

      if (shouldUpdate) {
        await CookieManager.set(defaultDomain, {
          name,
          value: config.value,
          domain: config.domain || defaultDomain,
          path: config.path || '/',
          secure: config.secure ?? true,
          httpOnly: config.httpOnly ?? true,
          expires:
            config.expires ||
            new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        });
      }
    }
  }

  static async getCookieHeader(keys: string[], url?: string) {
    const domain = url ? url : this.getDomain();
    const cookies = await CookieManager.get(domain);
    const filtered = keys
      .filter(key => cookies[key])
      .map(key => `${key}=${cookies[key].value}`)
      .join('; ');
    return {Cookie: filtered};
  }

  static async isCookiesSet(keys: string[], url?: string): Promise<boolean> {
    const domain = url ? url : this.getDomain();
    const cookies = await CookieManager.get(domain);
    return keys.every(key => cookies[key]?.value);
  }

  static async removeCookies(keys: string[], url?: string) {
    const domain = url ? url : this.getDomain();
    for (const key of keys) {
      await CookieManager.clearByName(domain, key);
    }
  }

  static async flushAll() {
    await CookieManager.clearAll();
  }
}
