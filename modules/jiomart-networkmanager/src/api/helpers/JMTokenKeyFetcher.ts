import {NullableString} from '../../../../jiomart-common/src/JMObjectUtility';
import {JMLocalStorageConstants} from '../constants/JMNetworkRequestConstants';
import {JMDhLocalStorageConstants} from '../dhAlgo/constants/JMDhKeyConstants';
import {JMBaseUrlKeys} from '../../JMEnvironmentConfig';

export class JMTokenKeyFetcher {
  static getKeyForAccessToken(platform = JMBaseUrlKeys.LEGACY): NullableString {
    switch (platform) {
      case JMBaseUrlKeys.LEGACY:
        return JMLocalStorageConstants.ACCESS_TOKEN;
      case JMBaseUrlKeys.BIFROST:
        return JMLocalStorageConstants.BIFROST_ACCESS_TOKEN;
      case JMBaseUrlKeys.JCP:
        return JMLocalStorageConstants.JCP_ACCESS_TOKEN;
      case JMBaseUrlKeys.BAU:
        return JMLocalStorageConstants.BAU_ACCESS_TOKEN;
      default:
        return null;
    }
  }
  static getKeyForRandomNumber(
    platform = JMBaseUrlKeys.LEGACY,
  ): NullableString {
    switch (platform) {
      case JMBaseUrlKeys.LEGACY:
        return JMDhLocalStorageConstants.RANDOM_NO;
      case JMBaseUrlKeys.BIFROST:
        return JMDhLocalStorageConstants.BIFROST_RANDOM_NO;
      default:
        return null;
    }
  }
  static getKeyForClientPublicKey(
    platform = JMBaseUrlKeys.LEGACY,
  ): NullableString {
    switch (platform) {
      case JMBaseUrlKeys.LEGACY:
        return JMDhLocalStorageConstants.CLIENT_PUBLIC_KEY;
      case JMBaseUrlKeys.BIFROST:
        return JMDhLocalStorageConstants.BIFROST_CLIENT_PUBLIC_KEY;
      default:
        return null;
    }
  }
  static getKeyForSecretKey(platform = JMBaseUrlKeys.LEGACY): NullableString {
    switch (platform) {
      case JMBaseUrlKeys.LEGACY:
        return JMDhLocalStorageConstants.SECRET_KEY;
      case JMBaseUrlKeys.BIFROST:
        return JMDhLocalStorageConstants.BIFROST_SECRET_KEY;
      default:
        return null;
    }
  }
  static getKeyForTTL(platform = JMBaseUrlKeys.LEGACY): NullableString {
    switch (platform) {
      case JMBaseUrlKeys.LEGACY:
        return JMDhLocalStorageConstants.TTL_KEY;
      case JMBaseUrlKeys.BIFROST:
        return JMDhLocalStorageConstants.BIFROST_TTL_KEY;
      default:
        return null;
    }
  }
}
