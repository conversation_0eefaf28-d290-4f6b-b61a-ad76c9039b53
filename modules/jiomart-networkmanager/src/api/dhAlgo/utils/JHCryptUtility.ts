import * as CryptoJS from 'crypto-js';
import {JMDhKeyConstants} from '../constants/JMDhKeyConstants';

const sanitizeCipherText = (text: string): string =>
  text.replace(/[^a-z0-9+/=]/gi, '');

// Encrypts an object, array, or primitive based on the data type
export const encryptData = async (
  data: any,
  secretKey: string,
): Promise<any> => {
  if (typeof data === 'object' && !Array.isArray(data)) {
    return await encryptObject(data, secretKey);
  }
  if (Array.isArray(data)) {
    return await encryptArray(data, secretKey);
  }
  return encryptPrimitive(data, secretKey);
};

const encryptObject = async (
  obj: Record<string, any>,
  secretKey: string,
): Promise<Record<string, string>> => {
  const encryptedObject: Record<string, string> = {};
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const encryptedValue = await encryptPrimitive(obj[key], secretKey);
      encryptedObject[key] = encryptedValue;
    }
  }

  return encryptedObject;
};

const encryptArray = async (
  arr: any[],
  secretKey: string,
): Promise<string[]> => {
  const encryptedArray: string[] = [];
  for (const value of arr) {
    const encryptedValue = await encryptPrimitive(value, secretKey);
    encryptedArray.push(encryptedValue);
  }
  return encryptedArray;
};

export const encryptPrimitive = async (
  value: any,
  secretKey: string,
): Promise<string> => {
  if (value === null || value === undefined) {
    return '';
  }
  const encrypted = CryptoJS.AES.encrypt(
    CryptoJS.enc.Utf8.parse(String(value)),
    getHash(secretKey),
    getEncryptionOptions(),
  );
  return encrypted.ciphertext.toString(CryptoJS.enc.Base64);
};

export const decryptData = (
  cipher: string,
  secretKey: string,
): any | number => {
  try {
    const bytes = CryptoJS.AES.decrypt(
      sanitizeCipherText(cipher),
      getHash(secretKey),
      getEncryptionOptions(),
    );
    const decryptedText = bytes.toString(CryptoJS.enc.Utf8);
    if (decryptedText === null || decryptedText === undefined) {
      return decryptedText;
    }
    return parseDecryptedData(String(decryptedText));
  } catch (error) {
    console.error('Decryption error:', error);
    return undefined;
  }
};

const getHash = (key: string): CryptoJS.lib.WordArray => CryptoJS.SHA256(key);

const getEncryptionOptions = () => ({
  iv: CryptoJS.enc.Hex.parse(JMDhKeyConstants.IV_VALUE),
});

const parseDecryptedData = (decryptedText: string): any | number => {
  try {
    return JSON.parse(decryptedText);
  } catch (e) {
    if (!isNaN(Number(decryptedText))) {
      return Number(decryptedText);
    }
    return decryptedText;
  }
};
