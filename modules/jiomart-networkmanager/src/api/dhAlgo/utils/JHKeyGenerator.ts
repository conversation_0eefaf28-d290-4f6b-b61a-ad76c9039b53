import {JMDhKeyConstants} from '../constants/JMDhKeyConstants';

const prime = BigInt(JMDhKeyConstants.P_VALUE);
const generator = BigInt(JMDhKeyConstants.G_VALUE);

const generateRandomNumber = (): number => {
  return (
    Math.floor(
      Math.random() *
        (JMDhKeyConstants.MAX_VALUE_RANDOM_NO -
          JMDhKeyConstants.MIN_VALUE_RANDOM_NO +
          1),
    ) + JMDhKeyConstants.MIN_VALUE_RANDOM_NO
  );
};

const generatePublicKey = (randomNo: number): bigint => {
  return generator ** BigInt(randomNo) % prime;
};

export const generatePublicKeyAndRandomNumber = (): {
  public_key: bigint;
  randomNo: number;
} => {
  const random = generateRandomNumber();
  const public_key = generatePublicKey(random);
  return {public_key: public_key, randomNo: random};
};

export const computeSecretKey = (
  serverPublicKey: bigint,
  randomNo: number,
): bigint => {
  return serverPublicKey ** BigInt(randomNo) % prime;
};
