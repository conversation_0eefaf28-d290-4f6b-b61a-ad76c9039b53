import {PayloadAction, createSlice} from '@reduxjs/toolkit';

export interface JMFileState {
  serverFileVersionData: any | null;
  baseServerPath: string | '';
  deeplinkUrl: string | '';
}

const intitialVersionData: JMFileState = {
  serverFileVersionData: null,
  baseServerPath: '',
  deeplinkUrl: '',
};

const JMFileSlice = createSlice({
  name: 'FileVersion',
  initialState: intitialVersionData,
  reducers: {
    setServerFileData: (state, action: PayloadAction<any>) => {
      state.serverFileVersionData = action.payload;
      return state;
    },
    setbaseURL: (state, action: PayloadAction<string>) => {
      state.baseServerPath = action.payload;
      return state;
    },
    setDeeplinkUrl: (state, action: PayloadAction<string>) => {
      state.deeplinkUrl = action.payload;
      return state;
    },
  },
});

export const {setServerFileData, setbaseURL, setDeeplinkUrl} =
  JMFileSlice.actions;

export default JMFileSlice.reducer;
