import {
  getPrefString,
  addStringPref,
} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import axios, {AxiosResponse} from 'axios';
import {setServerFileData} from './JMFileSlice';
import {JMLogger} from '../../../jiomart-common/src/utils/JMLogger';
import {JMConfigFileName} from './JMFileName';
import {
  JMConfigNames,
  JMBaseUrlKeys,
  getAwsConfigPath,
  getJioMartEnvironmentConfig,
} from '../JMHeliosNetworkManager/JMEnvironmentConfig';
import {isNullOrUndefinedOrEmpty} from '../../../jiomart-common/src/JMObjectUtility';
import {getAssetConfigFileData} from './utils/JMAssetConfigFileUtility';
import {Platform} from 'react-native';
import store from '../../../jiomart-main/src/store/store';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import { JMSharedViewModel } from '../../../jiomart-common/src/JMSharedViewModel';

export interface JMFileData {
  version?: string | '';
  data?: any | null;
}

export interface JMConfigFileDataOption {
  sourceType?: AppSourceType;
}

function getAwsUrl(fileName: string): string {
  return (
    getJioMartEnvironmentConfig()[JMBaseUrlKeys.AWS_STORAGE] +
    '/' +
    getAwsConfigPath()[JMConfigNames.CONFIG_FILES] +
    '/' +
    fileName +
    '.json'
  );
}

export async function getConfigDataFromServer(configUrl: string): Promise<any> {
  JMLogger.log(
    `busiCode request url (${new Date().toISOString()}):` + configUrl,
  );
  const response: AxiosResponse<any> = await axios.get<AxiosResponse<any>>(
    configUrl
  );
  JMLogger.log(
    `busiCode response filename (${new Date().toISOString()}): ` +
      configUrl +
      ' response ' +
      JSON.stringify(response.data).length,
  );
  return response.data;
}

export function getConfigDataFromStorage(
  fileName: string,
  resultCallback: (data?: any) => void,
) {
  getPrefString(fileName)
    .then(data => {
      if (data) {
        let jsonData = JSON.parse(data);
        if (jsonData) {
          resultCallback(jsonData['data']);
        }
      } else {
        resultCallback(null);
      }
    })
    .catch(() => {
      resultCallback(null);
    });
}

export function getConfigDataFromAssetFile(
  fileName: string,
  resultCallback: (data?: any) => void,
) {
  resultCallback(getAssetConfigFileData(fileName));
}

/**
 * Converts version string to comparable number
 * Supports any number of parts: "1.2", "1.2.3", "1.2.3.4", etc.
 * Each part gets weighted by powers of 1000
 */
function versionToNumber(version: string): number {
  return parseFloat(version)
  // const parts = version.split('.').map(Number);
  // let result = 0;
  
  // // Calculate weight for each part (rightmost part has weight 1, next has 1000, etc.)
  // for (let i = 0; i < parts.length; i++) {
  //   const weight = Math.pow(100, parts.length - 1 - i);
  //   result += (parts[i] || 0) * weight;
  // }
  
  // return result;
}

const checkConfigFileServerCallRequired = async (fileName: string) => {
  let existingConfigFileDataString = await getPrefString(fileName);
  let existingConfigFileVersion = '0.0';
  if (existingConfigFileDataString) {
    // console.log('fileVersionConfigData 2 -- ', existingConfigFileDataString);
    const jsonObject = JSON.parse(existingConfigFileDataString ?? '');
    existingConfigFileVersion = fromFileDataToJSON(jsonObject).version ?? '0.0';
  }
  let versionFileData = getVersionConfigFileDataFromRedux();
  if (versionFileData) {
    // console.log('fileVersionConfigData 3 -- ', versionFileData);
    let jsonData = JSON.parse(versionFileData);
    let serverVersion = (jsonData[fileName] as string) || '0.0';
    
    JMLogger.log(
      'version fileName' + fileName + " serverVersion " + serverVersion + ' existingConfigFileVersion ' + existingConfigFileVersion + "data" + jsonData[fileName],
    );
    
    if (isNullOrUndefinedOrEmpty(jsonData[fileName])) {
      // console.log('fileVersionConfigData 4 -- ', 1);
      return '0.1';
    }
    
    const serverVersionNum = versionToNumber(serverVersion);
    const existingVersionNum = versionToNumber(existingConfigFileVersion);
    return serverVersionNum > existingVersionNum ? serverVersion : null;
  } else {
    let fileVersionConfigData = await getPrefString(
      JMConfigFileName.JMVersionFileName,
    );
    // console.log('fileVersionConfigData -- ', fileVersionConfigData + "fileName "+fileName);
    if (fileVersionConfigData) {
      let jsonData = JSON.parse(fileVersionConfigData);
      let serverVersion = (jsonData[fileName] as string) || '0.0';
      
      if (isNullOrUndefinedOrEmpty(jsonData[fileName])) {
        return '0.1';
      }
      
      const serverVersionNum = versionToNumber(serverVersion);
      const existingVersionNum = versionToNumber(existingConfigFileVersion);
      return serverVersionNum > existingVersionNum ? serverVersion : null;
    }
  }
};

function getVersionConfigFileDataFromRedux() {
  const serverFileData = store.getState().fileVersion.serverFileVersionData;
  if (serverFileData) {
    return serverFileData;
  }
}

function insertConfigDataInStorage(
  fileName: string,
  data: any,
  fileVersionNumber: string,
) {
  const fileModel: JMFileData = {
    version: fileVersionNumber,
    data: data,
  };
  let jsonString = JSON.stringify(fileModel);
  addStringPref(fileName, jsonString);
}

function fromFileDataToJSON(json: any): JMFileData {
  return {
    version: json['version'],
    data: json['data'],
  };
}

//wrap promise callVersion file

export const callVersionFileAsync = (fileName: string) => {
  JMSharedViewModel.Instance.fileVersionCalledPromise =  new Promise((resolve, reject) => {
    callVersionFile(fileName, isSuccess => {
      if (isSuccess) {
        resolve(true);
      } else {
        resolve(false);
      }
    });
  });
};

// version file logic
export async function callVersionFile(
  fileName: string,
  resultCallback: (isSuccess: any) => void,
) {
  let configUrl = getAwsUrl(fileName);
  getConfigDataFromServer(configUrl)
    .then(data => {
      let jsonString = JSON.stringify(data);
      JMLogger.log(
        'callVersionFile getConfigDataFromServer fileName ' +
          fileName +
          ' data ' +
          jsonString,
      );
      if (data) {
        store.dispatch(setServerFileData(jsonString));
        addStringPref(fileName, jsonString);
        resultCallback(true);
      } else {
        setFileVersionConfigDataInRedux();
        resultCallback(false);
      }
    })
    .catch(() => {
      setFileVersionConfigDataInRedux();
      resultCallback(false);
    });
}

async function setFileVersionConfigDataInRedux() {
  getPrefString(JMConfigFileName.JMVersionFileName).then(data => {
    if (data) {
      store.dispatch(setServerFileData(data));
    }
  });
}
// end region of version file logic

// get config file data from server and then async storage and then asset file
export async function getConfigFileDataDbAsync(fileName: string) {
  return new Promise(resolve => {
        getConfigDataFromStorage(fileName, configStorageData => {
          if (configStorageData) {
            // JMLogger.log(
            //   'getConfigFileData getConfigDataFromStorage fileName ' +
            //     fileName +
            //     ' data ' +
            //     JSON.stringify(configStorageData),
            // );
            resolve(configStorageData);
          } else {
            getConfigDataFromAssetFile(fileName, configAssetData => {
              resolve(configAssetData);
            });
          }
        });
  });
}


// async methods

// Convert callback-based functions to return Promises
export async function getConfigDataFromStorageAsync(fileName: string): Promise<any> {
  const data = await getPrefString(fileName)
  if (data) {
    let jsonData = JSON.parse(data);
    if (jsonData) {
      return jsonData['data'];
    } else {
      return null;
    }
  } else {
    return null;
  }
}

export async function getConfigDataFromAssetFileAsync(fileName: string): Promise<any> {
  return await getAssetConfigFileData(fileName)
}

export async function readConfigFileFromServerAsync(fileName: string): Promise<any> {
  const newVersion = await checkConfigFileServerCallRequired(fileName);
  // console.log('newVersion -- ', newVersion, fileName);
  if (newVersion && (typeof newVersion === 'string')) {
    let configUrl = getAwsUrl(fileName);
    console.log('URL ---  ', configUrl);
    try {
      const data = await getConfigDataFromServer(configUrl);
      let jsonData = data;
      if (jsonData) {
        insertConfigDataInStorage(
          fileName,
          jsonData,
          newVersion,
        );
        return jsonData;
      } else {
        return null;
      }
    } catch {
      return null;
    }
  } else {
    return null;
  }
}

function printFileNameJsonFromServer(fileNamesList: string[], fileName: string, configServerData: any){
    if(fileNamesList?.includes(fileName)){
      JMLogger.log(
        'printFileNameJsonFromServer fileName ' +
          fileName +
          JSON.stringify(configServerData),
      );
    }
    else{
      // JMLogger.log(
      //   'printFileNameJsonFromServer fileName ' +
      //     fileName +
      //     configServerData,
      // );
    }
}

// Updated getConfigFileDataAsync to properly work with async/await
export async function getConfigFileDataAsync(fileName: string, options?: JMConfigFileDataOption): Promise<any> {
  try {
    JMLogger.log("getConfigFileDataAsync method called filename "+fileName)
    if(JMSharedViewModel.Instance.fileVersionCalledPromise === null){
       callVersionFileAsync(JMConfigFileName.JMVersionFileName)
    }
    await JMSharedViewModel.Instance.fileVersionCalledPromise
    // First try to get data from server
    const configServerData = await readConfigFileFromServerAsync(fileName);
    printFileNameJsonFromServer([""], fileName, configServerData)
    if (configServerData) {
      return configServerData;
    }

    // If server data not available, try storage
    const configStorageData = await getConfigDataFromStorageAsync(fileName);
    if (configStorageData) {
      // JMLogger.log(
      //   'getConfigFileData getConfigDataFromStorage fileName ' +
      //     fileName +
      //     ' data ' +
      //     JSON.stringify(configStorageData),
      // );
      return configStorageData;
    }

    // If storage data not available, try asset file
    const configAssetData = await getConfigDataFromAssetFileAsync(fileName);
    return configAssetData;
  } catch (error) {
    JMLogger.log('Error in getConfigFileDataAsync: ' + error);
    return null;
  }
}
