{"headerType": 9, "scrollHeaderType": 6, "isDeliverToBarShow": true, "showBNB": false, "sort": {"isVisible": true, "text": {"title": "By [TITLE]", "color": "primary_grey_80"}, "icon": {"ic": "IcChevronDown", "size": "small", "color": "primary_grey_80"}, "leftIcon": {"ic": "IcSort", "size": "small", "color": "primary_grey_80"}}, "brand": {"isVisible": true, "text": {"title": "Brands", "color": "primary_grey_80"}, "icon": {"ic": "IcChevronDown", "size": "small", "color": "primary_grey_80"}}, "filter": {"isVisible": true, "text": {"title": "Filters", "color": "primary_grey_80"}, "leftIcon": {"ic": "IcFilterMultiple", "size": "small", "color": "primary_grey_80"}, "icon": {"ic": "IcChevronDown", "size": "small", "color": "primary_grey_80"}}, "disableFabIcon": false, "disableCategoryName": false, "addToCartVerticalCode": ["ELECTRONICS"], "addToCart": {"isVisible": true, "title": "Add"}, "wishlistVerticalCode": [], "wishlist": {"isVisible": true}, "pdpVerticalCode": ["GROCERIES", "JEWELLERY", "WELLNESS", "FURNITURE"], "gridCard": {"effectivePriceColor": "primary_grey_100", "markedPriceColor": "primary_grey_60", "title": {"color": "primary_grey_80", "maxLine": 2}, "multiVariant": {"total": "[TOTAL] Options", "totalColor": "primary_grey_80", "title": {"color": "primary_60"}, "icon": {"ic": "IcChevronDown", "size": "small", "color": "primary_60"}}, "outOfStock": {"text": "Out of Stock", "color": "feedback_error_50"}, "discount": {"color": "secondary_50"}, "offer": {"title": "[OFFER] offers available"}, "options": {"title": "[TITLE] Options", "color": "primary_60", "icon": {"ic": "IcChevronDown", "color": "primary_60", "size": "small"}}}, "hyperLocal": {"show": true, "text": "Quick \nDelivery", "image": "https://cdn.pixelbin.io/v2/jiomart-fynd/jminfi/t.resize(w:250)/images/othe/egurhk/.j/pg/bdel-lime-floor-cleaner-5-l-0-20210621.jpg.5692fecc85.jpg", "textColor": "sparkle_70", "backgroundColor": "#E5F7EE", "primaryText": "Delivery", "secondaryText": "in 10 to 30 mins", "iconHeight": 10, "iconWidth": 40}, "scheduleDelivery": {"show": true, "text": "Scheduled Delivery By", "delay": 432000000, "backgroundColor": "#E5F1F7", "textColor": "sparkle_70"}, "jioAds": {"topAds": [{"adHeight": 360, "adWidth": 720, "adjHeight": 180, "adSpotID": "pvkl06ij", "isVisible": false, "adType": 5}, {"adHeight": 360, "adWidth": 720, "adjHeight": 180, "adSpotID": "e5pf3c1i", "isVisible": false, "adType": 5}, {"adHeight": 720, "adWidth": 720, "adjHeight": 360, "adSpotID": "aqgnkyp5", "isVisible": false, "adType": 5}, {"adHeight": 180, "adWidth": 720, "adjHeight": 90, "adSpotID": "6p1jge23", "isVisible": false, "adType": 5}], "middleAds": [{"adHeight": 360, "adWidth": 720, "adjHeight": 180, "adSpotID": "pvkl06ij", "isVisible": false, "position": 4, "repetitive": false, "adType": 5}, {"adHeight": 720, "adWidth": 720, "adjHeight": 360, "adSpotID": "6p1jge23", "isVisible": false, "position": 5, "repetitive": false, "adType": 5}]}, "negativeCases": {"noItemFound": {"imageUrl": "https://myjiostatic.cdn.jio.com/JioMart/Common/qna.jpg", "title": "Oops! Category Unavailable", "subTitle": "Products in this category are not serviceable at your location.", "buttonTitle": "Explore Categories"}}, "bottomSheet": {"sort": {"headerTitle": "Sort"}, "filter": {"headerTitle": "Filter by", "notFound": "No results found", "shouldShowSearchBar": 10, "filterKey": {"DEPARTMENT": "departments", "CATEGORY_L1": "category-l1", "CATEGORY_L2": "category-l2", "BRAND": "brand"}, "removeFilterValues": {"slp": ["category-l1", "category-l2"], "plp": ["departments", "category-l1", "category-l2"]}, "showFilterValues": {"slp": [], "plp": [], "gridPlp": ["l3_category_names", "brand", "availability", "min_price_effective"]}, "button": {"clearAll": {"isVisible": true, "title": "Clear All"}, "apply": {"isVisible": true, "title": "Apply"}}}, "brand": {"headerTitle": "Brand", "notFound": "No results found", "shouldShowSearchBar": 10, "filterKey": {"DEPARTMENT": "departments", "CATEGORY_L1": "category-l1", "CATEGORY_L2": "category-l2", "BRAND": "brand"}, "removeFilterValues": {"slp": ["category-l1", "category-l2"], "plp": ["departments", "category-l1", "category-l2"]}, "showFilterValues": {"slp": [], "plp": [], "gridPlp": ["l3_category_names", "brand", "availability", "min_price_effective"]}, "button": {"clearAll": {"isVisible": true, "title": "Clear All"}, "apply": {"isVisible": true, "title": "Apply"}}}, "multiVariant": {"headerTitle": "Select Size", "button": {"done": {"isVisible": true, "title": "Done"}}}}}