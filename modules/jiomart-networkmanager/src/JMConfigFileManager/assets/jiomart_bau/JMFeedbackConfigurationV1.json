{"ratingAndReviewForm": {"headerType": 1, "isDeliverToBarShow": false, "showBNB": false, "title": "Rate & Review", "RateYourProduct": {"title": "Rate Your Products"}, "WriteATitle": {"title": "Write a title", "placeholder": "Give a suitable title for your review", "message": "[CHAR]/[MAX][ERROR]", "regex": "[^a-zA-Z0-9 \\n]| {2,}|\\n{2,}", "subText": {"isVisible": true, "icon": {"ic": "IcLightbulb", "isVisible": true}, "title": "You can write about Ease of use, Value of money, Build quality, Charging, etc."}, "validation": {"maxCharLimit": ": Maximum of [CHAR] characters required", "minCharLimit": ": Minimum of [CHAR] characters required", "required": ": Please enter your title!"}}, "ShareAReview": {"title": "Share a Review", "placeholder": "What would you like other customers to know?", "message": "[CHAR]/[MAX][ERROR]", "regex": "[^a-zA-Z0-9 \\n]| {2,}|\\n{2,}", "subText": {"isVisible": true, "icon": {"ic": "IcLightbulb", "isVisible": true}, "title": "You can write about Ease of use, Value of money, Build quality, Charging, etc."}, "validation": {"maxCharLimit": ": Maximum of [CHAR] characters required", "minCharLimit": ": Minimum of [CHAR] characters required", "required": ": Please enter your title!"}}, "AddPhotosBlock": {"title": "Add photos", "titleTag": "(Optional)", "subtitle": "Photos make review more helpful.", "UploadBtnTitle": "Upload images", "ButtonTag": "Add up to [LENGTH_LIMIT] images (JPEG, PNG, JPG, max [SIZE_LIMIT]MB each)", "sizeLimitErrorToast": "Please upload file upto size [SIZE_LIMIT] MB"}, "FAQSection": {"submitBtnTitle": "Submit", "FAQPrompt": "Your review matters! Find guidance on writing reviews in our", "FAQSection": "FAQ Section", "EditToast": "Thank you for the review. Your review will reflect soon.", "cta": {"navTitle": "FAQs", "source": "", "destination": "CommonWebViewScreen", "actionType": "T003", "actionUrl": "/faqs", "bundle": "", "headerType": 1, "navigationType": "push"}}, "bottomSheet": {"continueReview": {"headerTitle": "Your review matters", "modalText": "If you go back, all your written information will be lost. Continue reviewing?", "goBackBtn": {"visible": true, "title": "Go back"}, "continueBtn": {"visible": true, "title": "Continue"}}}}, "ratingAndReviewSuccess": {"headerType": 2, "isDeliverToBarShow": false, "showBNB": false, "statusBarColor": "#E5F7EE", "successMsg": {"title": "Thank you so much 🎉", "subTitle": "We are processing your review. This might take several days, so we appreciate your patience. We will email you when this is complete."}, "reviewDetails": {"isVisible": true, "title": "Your review"}, "doneBtn": {"visible": true, "title": "Done"}}}