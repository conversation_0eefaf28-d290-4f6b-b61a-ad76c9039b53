{"searchConfig": [{"rnVersion": "", "iosVersion": "", "androidVersion": "", "jiomart_header": {"header_type": 8}, "slpContent": [{"viewType": 101, "showElement": true, "orderNo": 1, "title": "Got a Shopping List?", "subTitle": "Search multiple products together.", "rnVersion": "", "iosVersion": "", "androidVersion": ""}, {"viewType": 102, "showElement": true, "orderNo": 2, "title": "Your recent searches", "visibleSearchKeywordCount": 10, "actionContentItems": [{"showElement": true, "title": "Clear All"}], "cta": {"actionType": "T003", "destination": "CommonWebViewScreen", "headerVisibility": 2, "navigationType": "replace", "loginRequired": false, "actionUrl": "/search/[QUERY]", "headerType": 9, "shouldShowDeliverToBar": true, "abTestingparams": [{"abTestingKey": "searchApi", "urlKey": "searchApi", "defaultValue": "none"}, {"abTestingKey": "results_algolia", "urlKey": "results_algolia", "defaultValue": "none"}]}, "rnVersion": "", "iosVersion": "", "androidVersion": ""}, {"viewType": 103, "showElement": true, "orderNo": 3, "title": "Try something new", "visibleSearchKeywordCount": 10, "cta": {"actionType": "T003", "destination": "CommonWebViewScreen", "headerVisibility": 2, "navigationType": "replace", "loginRequired": false, "actionUrl": "/search/[QUERY]", "headerType": 9, "shouldShowDeliverToBar": true, "abTestingparams": [{"abTestingKey": "searchApi", "urlKey": "searchApi", "defaultValue": "none"}, {"abTestingKey": "results_algolia", "urlKey": "results_algolia", "defaultValue": "none"}]}, "rnVersion": "", "iosVersion": "", "androidVersion": ""}, {"viewType": 104, "showElement": true, "orderNo": 5, "title": "Most Trending Categories", "default_image": "", "categoryViewContent": [{"Key": "GROCERIES", "IconAsset": "ic_grocery", "IconURL": "https://jep-asset.akamaized.net/JioMart/Common/JDSgrocery.png", "ActionWebURL": "c/groceries/2", "Title": "Grocery"}, {"Key": "FASHION", "IconAsset": "ic_apparel", "IconURL": "https://jep-asset.akamaized.net/JioMart/Common/JDSfashion.png", "ActionWebURL": "sections/fashion", "Title": "Fashion"}, {"Key": "JEWELLERY", "IconAsset": "ic_jewellery", "IconURL": "https://jep-asset.akamaized.net/JioMart/Common/JDSjewelry.png", "ActionWebURL": "c/jewellery/1524", "Title": "Jewellery"}, {"Key": "BEAUTY", "IconAsset": "ic_beauty", "IconURL": "https://jep-asset.akamaized.net/JioMart/Common/JDSbeauty.png", "ActionWebURL": "c/beauty/5", "Title": "Beauty", "versionNumber": "1.0.10"}, {"ActionWebURL": "c/electronics/4", "IconAsset": "ic_digital", "IconURL": "https://jep-asset.akamaized.net/JioMart/Common/JDSelectronics.png", "Key": "ELECTRONICS", "Title": "Electronics"}, {"ActionWebURL": "c/premiumfruits/4543", "IconAsset": "ic_premiumfruits", "IconURL": "https://jep-asset.akamaized.net/JioMart/Common/JDSpremium_fruits.png", "Key": "PREMIUMFRUITS", "Title": "Premium fruits"}, {"ActionWebURL": "c/homeandkitchen/1591", "IconAsset": "ic_home_kitchen", "IconURL": "https://jep-asset.akamaized.net/JioMart/Common/JDShome_ktchen.png", "Key": "HOMEANDKITCHEN", "Title": "Home & Kitchen"}], "cta": {"actionType": "T003", "destination": "CommonWebViewScreen", "headerVisibility": 2, "navigationType": "replace", "loginRequired": false, "actionUrl": "/[SLUG]", "headerType": 9, "shouldShowDeliverToBar": true, "abTestingparams": [{"abTestingKey": "searchApi", "urlKey": "searchApi", "defaultValue": "none"}, {"abTestingKey": "results_algolia", "urlKey": "results_algolia", "defaultValue": "none"}]}, "rnVersion": "", "iosVersion": "", "androidVersion": ""}, {"viewType": 105, "showElement": true, "orderNo": 4, "title": "Recommended for you", "default_image": "", "maxItemsVisible": 10, "is_buy_again_appear_first": true, "is_buy_again_api_enabled": true, "is_recommended_product_api_enabled": true, "rnVersion": "", "iosVersion": "", "androidVersion": "", "cta": {"actionType": "T003", "destination": "CommonWebViewScreen", "headerVisibility": 2, "navigationType": "push", "loginRequired": false, "actionUrl": "", "shouldShowDeliverToBar": true, "headerType": 9, "abTestingparams": [{"abTestingKey": "searchApi", "urlKey": "searchApi", "defaultValue": "none"}, {"abTestingKey": "results_algolia", "urlKey": "results_algolia", "defaultValue": "none"}]}}], "sspContent": [{"viewType": 106, "orderNo": 1, "showElement": true, "title": "Categories", "maxItemsVisible": 10, "type": "categories", "borderRadius": 8, "cta": {"actionType": "T003", "destination": "CommonWebViewScreen", "headerVisibility": 2, "navigationType": "replace", "loginRequired": false, "actionUrl": "/search/[QUERY]?stg_smart_master_vertical[refinementList][category_level.level4][0]=[L3_CATEGORY_NAMES]", "headerType": 9, "shouldShowDeliverToBar": true, "abTestingparams": [{"abTestingKey": "searchApi", "urlKey": "searchApi", "defaultValue": "none"}, {"abTestingKey": "results_algolia", "urlKey": "results_algolia", "defaultValue": "none"}]}, "rnVersion": "", "iosVersion": "", "androidVersion": ""}, {"viewType": 106, "orderNo": 2, "showElement": true, "title": "Brands", "maxItemsVisible": 10, "type": "brands", "borderRadius": 80, "cta": {"actionType": "T003", "destination": "CommonWebViewScreen", "headerVisibility": 2, "navigationType": "replace", "loginRequired": false, "actionUrl": "/search/[QUERY]?stg_smart_master_vertical[refinementList][brand][0]=[BRAND]", "headerType": 9, "shouldShowDeliverToBar": true, "abTestingparams": [{"abTestingKey": "searchApi", "urlKey": "searchApi", "defaultValue": "none"}, {"abTestingKey": "results_algolia", "urlKey": "results_algolia", "defaultValue": "none"}]}, "rnVersion": "", "iosVersion": "", "androidVersion": ""}, {"viewType": 107, "orderNo": 3, "showElement": true, "default_image": "", "title": "Products", "maxItemsVisible": 10, "rnVersion": "", "iosVersion": "", "androidVersion": "", "cta": {"actionType": "T003", "destination": "CommonWebViewScreen", "headerVisibility": 2, "navigationType": "push", "loginRequired": false, "actionUrl": "/[SLUG]", "shouldShowDeliverToBar": true, "headerType": 9, "abTestingparams": [{"abTestingKey": "searchApi", "urlKey": "searchApi", "defaultValue": "none"}, {"abTestingKey": "results_algolia", "urlKey": "results_algolia", "defaultValue": "none"}]}}], "searchSuggestion_List": {"is_visible": true, "maxItemsVisible": 4, "cta": {"actionType": "T003", "destination": "CommonWebViewScreen", "headerVisibility": 2, "navigationType": "replace", "loginRequired": false, "actionUrl": "/search/[QUERY]", "headerType": 9, "shouldShowDeliverToBar": true, "abTestingparams": [{"abTestingKey": "searchApi", "urlKey": "searchApi", "defaultValue": "none"}, {"abTestingKey": "results_algolia", "urlKey": "results_algolia", "defaultValue": "none"}]}}, "btm_sheet": {"shopping_list_block": {"is_visible": true, "header_title": "Shopping List", "sub_title": "Search multiple products by entering your shopping list below", "text_input": {"is_visible": true, "label": "Enter your items", "placeholder": "e.g. Milk, Bread, Fruit", "sub_label": "Add comma as separator", "invalid_message": "Please enter text in correct Format*"}, "action_button": [{"is_visible": true, "button_title": "Clear"}, {"is_visible": true, "button_title": "Search All"}], "toast_messages": ["Please enter products to search"], "cta": {"actionType": "T003", "destination": "CommonWebViewScreen", "headerVisibility": 2, "navigationType": "replace", "loginRequired": false, "actionUrl": "/search/[QUERY]", "shouldShowDeliverToBar": true, "headerType": 9, "abTestingparams": [{"abTestingKey": "searchApi", "urlKey": "searchApi", "defaultValue": "none"}, {"abTestingKey": "results_algolia", "urlKey": "results_algolia", "defaultValue": "none"}]}}}, "algoliaSearchConfiguration": {"uiPathID": "M1lQMEhQM1dTSA==", "algoliaEnvironment": "prod_mart_", "algoliaIndexNameForAllCategory": "stg_smart_master_vertical", "cardViewID": "Y2ZiZTk1NDhhNWQ4M2IxZjBjZDY4YTRmY2I5YTM4NGQ=", "algoliaIndexName": "stg_smart_query_suggestions", "algoliaFirstIndexName": "stg_smart_query_suggestions", "algoliaSecondIndexName": "stg_smart_master_vertical", "hitsPerPageForMasterVertical": 5, "hitsPerPageForSearchQuery": 4, "hitsPerPageForDiscoverMoreQuery": 5}}], "qrScannerConfig": [{"rnVersion": "", "iosVersion": "", "androidVersion": "", "is_visible": true, "title": "Camera Access", "subTitle": "To enable faster and easier search via QR code, please provide camera permission", "buttonText": "Enable Camera", "closeIcon": "IcClose", "cameraAccesUrl": "https://myjiostatic.cdn.jio.com/JioMart/Common/jm_ic_camera_access.svg", "flashOffUrl": "https://myjiostatic.cdn.jio.com/JioMart/Common/ic_flash_off.svg", "flashOnUrl": "https://myjiostatic.cdn.jio.com/JioMart/Common/ic_flash.svg", "galleryImageIUrl": "https://myjiostatic.cdn.jio.com/JioMart/Common/ic_photo.svg", "modifiedToastData": {"isVisible": true, "message": "QR not supported. Please try again"}}]}