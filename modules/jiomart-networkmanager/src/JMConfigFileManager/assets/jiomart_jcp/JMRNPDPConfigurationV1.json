{"pdpConfig": [{"isVisibleForVersion": "1", "versionNumber": "3.1.0", "headerType": 9, "isDeliverToBarShow": true, "showBNB": false, "isEligibleEmiVerticalCode": {"ELECTRONICS": 3000, "JEWELLERY": 2000, "GROCERIES": 200, "OTHERS": 200}, "emiPriorityCredit": ["NO_COST_EMI", "LOW_COST_EMI", "STANDARD_EMI"], "emiPriorityDebit": ["NO_COST_EMI", "LOW_COST_EMI", "STANDARD_EMI"], "notAvailableStrip": "Product not available at the selected PIN code", "smartBazzarToShowVerticalCode": ["GROCERIES"], "smartBazzarToShowSellerId": 1, "addToCartVerticalCode": ["ELECTRONICS"], "addToCart": {"isVisible": true, "title": "Add to Cart"}, "buyNow": {"isVisible": true, "title": "Buy Now"}, "exploreCategory": {"isVisible": true, "title": "Explore More!", "subTitle": "View Categories"}, "noProductFound": {"title": "Ooops, Something went wrong!", "subTitle": "Please try refreshing the page, or try again after sometime", "isButtonVisible": true, "buttonTitle": "Try Again"}, "reviewList": {"pageSize": 1, "sortBy": "MOST_RECENT"}, "block": [{"id": 1, "isVisible": true, "type": "PRODUCT_HEADER", "brand": {"isVisible": true, "color": "primary_60", "click": {"openInRN": true, "rnActionScrren": "ProductListingUpdate", "webUrl": "/", "header": {"header_type": 9}, "deeplink": {}}}, "tag": {"isVisible": true, "color": "#FF9DA333", "textColor": "secondary_60"}, "productName": {"isVisible": true, "color": "black"}, "productRating": {"isVisible": true, "color": ""}, "share": {"isVisible": true, "title": "Buy [TITLE] at Best Prices in India - JioMart.", "message": "Buy [TITLE] at Best Prices in India - JioMart. [PRODUCT_URL]"}, "wishlist": {"isVisible": true}, "smartBazzar": {"isVisible": true, "icon": ""}}, {"id": 2, "isVisible": true, "type": "PRODUCT_CAROUSEL", "popup": {"close": "IcClose", "disclamier": "Note: Product image is for illustration purpose only"}}, {"id": 3, "isVisible": true, "type": "PRODUCT_PRICE", "productPrice": {"price": {"color": "primary_grey_100"}}, "markedPrice": "M.R.P: <del>[MARKED_PRICE]</del> (Incl. of all taxes)", "discount": {"color": "#E5F7EE", "textColor": "sparkle_60"}, "emi": {"isVisible": true, "NO_COST_EMICreditTitle": "<icon>IcPayEmiInstall,sparkle_50,medium</icon> No Cost Emi Available from <b>₹[EMI_PRICE]/-</b> per month.", "LOW_COST_EMICreditTitle": "<icon>IcPayEmiInstall,sparkle_50,medium</icon> Low Cost Emi Available from <b>₹[EMI_PRICE]/-</b> per month.", "STANDARD_EMICreditTitle": "<icon>IcPayEmiInstall,sparkle_50,medium</icon> Emi Available from <b>₹[EMI_PRICE]/-</b> per month."}, "paymentTag": {"isVisible": true, "title": "<icon><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,sparkle_50,small</icon> [PAYMENT_TAG]"}, "outOfStock": "Out of Stock", "productNotAvailable": "Currently this product is not available", "sodexo": {"isVisible": true}, "termsAndCondition": {"isVisible": true, "title": "T&C Apply"}}, {"id": 4, "isVisible": true, "type": "PRODUCT_MULTI_VARIANTS", "title": "Select a size [SIZE]"}, {"id": 5, "isVisible": true, "type": "PRODUCT_WARRANTY", "title": "Warranty : [WARRANTY]"}, {"id": 6, "isVisible": true, "type": "PRODUCT_OFFER", "title": "Offers ([OFFER])", "block": [{"isVisible": true, "type": "COUPON", "icon": {"ic": "IcCoupon", "color": "sparkle_50", "size": "small", "mode": "", "iconUrl": ""}, "title": "Coupons", "subTitle": "", "description": "[COUPON] Offers available", "rightIcon": {"ic": "IcChevronRight", "color": "primary_60", "size": "medium", "mode": "", "iconUrl": ""}}, {"isVisible": true, "type": "BANK_OFFER", "icon": {"ic": "IcBankBranch", "color": "sparkle_50", "size": "small", "mode": "", "iconUrl": ""}, "title": "Bank Offers", "subTitle": "Save more with your preferred bank!", "description": "[BANK_OFFER] Offers available", "rightIcon": {"ic": "IcChevronRight", "color": "primary_60", "size": "medium", "mode": "", "iconUrl": ""}}, {"isVisible": true, "type": "ADDITIONAL_OFFER", "icon": {"ic": "IcOfferCoupon", "color": "sparkle_50", "size": "small", "mode": "", "iconUrl": ""}, "title": "Additional Offers", "subTitle": "Get massive savings with coupons", "description": "[ADDITIONAL_OFFER] Offers available", "rightIcon": {"ic": "IcChevronRight", "color": "primary_60", "size": "medium", "mode": "", "iconUrl": ""}}, {"isVisible": true, "type": "LIMITED_DEALS", "icon": {"ic": "IcFlash", "color": "sparkle_50", "size": "small", "mode": "", "iconUrl": ""}, "title": "Limited Deals", "subTitle": "Get massive savings with coupons", "description": "[LIMITED_DEALS] Offers available", "rightIcon": {"ic": "IcChevronRight", "color": "primary_60", "size": "medium", "mode": "", "iconUrl": ""}}, {"isVisible": true, "type": "BUY_MORE_SAVE_MORE", "icon": {"ic": "IcOfferCoupon", "color": "sparkle_50", "size": "small", "mode": "", "iconUrl": ""}, "title": "Buy More Save More", "subTitle": "Get massive savings with coupons", "description": "[BUY_NOW_SAVE_MORE] Offers available", "rightIcon": {"ic": "IcChevronRight", "color": "primary_60", "size": "medium", "mode": "", "iconUrl": ""}}, {"isVisible": true, "type": "EMI", "icon": {"ic": "IcPayEmiInstall", "color": "sparkle_50", "size": "small", "mode": "", "iconUrl": ""}, "title": "<PERSON><PERSON>ers", "subTitle": "", "description": "[EMI_OFFERS] Offers available", "rightIcon": {"ic": "IcChevronRight", "color": "primary_60", "size": "medium", "mode": "", "iconUrl": ""}}, {"isVisible": true, "type": "LIMITER_TIME_OFFER", "icon": {"ic": "IcPayEmiInstall", "color": "sparkle_50", "size": "small", "mode": "", "iconUrl": ""}, "title": "<PERSON><PERSON>ers", "subTitle": "", "description": "[EMI_OFFERS] Offers available", "rightIcon": {"ic": "IcChevronRight", "color": "primary_60", "size": "medium", "mode": "", "iconUrl": ""}}], "viewAll": {"isVisible": true, "title": "View All"}, "click": {"openInRN": true, "rnActionScrren": "OffersListScreen", "webUrl": "", "header": {"header_type": 1, "title": "Offers ([OFFER])"}, "deeplink": {}}}, {"id": 7, "isVisible": true, "type": "PRODUCT_EMI", "title": "Save more with easy EMIs", "block": [{"type": "CREDIT", "isVisible": true, "icon": {"ic": "IcCcv", "color": "sparkle_50", "size": "small"}, "NO_COST_EMITitle": "No Cost EMI on Credit Card Available", "LOW_COST_EMITitle": "Low Cost EMI on Credit Card Available", "STANDARD_EMITitle": "Avail EMI on Credit Card", "NO_COST_EMISubTitle": "EMIs from ₹ [EMI] /- Month", "LOW_COST_EMISubTitle": "EMIs from ₹ [EMI] /- Month", "STANDARD_EMISubTitle": "EMIs from ₹ [EMI] /- Month", "rightIcon": {"ic": "IcChevronRight", "color": "primary_60", "size": "medium"}}, {"type": "DEBIT", "isVisible": true, "icon": {"ic": "IcCard", "color": "sparkle_50", "size": "small"}, "title": "Avail EMI on Debit Card", "subTitle": "Congratulations! You’re pre-approved for Debit Card EMI.", "NO_COST_EMITitle": "Avail No Cost EMI on Debit Card​", "LOW_COST_EMITitle": "Low Cost EMI on Debit Card​", "STANDARD_EMITitle": "Avail EMI on Debit Card​", "NO_COST_EMISubTitle": "Congratulations! You’re Pre-Approved for Debit Card EMI. ", "LOW_COST_EMISubTitle": "Congratulations! You’re Pre-Approved for Debit Card EMI. ​", "STANDARD_EMISubTitle": "Congratulations! You’re Pre-Approved for Debit Card EMI. ​", "rightIcon": {"ic": "IcChevronRight", "color": "primary_60", "size": "medium"}}]}, {"id": 24, "isVisible": true, "type": "PRODUCT_SERVICE_LIST", "block": [{"isVisible": true, "type": "WARRANTY", "icon": {"ic": "IcWarranty", "kind": "background", "size": "xxl", "color": "sparkle_50"}, "title": "[WARRANTY] Warranty"}, {"isVisible": true, "type": "RESQ_GUARANTEE", "icon": {"ic": "IcSecured", "kind": "background", "size": "xxl", "color": "sparkle_50"}, "title": "ResQ guarantee"}, {"isVisible": true, "type": "COD", "icon": {"ic": "IcPayFriend", "kind": "background", "size": "xxl", "color": "sparkle_50"}, "title": "Cash On Delivery"}, {"isVisible": true, "type": "SEVENT_DAY_RETURN", "icon": {"ic": "IcReturn", "kind": "background", "size": "xxl", "color": "sparkle_50"}, "title": "[RETURN] return"}, {"isVisible": true, "type": "EXTENDED_WARRANTY", "icon": {"ic": "IcSecured", "kind": "background", "size": "xxl", "color": "sparkle_50"}, "title": "Extended Warranty"}]}, {"id": 8, "isVisible": true, "type": "PRODUCT_DELIVER_TO", "title": "Deliver to", "inStock": "In Stock", "outOfStock": "Out of Stock", "unavailableAtYourLocation": "Unavailable at your location", "notServiceable": "Product is not serviceable at given pincode", "deliveryMessage": "Delivery by [DELIVERY_DATE]", "deliveryDateFormat": "[ddd] [MMM] [DD] [YYYY]"}, {"id": 9, "isVisible": true, "type": "PRODUCT_SOLD_BY", "title": "Sold By", "sellerTextColor": "primary_60", "click": {"openInRN": true, "rnActionScrren": "WebViewScreen", "webUrl": "/c/product-seller?seller=[SELLER_NAME]", "header": {"header_type": 9}, "deeplink": {}}, "smartBazzar": {"isVisible": true, "icon": ""}}, {"id": 10, "isVisible": true, "type": "PRODUCT_KEY_FEATURE", "title": "Key Features"}, {"id": 11, "isVisible": true, "type": "PRODUCT_DESCRIPTION", "title": "Description"}, {"id": 12, "isVisible": true, "type": "PRODUCT_SPECIFICATION", "title": "Specifications", "moreDetailButtonText": "More Details", "lessDetailButtonText": "Less Details"}, {"id": 13, "isVisible": true, "type": "PRODUCT_RETURNABLE", "title": "Return Policy", "returnable": "Items are eligible for return within [RETURNABLE] of Delivery*. All accessories, product and packaging need to be returned in original condition.", "nonReturnable": "Non Returnable", "preHyperText": "To see the return policy,", "hyperText": "know more.", "click": {"openInRN": true, "rnActionScrren": "WebViewScreen", "webUrl": "/return-policy", "header": {"header_type": 1, "title": "Return Policy"}, "deeplink": {}}}, {"id": 14, "isVisible": true, "type": "PRODUCT_DISCLAIMER", "title": "Disclaimer", "description": "Despite our attempts to provide you with the most accurate information possible, the actual packaging, ingredients and colour of the product may sometimes vary. Please read the label, directions and warnings carefully before use."}, {"id": 15, "isVisible": true, "type": "PRODUCT_ARTICLE_ID", "title": "Article ID: [ARTICLE_ID]", "isCopyVisible": true, "copyIcon": "IcCopy", "copyMessage": "Copied!"}, {"id": 16, "isVisible": true, "type": "PRODUCT_FREQUENTLY_BOUGHT_TOGETHER", "title": "Frequently Bought Together", "click": {"openInRN": true, "rnActionScrren": "ProductDetails", "webUrl": "/", "header": {"header_type": 1, "title": "Return Policy"}, "deeplink": {}, "verticalCode": ["GROCERIES"]}}, {"id": 17, "isVisible": true, "type": "PRODUCT_SIMILAR_PRODUCTS", "title": "Similar Products", "click": {"openInRN": true, "rnActionScrren": "ProductDetails", "webUrl": "/", "header": {"header_type": 1, "title": "Return Policy"}, "deeplink": {}, "verticalCode": ["GROCERIES"]}}, {"id": 25, "type": "PRODUCT_QNA", "isVisible": true, "title": "Questions & Answers", "shortMessage": "Did you question got answered?", "titleIcon": "IcChevronRight", "pageSize": 3, "alert": {"text": "no questions available for the product", "icon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "backgroundColor": "primary_grey_20", "iconColor": "primary_60"}, "notValidPurchaseUserToastMessage": "you not eligible to ask a question", "card": {"questionTitle": "Q. [QUESTION] ?", "linkText": "Contribute and Answer", "verifiedText": "Verified user", "verifiedIcon": "IcSuccess", "likeIcon": "IcLike", "dislikeIcon": "IcDislike", "moreAnswerText": "+[NO_OF_ANSWER] more answers", "hideLike": false, "hideDislike": false}, "viewAllText": "View all", "askAQuestionText": "Ask a question", "click": {"contributeAnswer": {"openInRN": true, "rnActionScrren": "QnAAnswerScreen", "webUrl": "/", "header": {"header_type": 1, "title": "Question & Answer"}, "deeplink": {}}, "contributeQuestion": {"openInRN": true, "rnActionScrren": "QnAScreen", "webUrl": "/", "header": {"header_type": 1, "title": "Question & Answer"}, "deeplink": {}}}}, {"id": 18, "isVisible": true, "type": "PRODUCT_RATING_REVIEW", "title": "Rating & Reviews", "rating": "[NO_OF_CUSTOMER] ratings", "progressColor": "sparkle_50"}, {"id": 19, "isVisible": true, "type": "PRODUCT_REVIEW_THIS", "title": "Review this product", "subTitle": "Help others make an informed decision!"}, {"id": 20, "isVisible": true, "type": "PRODUCT_WRITE_A_REVIEW", "title": "Write a review"}, {"id": 21, "isVisible": true, "type": "PRODUCT_MY_REVIEW", "title": "My Review", "link": "Edit", "approval": {"text": "Approval pending", "backgroundColor": "#F7AB20", "textColor": "primary_inverse"}, "verified": {"ic": "IcProtection", "title": "Verified purchased"}, "subTitle": "[USER_NAME] on [DATE]", "dateFormat": "[DD] [MMM] [YY]", "minNumberOfWords": 26, "showText": "Read More", "hideText": "Read Less"}, {"id": 22, "isVisible": true, "type": "PRODUCT_CUSTOMER_PHOTOS", "title": "Customer Photos ([PHOTO_COUNT])"}, {"id": 23, "isVisible": true, "type": "PRODUCT_REVIEWS", "title": "Product Reviews ([REVIEW_COUNT])", "reviewCard": {"subTitle": "[USER_NAME] on [DATE]", "dateFormat": "[DD] [MMM] [YY]", "verified": {"ic": "IcProtection", "title": "Verified purchased"}, "likeIcon": "IcLike", "disLikeIcon": "IcDislike", "likeTitle": "Helpful [LIKE]", "dislikeTitle": "[DISLIKE]", "showText": "Read More", "hideText": "Read Less", "minNumberOfWords": 35, "report": {"ic": "IcFlag", "title": "Report"}}, "previousText": "Previous", "nextText": "Next"}], "screen": {"qna": {"headerType": 1, "headerTitle": "Questions & Answers", "noQuestionHeaderTitle": "Ask a question", "pageSize": 6, "notValidPurchaseUserToastMessage": "you not eligible to ask a question", "card": {"questionTitle": "Q. [QUESTION] ?", "linkText": "Contribute and Answer", "verifiedText": "Verified user", "verifiedIcon": "IcSuccess", "likeIcon": "IcLike", "dislikeIcon": "IcDislike", "moreAnswerText": "+[NO_OF_ANSWER] more answers", "hideLike": false, "hideDislike": false}, "click": {"openInRN": true, "rnActionScrren": "QnAAnswerScreen", "webUrl": "/", "header": {"header_type": 1, "title": "Question & Answer"}, "deeplink": {}}, "chatBox": {"subText": "Your question will be reviewed and posted in 1-2 days.", "textInput": {"placeholder": "Type your question here..."}, "validation": {"maxCharLimitText": "Maximum of [CHAR] characters required", "minCharLimitText": "Minimum of [CHAR] characters required", "requiredText": "Please enter your question!", "maxCharLimit": 100, "minCharLimit": 30, "required": true, "regex": "[^a-zA-Z0-9 \\n]| {2,}|\\n{2,}"}, "sendButton": {"title": "", "icon": "IcSendMessage"}, "submitButton": {"title": ""}, "minTextInputHeight": 0, "maxTextInputHeight": 103}}, "qnaAnswer": {"headerType": 1, "headerTitle": "More Answers", "pageSize": 6, "notValidPurchaseUserToastMessage": "you not eligible to send a answer", "card": {"questionTitle": "Q. [QUESTION] ?", "verifiedText": "Verified user", "verifiedIcon": "IcSuccess", "likeIcon": "IcLike", "dislikeIcon": "IcDislike", "hideLike": false, "hideDislike": false}, "chatBox": {"text": "Answer this question", "subText": "Your question will be reviewed and posted in 1-2 days.", "textInput": {"placeholder": "Type your answer here..."}, "validation": {"maxCharLimitText": "Maximum of [CHAR] characters required", "minCharLimitText": "Minimum of [CHAR] characters required", "requiredText": "Please enter your answer!", "maxCharLimit": 100, "minCharLimit": 30, "required": true, "regex": "[^a-zA-Z0-9 \\n]| {2,}|\\n{2,}"}, "sendButton": {"title": "", "icon": ""}, "submitButton": {"title": "Submit"}, "minTextInputHeight": 103, "maxTextInputHeight": 103}}, "offer": {"headerType": 1, "headerTitle": "Offer ([OFFER])", "block": [{"id": 1, "isVisible": true, "type": "COUPON", "title": "Coupons", "subTitle": "Get discounts/cashback on orders.", "icon": {"ic": "IcCoupon", "color": "sparkle_50", "size": "small", "mode": "", "iconUrl": ""}}, {"id": 2, "isVisible": true, "type": "BANK_OFFER", "title": "Bank Offers", "subTitle": "Get discounts/cashback on orders.", "icon": {"ic": "IcCoupon", "color": "sparkle_50", "size": "small"}, "rightIcon": {"ic": "IcChevronRight", "color": "primary_60", "size": "medium"}, "click": {"openInRN": true, "rnActionScrren": "WebViewScreen", "header": {"header_type": 9}, "deeplink": {}}}, {"id": 3, "isVisible": true, "type": "ADDITIONAL_OFFER", "title": "Additional Offers", "subTitle": "Get discounts/cashback on orders.", "icon": {"ic": "IcCoupon", "color": "sparkle_50", "size": "small"}, "rightIcon": {"ic": "IcChevronRight", "color": "primary_60", "size": "medium"}, "click": {"openInRN": true, "rnActionScrren": "WebViewScreen", "header": {"header_type": 9}, "deeplink": {}}}, {"id": 4, "isVisible": true, "type": "EMI", "title": "<PERSON><PERSON>ers", "subTitle": "Get discounts/cashback on orders.", "icon": {"ic": "IcCoupon", "color": "sparkle_50"}}, {"id": 5, "isVisible": true, "type": "LIMITED_DEALS", "title": "Limited Time Deals", "subTitle": "Get discounts/cashback on orders.", "icon": {"ic": "IcCoupon", "color": "sparkle_50"}}, {"id": 6, "isVisible": true, "type": "BUY_MORE_SAVE_MORE", "title": "Buy more save more", "subTitle": "Get discounts/cashback on orders.", "icon": {"ic": "IcCoupon", "color": "sparkle_50"}}, {"id": 7, "isVisible": true, "type": "LIMITER_TIME_OFFER", "title": "limited time", "subTitle": "Get discounts/cashback on orders.", "icon": {"ic": "IcCoupon", "color": "sparkle_50"}}]}, "customerPhotoDetail": {"subTitle": "[USER_NAME] on [DATE]", "dateFormat": "[DD] [MMM] [YY]", "verified": {"ic": "IcProtection", "title": "Verified purchased"}, "likeIcon": "IcLike", "disLikeIcon": "IcDislike", "likeTitle": "Helpful [LIKE]", "dislikeTitle": "[DISLIKE]", "showText": "Read More", "hideText": "Read Less", "minNumberOfWords": 35, "minDetailBoxHeight": 320, "maxDetailBoxHeight": 605, "report": {"ic": "IcFlag", "title": "Report"}, "bottomSheet": {"reportReview": {"reportReviewSelection": {"headerTitle": "Why are you reporting this review?", "data": [{"reportId": 1, "reportTitle": "Inappropriate language", "reportSubTitle": "Offensive, disrespectful/ obscene content ", "isSelected": false}, {"reportId": 2, "reportTitle": "Off topic", "reportSubTitle": "Comment is not about the product", "isSelected": false}, {"reportId": 3, "reportTitle": "Misinformation", "reportSubTitle": "Comment provides inaccurate information", "isSelected": false}, {"reportId": 4, "reportTitle": "Spam/Fake ", "reportSubTitle": "Comment appears to be inauthentic/paid for", "isSelected": false}], "cancelButton": {"isVisible": true, "title": "Cancel"}, "submitButton": {"isVisible": true, "title": "Submit"}, "reportFailedMessage": "Failed to report"}, "reportReviewSuccess": {"headerTitle": "Thanks for letting us know", "reviewFeedbackText": "We will review your feedback to make sure the comment follows our ", "linkText": "community guidelines.", "validReviewText": "If valid, we'll take the comment down.", "closeButton": {"visible": true, "title": "Close"}, "linkRedirectionUrl": "/"}}}}, "customerPhotos": {"headerType": 1, "headerTitle": "Customer Photos ([PHOTOS])"}, "ratingAndReviewForm": {"headerType": 1, "isDeliverToBarShow": false, "showBNB": false, "title": "Rate & Review", "RateYourProduct": {"title": "Rate Your Products", "is_divider_visible": true, "is_visible": true}, "WriteATitle": {"title": "Write a title", "regex": "[^a-zA-Z0-9 \\n]| {2,}|\\n{2,}", "placeholder": "Give a suitable title for your review", "message": "[CHAR]/[MAX][ERROR]", "subText": {"isVisible": true, "icon": {"ic": "IcLightbulb", "isVisible": true}, "title": "You can write about Ease of use, Value of money, Build quality, Charging, etc."}, "validation": {"maxCharLimit": ": Maximum of [CHAR] characters required", "minCharLimit": ": Minimum of [CHAR] characters required", "required": ": Please enter your title!"}}, "ShareAReview": {"title": "Share a Review", "regex": "[^a-zA-Z0-9 \\n]| {2,}|\\n{2,}", "placeholder": "What would you like other customers to know?", "message": "[CHAR]/[MAX][ERROR]", "subText": {"isVisible": true, "icon": {"ic": "IcLightbulb", "isVisible": true}, "title": "You can write about Ease of use, Value of money, Build quality, Charging, etc."}, "validation": {"maxCharLimit": ": Maximum of [CHAR] characters required", "minCharLimit": ": Minimum of [CHAR] characters required", "required": ": Please enter your title!"}}, "AddPhotosBlock": {"title": "Add photos", "titleTag": "(Optional)", "subtitle": "Photos make review more helpful.", "UploadBtnTitle": "Upload images", "ButtonTag": "Add up to [LENGTH_LIMIT] images (JPEG, PNG, JPG, max [SIZE_LIMIT]MB each)", "sizeLimitErrorToast": "Please upload file upto size [SIZE_LIMIT] MB"}, "FAQSection": {"submitBtnTitle": "Submit", "FAQPrompt": "Your review matters! Find guidance on writing reviews in our", "FAQSection": "FAQ Section", "EditToast": "Thank you for the review. Your review will reflect soon.", "click": {"openInRN": true, "rnActionScrren": "WebViewScreen", "webUrl": "/faq", "header": {"header_type": 1, "title": "FAQs"}, "deeplink": {}}}, "bottomSheet": {"continueReview": {"headerTitle": "Your review matters", "modalText": "If you go back, all your written information will be lost. Continue reviewing?", "goBackBtn": {"visible": true, "title": "Go back"}, "continueBtn": {"visible": true, "title": "Continue"}}}}, "ratingAndReviewSuccess": {"headerType": 2, "isDeliverToBarShow": false, "showBNB": false, "statusBarColor": "#E5F7EE", "successMsg": {"title": "Thank you so much 🎉", "subTitle": "We are processing your review. This might take several days, so we appreciate your patience. We will email you when this is complete."}, "reviewDetails": {"isVisible": true, "title": "Your review"}, "doneBtn": {"visible": true, "title": "Done"}}}, "bottomSheet": {"emi": {"credit": "<PERSON><PERSON>", "debit": "Debit Card EMI", "note": "1. Some No-Cost/Low-Cost EMI or Cashback Schemes can be availed if the eligible products are the ONLY item in your cart. Please purchase additional items in a separate transaction.\n 2. The cashback will be credited at the beginning or the end of the EMI Tenure depending on the card selected. The details for it are available on the payment page.\n 3. If a card is eligible for an upfront discount for EMI, the interest amount will be waived off as a discount.", "table": ["Months", "EMI Amount", "Interest", "Overall Cost"], "isNoteVisible": false}, "category": {"click": {"openInRN": true, "rnActionScrren": "ProductListingUpdate", "webUrl": "/return-policy", "header": {"header_type": 1, "title": "Return Policy"}, "deeplink": {}}}, "reportReview": {"reportReviewSelection": {"headerTitle": "Why are you reporting this review?", "data": [{"reportId": 1, "reportTitle": "Inappropriate language", "reportSubTitle": "Offensive, disrespectful/ obscene content ", "isSelected": false}, {"reportId": 2, "reportTitle": "Off topic", "reportSubTitle": "Comment is not about the product", "isSelected": false}, {"reportId": 3, "reportTitle": "Misinformation", "reportSubTitle": "Comment provides inaccurate information", "isSelected": false}, {"reportId": 4, "reportTitle": "Spam/Fake ", "reportSubTitle": "Comment appears to be inauthentic/paid for", "isSelected": false}], "cancelButton": {"isVisible": true, "title": "Cancel"}, "submitButton": {"isVisible": true, "title": "Submit"}, "reportFailedMessage": "Failed to report"}, "reportReviewSuccess": {"headerTitle": "Thanks for letting us know", "reviewFeedbackText": "We will review your feedback to make sure the comment follows our ", "linkText": "community guidelines.", "validReviewText": "If valid, we'll take the comment down.", "closeButton": {"visible": true, "title": "Close"}, "linkRedirectionUrl": "/"}}}}]}