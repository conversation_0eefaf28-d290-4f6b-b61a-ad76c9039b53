import {JMJCPQCEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {
  JMHttpMethods,
  JMRequestConfigObject,
} from '../api/helpers/JMRequestConfig';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import J<PERSON><PERSON><PERSON>lient from '../api/service/JMApiClient';
import JMB<PERSON><PERSON><PERSON>NetworkController from '../base/JMBaseQCNetworkController';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';

export default class JMJCPQCNetworkController extends JMB<PERSON>Q<PERSON>NetworkController {
  private apiClint: JMApiClient = JMApiClient.getInstance;
  private baseUrl: JMBaseUrlKeys = JMBaseUrlKeys.JCP;

  constructor() {
    super();
  }
  protected fetchQCDetails = async (): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPQCEndpointKeys.QC_DETAILS,
          JMHttpMethods.GET,
        ),
      );
      return await this.apiClint.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
}
