import {JMJCPHomeEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {
  JMHttpMethods,
  JMRequestConfigObject,
} from '../api/helpers/JMRequestConfig';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import J<PERSON><PERSON>Client from '../api/service/JMApiClient';
import JMBaseAddressNetworkController from '../base/JMBaseAddressNetworkController';
import JMBaseHomeNetworkController from '../base/JMBaseHomeNetworkController';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';

export default class JMJ<PERSON>HomeNetworkController extends J<PERSON>aseHomeNetworkController {
  private apiClient: JMApiClient = JMApiClient.getInstance;
  private baseUrl: JMBaseUrlKeys = JMBaseUrlKeys.JCP;
  constructor() {
    super();
  }

  protected checkServiceability = async (queryParams: {}) => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPHomeEndpointKeys.CHECK_SERVICEABILITY,
          JMHttpMethods.GET,
          {},
          queryParams,
          {},
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected getMcatInventory = async (pincode: string) => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPHomeEndpointKeys.GET_MCAT_INVENTORY + `/${pincode}`,
          JMHttpMethods.GET,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected getSingleTopDeal = async (queryParams: {}, customHeaders: {}) => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPHomeEndpointKeys.GET_SINGLE_TOP_DEAL,
          JMHttpMethods.GET,
          {},
          queryParams,
          customHeaders,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected getHomepageData = async (queryParams: {}, customHeaders: {}) => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPHomeEndpointKeys.GET_HOMEPAGE_DATA,
          JMHttpMethods.GET,
          {},
          queryParams,
          customHeaders,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected fetchProductDetailsFromAlgolia = async (bodyParams: any) => {
    try {
      console.log('fetchProductDetailsFromAlgolia',"JCP Not Implemented");
      return [];
    } catch (error) {
      throw error;
    }
  }
}
