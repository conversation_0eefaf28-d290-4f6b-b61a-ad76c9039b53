import { addStringPref } from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {JMJCPUserApiEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {
  JMHttpMethods,
  JMRequestConfigObject,
} from '../api/helpers/JMRequestConfig';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import JMApiClient from '../api/service/JMApiClient';
import JMBaseUserApiNetworkController from '../base/JMBaseUserApiNetworkController';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';
import { AsyncStorageKeys } from '../../../jiomart-common/src/JMConstants';
import JMCookieManager from '../api/helpers/JMCookieManager';

export default class JMJCPUserApiNetworkController extends JMBase<PERSON>serApiNetworkController {
  private apiClient: JMApiClient = JMApiClient.getInstance;
  private baseUrl: JMBaseUrlKeys;
  constructor() {
    super();
    this.baseUrl = JMBaseUrlKeys.JCP;
  }

  protected fetchUserDetails = async () => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPUserApiEndpointKeys.GET_USER_DETAILS,
          JMHttpMethods.GET,
        ),
      );

      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };


  protected fetchGuestUserSession = async () => {
    try {
    } catch (error) {
      throw error;
    }
  };
  protected fetchLoggedInUserSession = async (authCode: string) => {
    try {
      const headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
      };
      const body = {
        auth_code: authCode,
        application_id: '663e2332b7031043551ad694',
        return_ui_url:
          'https://jiomart.sit.jiomartjcp.com/ext/retail-auth/ui/session',
      };

      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPUserApiEndpointKeys.GET_LOGGED_IN_USER_SESSION,
          JMHttpMethods.POST,
          body,
          null,
          headers,
        ),
      );

      const userSessionData: any = await this.apiClient.request(requestHelper);
      console.log("userSessionData here in jcp", userSessionData)
      if (userSessionData.success === true) {
        const craJsonString = JSON.stringify(userSessionData.data.cra_session);
        const jcpSession = userSessionData.data.jcp_session;
        JMCookieManager.setCookies({
            ['f.session.sit']: {
              domain: jcpSession?.domain,
              httpOnly: jcpSession?.http_only,
              name: 'f.session',
              path: '/',
              secure: jcpSession?.secure,
              value: jcpSession?.cookie?.['f.session.sit'],
            },
          });
        const jcpJsonString = JSON.stringify(userSessionData.data.jcp_session);
        console.log("craJsonString",craJsonString)
        console.log("jcpJsonString",jcpJsonString)
        await addStringPref(
          AsyncStorageKeys.CRA_USER_SESSION_DATA,
          craJsonString,
        );
        await addStringPref(
          AsyncStorageKeys.JCP_USER_SESSION_DATA,
          jcpJsonString,
        );
      }
    } catch (error) {
      throw error;
    }
  };


  protected logoutUser = async () => {
    try {
    } catch (error) {
      throw error;
    }
  };
}
