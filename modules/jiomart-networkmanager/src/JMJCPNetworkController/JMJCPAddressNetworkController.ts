import {JMJCPAddressEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {
  JMHttpMethods,
  JMRequestConfigObject,
} from '../api/helpers/JMRequestConfig';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import J<PERSON>pi<PERSON>lient from '../api/service/JMApiClient';
import JMBaseAddressNetworkController from '../base/JMBaseAddressNetworkController';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';

export default class JMJCPAddressNetworkController extends JMBaseAddressNetworkController {
  private apiClient: JMApiClient = JMApiClient.getInstance;
  private baseUrl: JMBaseUrlKeys = JMBaseUrlKeys.JCP;
  constructor() {
    super();
  }
  protected fetchAddress = async () => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPAddressEndpointKeys.ADDRESS_LIST,
          JMHttpMethods.GET,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected removeAddress = async (address_id: string) => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPAddressEndpointKeys.REMOVE_ADDRESS?.replace(
            '{address_id}',
            address_id,
          ),
          JMHttpMethods.DELETE,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected defaultAddress = async (address_id: string, body: any) => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPAddressEndpointKeys.DEFAULT_ADDRESS?.replace(
            '{address_id}',
            address_id,
          ),
          JMHttpMethods.PUT,
          body,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
  protected editAddress = async (address_id: string, body: any) => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPAddressEndpointKeys.EDIT_ADDRESS?.replace(
            '{address_id}',
            address_id,
          ),
          JMHttpMethods.PUT,
          body,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected insertAddress = async (body: any) => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPAddressEndpointKeys.NEW_ADDRESS,
          JMHttpMethods.POST,
          body,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected fetchPincodeCity = async (pincode: string) => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPAddressEndpointKeys.GET_PINCODE?.replace('{pincode}', pincode),
          JMHttpMethods.GET,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
}
