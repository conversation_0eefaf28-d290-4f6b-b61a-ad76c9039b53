import {JMJCPProductEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {
  JMHttpMethods,
  JMRequestConfigObject,
} from '../api/helpers/JMRequestConfig';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import JMApiClient from '../api/service/JMApiClient';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';
import JMBaseProductNetworkController from '../base/JMBaseProductNetworkController';
import DataLoaderMap from '../DataLoaderMap';

class JMJCPPRoductNetworkController extends JMBaseProductNetworkController {
  private apiClient: JMApiClient = JMApiClient.getInstance;
  private baseUrl: JMBaseUrlKeys = JMBaseUrlKeys.JCP;
  constructor() {
    super();
  }

  public fetchProductPrice = async (request: any): Promise<any> => {
    try {
      const {slug, size} = request;
      const body = {
        items: [{slug, size}],
      };
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPProductEndpointKeys.PRODUCT_PRICE,
          JMHttpMethods.POST,
          body,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
  protected fetchProductSize = async (request: any) => {
    try {
      const {slug} = request;
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          (
            DataLoaderMap?.getCollectionItemsBySlug ||
            JMJCPProductEndpointKeys.PRODUCT_SIZE
          )?.replace('{slug}', slug),
          JMHttpMethods.GET,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
  protected fetchProductList = async (request: any): Promise<any> => {
    try {
      const {slug, params} = request;
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          (
            DataLoaderMap?.getCollectionItemsBySlug ||
            JMJCPProductEndpointKeys.PRODUCT_LIST
          )?.replace('{slug}', slug),
          JMHttpMethods.GET,
          undefined,
          params,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
  protected fetchSearchProductList = async (request: any): Promise<any> => {
    try {
      const {params} = request;
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          DataLoaderMap?.getProducts ||
            JMJCPProductEndpointKeys.PRODUCT_SEARCH_LIST,
          JMHttpMethods.GET,
          undefined,
          params,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
}

export default JMJCPPRoductNetworkController;
