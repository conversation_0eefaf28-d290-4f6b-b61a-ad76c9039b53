import {JMJCPHomeEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {
  JMHttpMethods,
  JMRequestConfigObject,
} from '../api/helpers/JMRequestConfig';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import JMA<PERSON>Client from '../api/service/JMApiClient';
import JMBaseServiceablityNetworkController from '../base/JMBaseServiceablityNetworkController';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';

class JMJCPServiceablityNetworkController extends JMBaseServiceablityNetworkController {
  private apiClient: JMApiClient = JMApiClient.getInstance;
  private baseUrl: JMBaseUrlKeys = JMBaseUrlKeys.JCP;
  constructor() {
    super();
  }

  protected getPinMetadataInfo(pincode: string): Promise<any> {
    throw new Error('Method not implemented.');
  }
  protected getHCATQCConfig = async (pincode: string): Promise<any> => {
    try {
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPHomeEndpointKeys.HCAT_QC_CONFIG + `/${pincode}`,
          JMHttpMethods.GET,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
  protected getPolygonStoreDetails(queryParams:{}): Promise<any> {
    throw new Error('Method not implemented.');
  }
  protected getMStarRegionCode(locationData: any): Promise<any> {
    throw new Error('Method not implemented.');
  }
  protected getFyndPromiseInfo(promiseParams: {}): Promise<any> {
    throw new Error('Method not implemented.');
  }
  protected setDeliveryDistance(distanceData: {
    distance: number;
    store_code: string;
    pincode: string;
  }): Promise<any> {
    throw new Error('Method not implemented.');
  }

  protected getFyndPromiseInfoWrapper(params: {},headers:{}): Promise<any> {
    throw new Error('Method not implemented.');
  }
}

export default JMJCPServiceablityNetworkController;
