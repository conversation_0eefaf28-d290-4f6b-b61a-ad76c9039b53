import {JMJCPWishlistEndpointKeys} from '../api/endpoints/JMApiEndpoints';
import {
  JMHttpMethods,
  JMRequestConfigObject,
} from '../api/helpers/JMRequestConfig';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import J<PERSON>piClient from '../api/service/JMApiClient';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';
import JMBaseWishlistNetworkController from '../base/JMBaseWishlistNetworkController';

class JMJCPWishlistNetworkController extends JMB<PERSON>WishlistNetworkController {
  private apiClient: JMApiClient = JMApiClient.getInstance;
  private baseUrl: JMBaseUrlKeys = JMBaseUrlKeys.JCP;
  constructor() {
    super();
  }

  protected fetchWishlist = async (request: any): Promise<any> => {
    try {
      const {collection} = request;
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPWishlistEndpointKeys.WISHLIST?.replace(
            '{collection_type}',
            collection,
          ),
          JMHttpMethods.GET,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected addToWishlist = async (request: any): Promise<any> => {
    try {
      const {collection = 'products', item_id} = request;

      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPWishlistEndpointKeys.ADD_TO_WISHLIST?.replace(
            '{collection_type}',
            collection,
          )?.replace('{collection_id}', item_id),
          JMHttpMethods.POST,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };

  protected removeFromWishlist = async (request: any): Promise<any> => {
    try {
      const {collection = 'products', item_id} = request;

      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          JMJCPWishlistEndpointKeys.REMOVE_FROM_WISHLIST?.replace(
            '{collection_type}',
            collection,
          )?.replace('{collection_id}', item_id),
          JMHttpMethods.DELETE,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
}

export default JMJCPWishlistNetworkController;
