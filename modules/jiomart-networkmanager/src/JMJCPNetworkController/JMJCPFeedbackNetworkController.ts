import {
  JMHttpMethods,
  JMRequestConfigObject,
} from '../api/helpers/JMRequestConfig';
import JMRequestHelper from '../api/helpers/JMRequestHelper';
import JMApiClient from '../api/service/JMApiClient';
import {JMBaseUrlKeys} from '../JMEnvironmentConfig';
import DataLoaderMap from '../DataLoaderMap';
import JMBaseFeedbackNetworkController from '../base/JMBaseFeedbackNetworkController';
import {JMJCPFeedbackApiEndpointKeys} from '../api/endpoints/JMApiEndpoints';

class JMJCPFeedbackNetworkController extends JMB<PERSON><PERSON>eedbackNetworkController {
  private apiClient: JMApiClient = JMApiClient.getInstance;
  private baseUrl: JMBaseUrlKeys = JMBaseUrlKeys.RATING_REVIEW;
  constructor() {
    super();
  }

  protected fetchReviewList = async (request: any) => {
    try {
      const {param, body} = request;
      let requestHelper = new JMRequestHelper(
        JMRequestConfigObject.requestConfig(
          this.baseUrl,
          DataLoaderMap?.getCollectionItemsBySlug ||
            JMJCPFeedbackApiEndpointKeys.REVIEW_LIST,
          JMHttpMethods.GET,
          body,
          param,
        ),
      );
      return await this.apiClient.request(requestHelper);
    } catch (error) {
      throw error;
    }
  };
}

export default JMJCPFeedbackNetworkController;
