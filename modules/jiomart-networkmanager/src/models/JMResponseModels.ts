export interface JMApiResponse {
  status: string;
  message?: string;
  contents?: string;
}

export interface JMRequestTokenResponse {
  public_key: number;
  source?: string;
  status: string;
  token: string;
  ttl: number;
  udf: string;
}

export interface JMTokenResponse {
  status: string;
  token?: string;
  public_key: number;
  ttl: number;
  source?: string;
  udf?: string;
  access_token?: string;
}

export interface JMRefreshTokenAcknowledgeResponse {
  status: string;
  message: string;
}

export interface JMSendOtpResponse {
  contents: string;
  is_retry: number;
  retry_time: number;
  status: string;
}

export interface JMUser {
  addresses?: Address[];
  auth_key?: string;
  consent_account?: boolean;
  country_code?: string;
  created_at?: Date;
  dob?: string;
  email_id?: string;
  gender?: number;
  healthhub_empi_status?: number;
  healthhub_id?: string;
  id?: number;
  idam_unique?: null;
  is_mpin_set?: boolean;
  jio_id?: string;
  medical_info?: string;
  mobile_no?: string;
  mpin_timer?: number;
  name?: string;
  profile_completeness?: number;
  profile_details?: string;
  profile_image?: string;
  profile_image_file_content_type?: null;
  profile_image_file_file_name?: null;
  profile_image_file_file_size?: null;
  profile_image_file_updated_at?: null;
  profile_privacy_status?: number;
  recovery_contact?: string;
  recovery_country_code?: string;
  resend_verify_email_count?: number;
  resend_verify_email_time?: null;
  status_id?: number;
  tags?: string;
  unique_id?: null;
  updated_at?: Date;
  user_source?: string;
  verify_email_id?: boolean;
  vitals_info?: null;
  color_indicator?: string;
  mpin?: string;
}

export interface UserProfileDetails {
  bloodgroup: string;
  pincode: string;
  height: number;
  weight: number;
  insuranceProvider: string;
  policyNumber: string;
  insurancePolicyUrl: string;
  insurancePolicyUploadTime: string;
  contactName1: string;
  contactNumber1: string;
  contactName2: string;
  contactNumber2: string;
}

export interface Address {
  adress_line1?: string;
  adress_line2?: string;
  city?: string;
  country?: string;
  created_at?: Date;
  id?: number;
  is_primary?: boolean;
  mobile?: string;
  name?: string;
  pincode?: string;
  state?: string;
}

export interface AddressWithPinCode {
  Address: string;
  City: string;
  State: string;
  State_code: string;
  Country: string;
}

export interface JMDeviceResponse {
  device?: JMDevice;
  hhub_id?: string;
  is_primary_user?: boolean;
  mpin_idle_value?: string;
  mpin_max_attempts?: number;
  primary_user_id?: number;
  recovery_contact?: string;
  recovery_country_code?: string;
  sync_actions?: any[];
}

export interface JMDevice {
  created_at?: Date;
  device_model?: string;
  device_name?: string;
  device_os?: string;
  device_type?: number;
  doctor_id?: null;
  id?: number;
  lang_name?: null;
  mpin_attempt_count?: number;
  mpin_attempt_end_time?: null;
  mpin_attempt_start_time?: null;
  primary_user_id?: null;
  status?: number;
  sync_id?: string;
  unique_id?: string;
  updated_at?: Date;
  user_id?: number;
  version_no?: number;
  voip_sync_id?: null;
}

export interface JMCreatePinResponse {
  status: string;
  contents: string;
}

export interface JMMPin {
  user_mpin: string;
}

export interface JMUpdateProfileApiResponse {
  message: string;
  status: string;
}

interface Service {
  id: string;
  name: string;
}

interface Corporate {
  corporateId: number;
  authId: string;
  name: string;
  services: Service[];
}

export interface BifrostApiResponse {
  message: string;
  data: Corporate[];
}

export interface JMEnterPin {
  status: string;
  contents: string;
  date: string;
}

export interface JMJWTTokenResponse {
  status: string;
  token: string;
  source: string;
  udfDetailsModel: JMJWTUdfDetailsResponse;
}

interface JMJWTUdfDetailsResponse {
  mPinSkip: number;
  currentUserId: number;
  rootUserId: number;
  navPage: string;
  familyProfileSkip: number;
}

export interface JMSelfCheckComputeRiskResponse {
  language: string;
  meta_data: {
    action_category: string | null;
    analytics_data: {
      name: string;
      risk_display_category: string;
      risk_id: string;
      advisory: string[];
      risk_markers: {
        low_risk_group: boolean;
        medium_risk_group: boolean;
        high_risk_group: boolean;
      };
    };
    display_data: {
      questions: JMSelfCheckAssessmentComputeRiskQuestionResponse[];
      risk_advisory: {
        data: string[];
        header: string;
        severity: string;
      };
    };
  };
  primary_action: {
    text: string;
  };
  workflow_id: string;
}

interface JMSelfCheckAssessmentComputeRiskQuestionResponse {
  header: string;
  highlight: boolean;
  data: string[];
}

export interface JMDiscoveryAPIResponse {
  partner: JMDiscoveryPartner;
  partner_user: JMPartnerUser;
}

interface JMDiscoveryPartner {
  partner_id: string;
  name: string;
  logo_url: string;
  type: string;
}

interface JMPartnerUser {
  partner_user_id: string;
  email: string;
  phone: string;
  meta_fields: JMDiscoveryMetaFields;
  family_details: JMFamilyDetails[];
}
interface JMDiscoveryMetaFields {
  date_of_birth: string;
  gender: string;
  city: string;
  date_of_joining: string;
  business_group: string;
  age: string;
  employee_designation: string;
  pme_status: string;
  first_name: string;
  company_code: string;
  pme_eligibility: string;
  pme_eligibility_start_date: string;
  pme_eligibility_end_date: string;
  pme_eligibility_amount: string;
  company: string;
  additional_attributes: {
    type: string;
    source: string;
  };
  state: string;
  cost_centre_id: string;
  cost_centre_name: string;
  emp_status_text: string;
  position_text: string;
  l1_emp_no: string;
  l1_emp_name: string;
  l1_email: string;
  job_role_text: string;
  cadre: string;
  hr_emp_no: string;
  hr_emp_name: string;
  hr_email: string;
  office_location: string;
}

interface JMFamilyDetails {
  name: string;
  date_of_birth: string;
  gender: string;
  relation: string;
  relation_id: string;
  age: string;
}

export interface UserModel {
  jhh_user_id: string;
  partner_id: string;
  meta_fields: MetaFields;
  active: boolean;
  partner: Partner;
}

interface MetaFields {
  partner_user_id: string;
  relation: string;
  relation_id: string;
}

interface Partner {
  partner_id: string;
  name: string;
  logo_url: string;
  type: string;
}

export interface JMCorporateServiceResponse {
  message: string;
  data: JMCorporateServiceDataResponse[];
}

interface JMCorporateServiceDataResponse {
  corporateId: string;
  authId: '';
  name: '';
  services: JMCorporateServiceServicesResponse[];
}

interface JMCorporateServiceServicesResponse {
  id: string;
  name: '';
}
export interface IJMSelfCheckQuestionDependency {
  condition?: string;
  options?: string[];
  question?: string;
}

export interface IJMSelfCheckQuestionOptions {
  id: string;
  preferred_term: string;
  name: string;
  ageValidation?: {
    min: number;
    max: number;
  };
}
export interface JMSelfCheckQuestionResponse {
  dependencies?: IJMSelfCheckQuestionDependency[];
  question_id: string;
  filter_id: string;
  question_text: string;
  selection_type: string;
  options?: IJMSelfCheckQuestionOptions[];
}

export interface JMDiscoveryAPIResponse {
  partner: JMDiscoveryPartner;
  partner_user: JMPartnerUser;
}

interface JMDiscoveryPartner {
  partner_id: string;
  name: string;
  logo_url: string;
  type: string;
}

interface JMPartnerUser {
  partner_user_id: string;
  email: string;
  phone: string;
  meta_fields: JMDiscoveryMetaFields;
  family_details: JMFamilyDetails[];
}
interface JMDiscoveryMetaFields {
  date_of_birth: string;
  gender: string;
  city: string;
  date_of_joining: string;
  business_group: string;
  age: string;
  employee_designation: string;
  pme_status: string;
  first_name: string;
  company_code: string;
  pme_eligibility: string;
  pme_eligibility_start_date: string;
  pme_eligibility_end_date: string;
  pme_eligibility_amount: string;
  company: string;
  additional_attributes: {
    type: string;
    source: string;
  };
  state: string;
  cost_centre_id: string;
  cost_centre_name: string;
  emp_status_text: string;
  position_text: string;
  l1_emp_no: string;
  l1_emp_name: string;
  l1_email: string;
  job_role_text: string;
  cadre: string;
  hr_emp_no: string;
  hr_emp_name: string;
  hr_email: string;
  office_location: string;
}

interface JMFamilyDetails {
  name: string;
  date_of_birth: string;
  gender: string;
  relation: string;
  relation_id: string;
  age: string;
}

export interface UserModel {
  jhh_user_id: string;
  partner_id: string;
  meta_fields: MetaFields;
  active: boolean;
  partner: Partner;
}

interface MetaFields {
  partner_user_id: string;
  relation: string;
  relation_id: string;
}

interface Partner {
  partner_id: string;
  name: string;
  logo_url: string;
  type: string;
}

export interface JMCorporateServiceResponse {
  message: string;
  data: JMCorporateServiceDataResponse[];
}

interface JMCorporateServiceDataResponse {
  corporateId: string;
  authId: '';
  name: '';
  services: JMCorporateServiceServicesResponse[];
}

interface JMCorporateServiceServicesResponse {
  id: string;
  name: '';
}

interface FamilyMasterRelationship {
  id?: number;
  name?: string;
}

interface FamilyMember {
  user_family_relationship_id?: number;
  user_id?: number;
  jio_id?: string;
  mobile_number?: string;
  country_code?: string;
  name?: string;
  date_of_birth?: string;
  email?: string | null;
  gender?: number;
  family_master_relationship?: FamilyMasterRelationship;
  created_at?: string;
  user_auth_key?: string;
  profile_image?: string;
  nickname?: string;
  color_indicator?: string | null;
}

export interface LinkedByMe {
  id?: number;
  name?: string;
  email_id?: string;
  jio_id?: string;
  country_code?: string;
  gender?: number;
  date_of_birth?: string;
  relationship?: FamilyMember[];
  family_user_id?: number;
  family_jio_id?: string;
  profile_image?: string;
  mobile_number?: string;
  profile_creation?: boolean;
  skip_mpin_verification?: boolean;
  user_auth_key?: string;
  color_indicator?: string;
  weight?: string;
  height?: string;
}

export interface SelfHealthAssessmentDetails {
  display_name: string;
  end_time: string;
  instance_name: string;
  rule_id: string;
  rule_priority: number;
  section_id: string;
  section_priority: number;
  skip_compute_risk: boolean;
  start_time: string;
  time_of_day: string;
  worflow_template: WorkflowTemplate[];
}

export interface WorkflowTemplate {
  can_skip: boolean;
  evaluate: boolean;
  filter_id?: string;
  hide?: boolean;
  name: string;
  option_type: string;
  options?: Option[];
  play_audio: boolean;
  question_id: string;
  question_text: string;
  selection_type: string;
  self_check_short_text: string;
  slider_range_end: number;
  slider_range_start: number;
  validation_error_msg?: string;
  validations?: Validation[];
  dependencies?: Dependency[];
  description?: string;
  description_text_color?: string;
  slider_unit_text?: string;
  slider_step?: number;
  continue_btn?: ActionButtons;
  skip_btn?: ActionButtons;
  guide_btn?: ActionButtons;
  question_icons?: ImageAsset[];
  options_banner?: ImageAsset;
  question_top_info?: QuestionTopInfo;
}
export interface QuestionTopInfo {
  heading: string;
  title: string;
  desc: string;
}

export interface ImageAsset {
  url: string;
  height?: number;
  width?: number;
}

interface ActionButtons {
  button_text?: string;
  icon?: string;
  cta?: any;
  bottom_sheet_content?: BottomSheetContent;
}

export interface BottomSheetContent {
  heading_text: string;
  image?: ImageAsset;
  dismiss_btn_text: string;
  description: string[];
}

export interface Option {
  ageValidation?: AgeValidation;
  clear_others: boolean;
  description?: string;
  id: string;
  name: string;
  preferred_term: string;
  type: string;
  left_image_url?: ImageAsset;
}

interface AgeValidation {
  max: number;
  min: number;
}

export interface Validation {
  type: string;
  value: string;
}

interface Dependency {
  condition: string;
  options: string[];
  question: string;
}

export interface JMSaveDetails {
  status: string;
  message: string;
}
