export interface JMCategoriesModel {
    slug: string;
    name: string;
    banners: Banners;
    department: string[];
    childs: CategoryChilds[];
}

export interface CategoryChilds {
    slug: string;
    name: string;
    logo: Portrait;
    department: string[];
    childs: SubCategoryChilds[];
}

export interface SubCategoryChilds {
    slug: string;
    name: string;
    logo: Portrait;
    department: string[];
}

export interface Banners {
    portrait: Portrait;
}

export interface Portrait {
    url: string;
}
