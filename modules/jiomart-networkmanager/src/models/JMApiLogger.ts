import {
  isDebugMode,
  isNullOrUndefinedOrEmpty,
} from '../../../jiomart-common/src/JMObjectUtility';
import {AxiosRequestConfig} from 'axios';

type logObject = any | null | undefined;
export class JMApiLogger {
  static logRequest(request: logObject, source: logObject = null) {
    if (!isDebugMode()) {
      return;
    }
    if (!isNullOrUndefinedOrEmpty(source)) {
      console.log(
        '\n\n',
        source,
        `busiCode API Request (${new Date().toISOString()}): `,
      );
    } else {
      console.log(`\n\n busiCode API Request (${new Date().toISOString()}): `);
    }
    const config = request as AxiosRequestConfig;
    const isoString = new Date().toISOString();
    console.log('URL => ', config.url + ' isodateString ' + isoString);
    console.log('HTTP Method => ', config.method);
    console.log('Headers => ', config.headers);
    console.log('Params => ', JSON.stringify(config.params, null, 2));
  }

  static logURL(request: logObject) {
    const config = request as AxiosRequestConfig;
    console.log('URL => ', config.url);
  }

  static logResponse(response: logObject, source: logObject = null) {
    if (!isDebugMode()) {
      return;
    }
    if (!isNullOrUndefinedOrEmpty(source)) {
      console.info(
        '\n\n',
        source,
        `busiCode API Response(${new Date().toISOString()}): `,
        JSON.stringify(response, null, 2),
      );
    } else {
      console.info(
        `\n\n busiCode API Response(${new Date().toISOString()}): `,
        JSON.stringify(response, null, 2),
      );
    }
  }

  static logError(error: logObject, source: logObject = null) {
    if (!isDebugMode()) {
      return;
    }
    if (!isNullOrUndefinedOrEmpty(source)) {
      console.log(
        '\n\n',
        source,
        `busiCode API Error(${new Date().toISOString()}): `,
        JSON.stringify(error, null, 2),
      );
    } else {
      console.log(
        `\n\n busiCode API Error(${new Date().toISOString()}): `,
        JSON.stringify(error, null, 2),
      );
    }
  }
}
