export interface JMUpdateProfileApiRequest {
  consent_account?: number;
  name: string;
  dob: string;
  gender: number;
  email_id: string;
}

export interface JMUpdatePinApiRequest {
  dob: string;
  email?: string;
  mobile?: string;
  country_code: string;
}

export interface JMUserDetails {
  name: string;
  device_user_id: string;
  email_id: string;
  version_number: string;
  consent_account: boolean;
  dob: string; // Date of Birth in string format
  device_auth_key: string;
  details: {
    weight: string;
    bloodgroup: string;
    height: string;
    contactNumber1: string;
    contact: string;
    policyNumber: string;
    contactName2: string;
    contactNumber2: string;
    insuranceProvider: string;
    contactName1: string;
  };
  recovery_contact: string;
  primary_user_id: string;
  device_jio_id: string;
  device_type: string;
  language: string;
  device_id: string;
  recovery_country_code: string;
  gender: string; // Gender as string (e.g., "1" for male, "2" for female)
  device_model: string;
}
