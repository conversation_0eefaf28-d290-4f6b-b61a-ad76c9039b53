export interface JMBAUCartModel {
  status: string;
  result: Result;
  response_time: string;
}

export interface Result {
  cart: Cart;
}

export interface Cart {
  id: number;
  customer_id: number;
  cart_type: string;
  reference_id: any;
  cart_status: string;
  closed_by_client: boolean;
  closed_by_server: boolean;
  method2: boolean;
  m2_status: string;
  created_channel: string;
  checkout_channel: any;
  is_edited: boolean;
  edit_scope: any;
  payment_type: any;
  created_time: string;
  billing_address_id: any;
  shipping_address_id: any;
  applied_iho_vouchers: any;
  applied_general_vouchers: any;
  applied_coupons: any;
  use_wallet_balance: boolean;
  use_nms_cash: boolean;
  use_super_cash: boolean;
  use_store_credit: boolean;
  use_employee_limit: boolean;
  use_loyalty_points: boolean;
  available_store_credit: number;
  available_employee_limit: number;
  available_loyalty_points: number;
  iho_voucher_amount: any;
  general_voucher_amount: any;
  cod_amount: any;
  prepaid_amount: any;
  doctor_consultation_needed: boolean;
  balance_info: any;
  order_id: string;
  payment_aggregator: any;
  payment_from: any;
  payment_status: string;
  payment_amount: any;
  emi_interest_amount: any;
  instant_discount_amount: any;
  payment_txnid: any;
  payment_conclusion_time: any;
  pass_on_json: string;
  webhook_status: string;
  tp_event_id: any;
  tp_status: any;
  order_status: any;
  updated_time: string;
  wasuc: boolean;
  lines: CartLine[];
  used_voucher_amount: number;
  used_wallet_amounts: UsedWalletAmounts;
  sub_total: number;
  total_savings: number;
  shipping_charges: number;
  convenience_fee: number;
  cod_charges: number;
  total_payable: number;
  net_payable: number;
  total_exchange_value: number;
  product_discount_total: number;
  coupon_discount_total: number;
  shipping_charges_original: number;
  shipping_charges_final: number;
  shipping_discount: number;
  'shipping_charges_original.sb': number;
  'shipping_charges_final.sb': number;
  'shipping_discount.sb': number;
  'shipping_free_threshold.sb': number;
  'shipping_charges_original.jm': number;
  'shipping_charges_final.jm': number;
  'shipping_discount.jm': number;
  'shipping_free_threshold.jm': number;
  billing_address: BillingAddress;
  shipping_address: ShippingAddress;
}

export interface CartLine {
  cart_id: number;
  product_code: number;
  qty: number;
  updated_time: string;
  discount: number;
  product_schedule: string;
  selling_price: number;
  mrp: number;
  is_alternate_available: boolean;
  is_alternate_switched: boolean;
  seller_id: number;
  display_name: string;
  vertical_code: string;
  is_always_available: boolean;
  rx_required: boolean;
  formulation_type: string;
  pack_size: any;
  max_qty_in_order: number;
  availability_status: string;
  is_cold_storage: boolean;
  stock_qty: any;
  url_path: string;
  is_swp: boolean;
  is_dpco: any;
  manufacturer_name: string;
  subs_selling_price: any;
  ext: any;
  product_type: string;
  product_json_ext: ProductJsonExt;
  product_image_path: string;
  out_of_stock: boolean;
  unit_product_discount: number;
  line_product_discount: number;
  unit_coupon_discount: number;
  line_coupon_discount: number;
  unit_promo_product_discount: number;
  line_promo_product_discount: number;
  unit_promo_group_discount: number;
  line_promo_group_discount: number;
  line_mrp: number;
  rate: number;
  line_value: number;
  rrp_funding_code: any;
}

export interface ProductJsonExt {
  gift: any;
  low_cost_emi: any;
  base_product_code: any;
  available_at_rrl_fc: boolean;
  available_at_3p_seller: boolean;
  catalog_source: string;
  seller_name: any;
  alternate_product_code: string;
  payment_tag: any;
  tag_todate: any;
  mart_availability: string;
  weight_in_grams: any;
  cellular_type: any;
  tag: any;
  visible_in_whatsapp: any;
  is_rrl_exclusive: boolean;
  offers: any;
  available_at_3p_kirana: boolean;
  lock_status: any;
  available_at_1p_kirana: boolean;
  tenant_ids: string[];
  food_type: string;
  option_code: any;
  standard_emi: any;
  seller_address: any;
  origin_country: string;
  colour: any;
  available_at_rrl_store: boolean;
  payment_tag_todate: any;
  is_perishable: boolean;
  size: any;
  standard_size: any;
  instant_discount: any;
  is_tradein: any;
  no_cost_emi: any;
  expiry_detail: any;
  doorstep_finance_enabled: any;
  status: string;
}

export interface UsedWalletAmounts {
  nms_cash: number;
  super_cash: number;
  wallet_cash: number;
  store_credit: number;
  employee_limit: number;
  loyalty_points: number;
  mahacb: number;
  egc: number;
  total: number;
}

export interface BillingAddress {
  id: number;
  is_active: boolean;
  customer_id: number;
  input_mode: string;
  address_type: string;
  address_type_other: string;
  apartment_id: any;
  subs_eligible: boolean;
  addressee_name: string;
  flat_or_house_no: any;
  floor_no: any;
  tower_no: any;
  building_type: any;
  building_name: any;
  building_address: string;
  area_name: string;
  city: string;
  state: string;
  pin: string;
  mobile_no: string;
  lat: number;
  lon: number;
  is_migrated: boolean;
  created_time: string;
  updated_time: string;
}

export interface ShippingAddress {
  id: number;
  is_active: boolean;
  customer_id: number;
  input_mode: string;
  address_type: string;
  address_type_other: string;
  apartment_id: any;
  subs_eligible: boolean;
  addressee_name: string;
  flat_or_house_no: any;
  floor_no: any;
  tower_no: any;
  building_type: any;
  building_name: any;
  building_address: string;
  area_name: string;
  city: string;
  state: string;
  pin: string;
  mobile_no: string;
  lat: number;
  lon: number;
  is_migrated: boolean;
  created_time: string;
  updated_time: string;
}

export interface JMBAUCreateCart {
  status: string;
  result: Result;
  response_time: string;
}

export interface Result {
  cart_id: number;
}
