export interface JMJCPCartModel {
  cart_id: number;
  uid: string;
  coupon_text: string;
  id: string;
  pan_config: PanConfig;
  delivery_promise: DeliveryPromise;
  comment: string;
  items: Item[];
  payment_selection_lock: PaymentSelectionLock;
  delivery_charge_info: string;
  common_config: CommonConfig;
  coupon: Coupon2;
  restrict_checkout: boolean;
  message: string;
  notification: Notification;
  success: boolean;
  breakup_values: BreakupValues;
  is_valid: boolean;
  currency: Currency;
  checkout_mode: string;
  last_modified: string;
  price_adjustment_applied: any[];
  buy_now: boolean;
  applied_promo_details: any[];
  is_pan_received: boolean;
  custom_cart: CustomCart;
  custom_cart_meta: CustomCartMeta;
  error_code: string;
}

export interface PanConfig {
  enabled: boolean;
  online_threshold_amount: number;
  cod_threshold_amount: number;
}

export interface DeliveryPromise {}

export interface Item {
  quantity: number;
  product: Product;
  parent_item_identifiers: ParentItemIdentifiers;
  is_set: boolean;
  article: Article;
  promotions_applied: any[];
  delivery_promise: DeliveryPromise2;
  key: string;
  coupon: Coupon;
  bulk_offer: BulkOffer;
  price_adjustment_applied: any[];
  price: Price2;
  coupon_message: string;
  identifiers: Identifiers;
  message: string;
  discount: string;
  availability: Availability;
  moq: Moq;
  price_per_unit: PricePerUnit;
  promo_meta: PromoMeta;
  custom_order: CustomOrder;
  charges: any[];
  allow_remove: boolean;
  auto_add_to_cart: boolean;
  discount_meta: DiscountMeta;
  journey_wise_promise: any[];
}

export interface Product {
  slug: string;
  images: Image[];
  teaser_tag: TeaserTag;
  brand: Brand;
  action: Action;
  uid: number;
  tags: string[];
  _custom_json: CustomJson;
  type: string;
  name: string;
  item_code: string;
  categories: Category[];
  attributes: Attributes;
  l1_categories: number[];
  l2_categories: number[];
  l3_categories: number[];
  departments: number[];
}

export interface Image {
  secure_url: string;
  url: string;
  aspect_ratio: string;
}

export interface TeaserTag {}

export interface Brand {
  uid: number;
  name: string;
}

export interface Action {
  type: string;
  url: string;
  query: Query;
}

export interface Query {
  product_slug: string[];
}

export interface CustomJson {
  brand: string;
  infinite_inventory: string;
}

export interface Category {
  uid: number;
  name: string;
}

export interface Attributes {
  'additionalinformation-netqty': string;
  countryorigin: string;
  eancode: string;
  'manufacturedetails-manufactureaddress': string;
  'manufacturedetails-manufactureid': string;
  'manufacturedetails-manufacturename': string;
  producttype: string;
  'dimensions-productheight': number;
  'dimensions-productlength': number;
  'dimensions-productweight': number;
  'dimensions-productwidth': number;
  'vertical-code': string;
  'jiomartcustomercare-email': string;
  jiomartcustomercarephone: string;
  'key-features': string;
  'manufacturer-email': string;
  'manufacturer-website': string;
  'item-height': string;
  'item-length': string;
  'item-width': string;
  'sodexo-payment-eligible': string;
  'category-l1': string;
  'category-l2': string;
  attributes: string;
  'max-qty-in-order': string;
  'is-high-value': string;
  'is-subscribable': string;
  'manufacturer-id': string;
  'brand-id': string;
  qty: string;
  'available-at-rrl-fc': string;
  'available-at-3p-seller': string;
  'catalog-source': string;
  'is-rrl-exclusive': string;
  'available-at-3p-kirana': string;
  'available-at-1p-kirana': string;
  availableatrrlstore: string;
  size: string;
  'is-liquid': string;
  'is-fragile': string;
  'is-hazmat': string;
  'group-product-id': string;
  'lookup-inventory': string;
  'price-compare-factor': string;
  'l1-category': string;
  'l2-category': string;
  'l3-category': string;
  'is-sodexo-eligible': string;
  'seller-type': string;
  brand_name: string;
}

export interface ParentItemIdentifiers {
  identifier: any;
  parent_item_size: any;
  parent_item_id: any;
}

export interface Article {
  quantity: number;
  seller: Seller;
  cart_item_meta: CartItemMeta;
  is_gift_visible: boolean;
  uid: string;
  gift_card: GiftCard;
  product_group_tags: any[];
  mto_quantity: number;
  extra_meta: ExtraMeta;
  type: string;
  _custom_json: CustomJson2;
  price: Price;
  meta: Meta;
  size: string;
  store: Store;
  tags: any[];
  variants: Variants;
}

export interface Seller {
  uid: number;
  name: string;
}

export interface CartItemMeta {}

export interface GiftCard {
  gift_price: number;
  display_text: string;
  is_gift_applied: boolean;
}

export interface ExtraMeta {}

export interface CustomJson2 {}

export interface Price {
  converted: Converted;
  base: Base;
}

export interface Converted {
  effective: number;
  currency_code: string;
  currency_symbol: string;
  marked: number;
  selling: number;
}

export interface Base {
  effective: number;
  currency_code: string;
  currency_symbol: string;
  marked: number;
  selling: number;
}

export interface Meta {
  vertical_code: string;
}

export interface Store {
  uid: number;
  name: string;
}

export interface Variants {}

export interface DeliveryPromise2 {}

export interface Coupon {}

export interface BulkOffer {}

export interface Price2 {
  converted: Converted2;
  base: Base2;
}

export interface Converted2 {
  marked: number;
  add_on: number;
  currency_code: string;
  currency_symbol: string;
  effective: number;
  selling: number;
}

export interface Base2 {
  marked: number;
  add_on: number;
  currency_code: string;
  currency_symbol: string;
  effective: number;
  selling: number;
}

export interface Identifiers {
  identifier: string;
}

export interface Availability {
  is_valid: boolean;
  other_store_quantity: number;
  deliverable: boolean;
  available_sizes: AvailableSize[];
  out_of_stock: boolean;
  sizes: string[];
}

export interface AvailableSize {
  display: string;
  value: string;
  is_available: boolean;
}

export interface Moq {
  minimum: number;
}

export interface PricePerUnit {
  converted: Converted3;
  base: Base3;
}

export interface Converted3 {
  marked: number;
  currency_code: string;
  currency_symbol: string;
  effective: number;
  selling_price: number;
}

export interface Base3 {
  marked: number;
  currency_code: string;
  currency_symbol: string;
  effective: number;
  selling_price: number;
}

export interface PromoMeta {}

export interface CustomOrder {
  is_custom_order: boolean;
  manufacturing_time: number;
  manufacturing_time_unit: string;
}

export interface DiscountMeta {}

export interface PaymentSelectionLock {
  payment_identifier: string;
  default_options: string;
  enabled: boolean;
}

export interface CommonConfig {
  delivery_charges_config: DeliveryChargesConfig;
}

export interface DeliveryChargesConfig {
  enabled: boolean;
  charges: any[];
}

export interface Coupon2 {
  cashback_amount: number;
  cashback_message_primary: string;
  cashback_message_secondary: string;
  coupon_code: string;
  coupon_description: string;
  coupon_subtitle: string;
  coupon_title: string;
  coupon_type: string;
  coupon_value: number;
  discount: number;
  is_applied: boolean;
  is_valid: boolean;
  maximum_discount_value: number;
  message: string;
  minimum_cart_value: number;
  medias: any[];
}

export interface Notification {}

export interface BreakupValues {
  coupon: Coupon3;
  display: any[];
  loyalty_points: LoyaltyPoints;
  raw: Raw;
}

export interface Coupon3 {
  title: string;
  value: number;
  is_applied: boolean;
  coupon_type: string;
  sub_title: string;
  coupon_value: number;
  code: string;
  type: string;
  minimum_cart_value: number;
  message: string;
  description: string;
}

export interface LoyaltyPoints {}

export interface Raw {
  promotion: number;
  coupon: number;
  gst_charges: number;
  mrp_total: number;
  fynd_cash: number;
  vog: number;
  gift_card: number;
  cod_charge: number;
  total: number;
  discount: number;
  delivery_charge: number;
  you_saved: number;
  subtotal: number;
  sub_total: number;
  convenience_fee: number;
  total_charge: number;
  mop_total: number;
}

export interface Currency {
  code: string;
  symbol: string;
}

export interface CustomCart {
  id: string;
  cart_name: string;
  cart_type: string;
  is_universal: boolean;
}

export interface CustomCartMeta {}
