export interface JMUserModel {
  _id: string;
  username: string;
  emails: emailModel[];
  gender: string;
  active: boolean;
  first_name: string;
  last_name: string;
  phone_numbers: phoneNumbersModel[];
  account_type: string;
  profile_pic_url: string;
  user_id: string;
  application_id: string;
  created_at: string;
  updated_at: string;
}

interface emailModel {
  email: string;
  active: boolean;
  primary: boolean;
  verified: boolean;
}

interface phoneNumbersModel {
  phone: string;
  active: boolean;
  primary: boolean;
  verified: boolean;
  country_code: number;
}
