import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import JMBaseNetworkController, {
  type JMBaseNetworkControllerOptions,
} from '../base/JMBaseNetworkController';
import JMJCPSearchNetworkController from '../JMJCPNetworkController/JMJCPSearchNetworkController';
import JMBAUSearchNetworkController from '../JMBAUNetworkController/JMBAUSearchNetworkController';
import {FetchAlgoliaIndexSearchProps} from '../models/JMSearch/JMSearchModels';

class JMSearchNetworkController extends JMBaseNetworkController {
  private _searchController: any;
  constructor(options?: JMBaseNetworkControllerOptions) {
    super(options);
    switch (this.options?.appSource) {
      case AppSourceType.JM_JCP:
        this._searchController = new JMJCPSearchNetworkController();
        break;
      case AppSourceType.JM_BAU:
        this._searchController = new JMBAUSearchNetworkController();
        break;
      default:
        this._searchController = new JMJCPSearchNetworkController();
    }
  }

  public getRecommendedProducts = async (params: any) => {
    try {
      let data: any = await this._searchController.getRecommendedProducts(
        params,
      );
      return data;
    } catch (error) {
      throw error;
    }
  };

  public getBuyAgainProducts = async (params: any) => {
    try {
      let data: any = await this._searchController.getBuyAgainItems(params);
      return data;
    } catch (error) {
      throw error;
    }
  };

  public fetchDiscoverMoreData = async (
    config?: FetchAlgoliaIndexSearchProps,
  ) => {
    try {
      let data: any = await this._searchController.fetchDiscoverMoreData(
        config,
      );
      console.log('Discover More Data:', data);
      return data;
    } catch (error) {
      console.log('Discover More Error:', error);
      throw error;
    }
  };

  public getSearchResults = async (params: any) => {
    try {
      const result = await this._searchController.getSearchResults(params);
      return result;
    } catch (error) {
      console.error('Error fetching search results:', error);
      throw error;
    }
  };

  public fetchRecommendedItemsServiceability = async (bodyParams: any) => {
    try {
      let data: any =
        await this._searchController.fetchRecommendedItemsServiceability(
          bodyParams,
        );
      return data;
    } catch (error) {
      throw error;
    }
  };
}

export default JMSearchNetworkController;
