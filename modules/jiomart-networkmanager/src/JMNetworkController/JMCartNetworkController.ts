import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import JMJCPCartNetworkController from '../JMJCPNetworkController/JMJCPCartNetworkController';
import JMB<PERSON><PERSON>artNetworkController from '../JMBAUNetworkController/JMBAUCartNetworkController';
import JMBaseNetworkController, {
  type JMBaseNetworkControllerOptions,
} from '../base/JMBaseNetworkController';
import JMCartMapper from '../JMMapperManager/JMCartMapper';

class JMCartNetworkController extends J<PERSON><PERSON>NetworkController {
  private _cartController: any;
  constructor(options?: JMBaseNetworkControllerOptions) {
    super(options);
    switch (this.options?.appSource) {
      case AppSourceType.JM_JCP:
        this._cartController = new JMJCPCartNetworkController();
        break;
      case AppSourceType.JM_BAU:
        this._cartController = new JMBAUCartNetworkController();
        break;
      default:
        this._cartController = new JMBAUCartNetworkController();
    }
  }

  public fetchCart = async (returnMappedResp = true): Promise<any> => {
    try {
      const response = await this._cartController.fetchCart();
      return returnMappedResp
        ? JMCartMapper.mapToUnifiedCartResponse(response)
        : response;
    } catch (error) {
      throw error;
    }
  };

  public addToCart = async (request: any): Promise<any> => {
    try {
      const data = await this._cartController.addToCart(request);
      return JMCartMapper.mapToUnifiedAddToCart(data);
    } catch (error) {
      throw error;
    }
  };

  public removeFromCart = async (request: any): Promise<any> => {
    try {
      const data = await this._cartController.removeFromCart(request);
      return JMCartMapper.mapToUnifiedRemoveFromCart(data);
    } catch (error) {
      throw error;
    }
  };
}

export default JMCartNetworkController;
