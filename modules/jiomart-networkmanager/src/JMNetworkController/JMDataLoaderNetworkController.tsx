import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import JMBaseNetworkController, {
  type JMBaseNetworkControllerOptions,
} from '../base/JMBaseNetworkController';
import {updateDataLoaderMap} from '../DataLoaderMap';
import JMBAUDataLoaderNetworkController from '../JMBAUNetworkController/JMBAUDataLoaderNetworkController';
import JMJCPDataLoaderNetworkController from '../JMJCPNetworkController/JMJCPDataLoaderNetworkController';
class J<PERSON><PERSON>LoaderNetworkController extends JMBaseNetworkController {
  private _dataLoaderController: any;

  constructor(options?: JMBaseNetworkControllerOptions) {
    super(options);
    switch (this.options?.appSource) {
      case AppSourceType.JM_JCP:
        this._dataLoaderController = new JMJCPDataLoaderNetworkController();
        break;
      case AppSourceType.JM_BAU:
        this._dataLoaderController = new JMBAUDataLoaderNetworkController();
        break;
      default:
        this._dataLoaderController = new JMBAUDataLoaderNetworkController();
    }
  }
  public fetchDataLoader = async () => {
    try {
      const data = await this._dataLoaderController.fetchDataLoader();
      if (data?.data) {
        updateDataLoaderMap(data?.data);
      }
    } catch (error) {}
  };
}

export default JMDataLoaderNetworkController;
