import { JMSharedViewModel } from '../../../jiomart-common/src/JMSharedViewModel';
import { AppSourceType } from '../../../jiomart-common/src/SourceType';
import JMB<PERSON><PERSON>UserApiNetworkController from '../JMBAUNetworkController/JMBAUUserApiNetworkController';
import J<PERSON><PERSON><PERSON><PERSON><PERSON>ApiNetworkController from '../JMJCPNetworkController/JMJCPUserApiNetworkController';
import JMUserMapper from '../JMMapperManager/JMUserMapper';
import { JMDatabaseManager } from '../db/JMDatabaseManager';

class JMUserApiNetworkController {
  private _userApiNetworkController: any;

  constructor() {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        this._userApiNetworkController = new JMJCPUserApiNetworkController();
        break;
      case AppSourceType.JM_BAU:
        this._userApiNetworkController = new JMBAUUserApiNetworkController();
        break;
      default:
        this._userApiNetworkController = new JMBAUUserApiNetworkController();
    }
  }

  public fetchUserDetails = async () => {
    try {
      let data: any = await this._userApiNetworkController.fetchUserDetails();
      const mappedData = JMUserMapper.mapToUnifiedUserDetailsResponse(data);
      if (mappedData) {
        JMDatabaseManager.user.saveUserDetails(JSON.stringify(mappedData))
      }
      return mappedData;
    } catch (error) {
      throw error;
    }
  };

  public fetchGuestUserSession = async () => {
    try {
      return await this._userApiNetworkController.fetchGuestUserSession();
    } catch (error) {
      throw error;
    }
  };

  public fetchLoggedInUserSession = async (authCode: string) => {
    try {
      return await this._userApiNetworkController.fetchLoggedInUserSession(
        authCode,
      );
    } catch (error) {
      throw error;
    }
  };

  public getRRID = async (sessionId: string, userId: string) => {
    try {
      return await this._userApiNetworkController.getRRID(sessionId, userId);
    } catch (error) {
      throw error;
    }
  };

  public logoutUser = async () => {
    try {
      return await this._userApiNetworkController.logoutUser();
    } catch (error) {
      throw error;
    }
  };

}

export default JMUserApiNetworkController;
