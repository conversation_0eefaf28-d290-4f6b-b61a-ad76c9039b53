import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import JMJCPWishlistNetworkController from '../JMJCPNetworkController/JMJCPWishlistNetworkController';
import JMBAUWishlistNetworkController from '../JMBAUNetworkController/JMBAUWishlistNetworkController';
import type {JMWishlistModel} from '../models/Wishlist/JMWishlistModel';
import JMBaseNetworkController, {
  type JMBaseNetworkControllerOptions,
} from '../base/JMBaseNetworkController';

class JMWishlistNetworkController extends JMBaseNetworkController {
  private _wishlistController: any;
  constructor(options?: JMBaseNetworkControllerOptions) {
    super(options);
    switch (this.options?.appSource) {
      case AppSourceType.JM_JCP:
        this._wishlistController = new JMJCPWishlistNetworkController();
        break;
      case AppSourceType.JM_BAU:
        this._wishlistController = new JMBAUWishlistNetworkController();
        break;
      default:
        this._wishlistController = new JMBAUWishlistNetworkController();
    }
  }

  public fetchWishlists = async () => {
    try {
      const response = await this._wishlistController.fetchWishlist({
        collection: 'products',
      });
      return this.mapToUnifiedWishlistResponse(response);
    } catch (error) {
      throw error;
    }
  };
  public fetchWishlistIds = async (): Promise<any> => {
    try {
      const response = await this._wishlistController.fetchWishlist({
        collection: 'ids',
      });
      return this.mapToUnifiedWishlistResponse(response);
    } catch (error) {
      throw error;
    }
  };

  public addToWishlist = async (request: any): Promise<any> => {
    try {
      return await this._wishlistController.addToWishlist(request);
    } catch (error) {
      throw error;
    }
  };

  public removeFromWishlist = async (request: any): Promise<any> => {
    try {
      return await this._wishlistController.removeFromWishlist(request);
    } catch (error) {
      throw error;
    }
  };

  private mapToUnifiedWishlistResponse = (data: any): JMWishlistModel => {
    switch (this.options?.appSource) {
      case AppSourceType.JM_JCP:
        return this.generateWishlistResponseFromJcp(data);
      case AppSourceType.JM_BAU:
        return this.generateWishlistResponseFromBau(data);
      default:
        return this.generateWishlistResponseFromBau(data);
    }
  };

  private generateWishlistResponseFromJcp = (data: any): any => {
    let cart: JMWishlistModel = {
      ids: data?.data?.products || [],
    };
    return cart;
  };

  private generateWishlistResponseFromBau = (data: any): any => {
    let cart: JMWishlistModel = {
      ids: data?.result?.productList,
    };
    return cart;
  };
}

export default JMWishlistNetworkController;
