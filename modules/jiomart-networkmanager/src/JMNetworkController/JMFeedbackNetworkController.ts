import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import JMBaseNetworkController, {
  type JMBaseNetworkControllerOptions,
} from '../base/JMBaseNetworkController';
import JMBAUFeedbackNetworkController from '../JMBAUNetworkController/JMBAUFeedbackNetworkController';
import JMJ<PERSON><PERSON>eedbackNetworkController from '../JMJCPNetworkController/JMJCPFeedbackNetworkController';
import JMFeedbackMapper from '../JMMapperManager/JMFeedbackMapper';

class JMFeedbackNetworkController extends JMBaseNetworkController {
  private _feedbackController: any;

  constructor(options?: JMBaseNetworkControllerOptions) {
    super(options);
    switch (this.options?.appSource) {
      case AppSourceType.JM_JCP:
        this._feedbackController = new JMJCPFeedbackNetworkController();
        break;
      case AppSourceType.JM_BAU:
        this._feedbackController = new JMBAUFeedbackNetworkController();
        break;
      default:
        this._feedbackController = new JMBAUFeedbackNetworkController();
    }
  }

  public fetchReviewList = async (request: any) => {
    try {
      const data = await this._feedbackController.fetchReviewList(request);
      return JMFeedbackMapper.mapToUnifiedReviewListResponse(data);
    } catch (error) {
      throw error;
    }
  };
  public getFeedbackConfig = async () => {
    try {
      const data = await this._feedbackController.fetchReviewValidationConfig();
      return JMFeedbackMapper.mapToUnifiedFeedbackConfigResponse(data);
    } catch (error) {
      throw error;
    }
  };
  public submitReview = async (request: any) => {
    try {
      const data = await this._feedbackController.submitReview(request);
      return JMFeedbackMapper.mapToUnifiedSubmitReviewResponse(data);
    } catch (error) {
      throw error;
    }
  };
  public updateReview = async (request: any) => {
    try {
      const data = await this._feedbackController.updateReview(request);
      return JMFeedbackMapper.mapToUnifiedUpdateReviewResponse(data);
    } catch (error) {
      throw error;
    }
  };
  public generateSignedUrl = async (request: any) => {
    try {
      const data = await this._feedbackController.generateSignedUrl(request);
      return JMFeedbackMapper.mapToUnifiedGenerateSignedUrlResponse(data);
    } catch (error) {
      throw error;
    }
  };
  public uploadImage = async (request: any) => {
    try {
      const data = await this._feedbackController.uploadImage(request);
      return JMFeedbackMapper.mapToUnifiedUploadImageResponse(data);
    } catch (error) {
      throw error;
    }
  };

  public fetchRequestBodyRatingAndReview = (request: any) => {
    try {
      const data =
        this._feedbackController.generateRatingAndReviewRequestBody(request);
      return data;
    } catch (error) {
      throw error;
    }
  };
}

export default JMFeedbackNetworkController;
