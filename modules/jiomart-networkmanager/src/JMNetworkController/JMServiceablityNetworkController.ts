import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import JMBaseNetworkController, {
  type JMBaseNetworkControllerOptions,
} from '../base/JMBaseNetworkController';
import JMJCPServiceablityNetworkController from '../JMJCPNetworkController/JMJCPServiceablityNetworkController';
import JMBAUServiceablityNetworkController from '../JMBAUNetworkController/JMBAUServiceablityNetworkController';
import {
  ConfigInfo,
  JMCheckServiceabilityData,
  JMPolygonStore,
  JMPStoreCode,
  PinInfo,
  PromiseResult,
  Serviceability,
} from '../models/home/<USER>';

import {jsonParse} from '../../../jiomart-common/src/uiModals/JMResponseParser';
import {AsyncStorageKeys} from '../../../jiomart-common/src/JMConstants';
import {
  addStringPref,
  getPrefString,
} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import { JMDatabaseManager } from '../db/JMDatabaseManager';

/**
 * JMServiceablityNetworkController
 *
 * Wrapper controller for handling serviceability checks across different JioMart applications.
 * Orchestrates the 6-step serviceability flow:
 * 1. MStar Pin Info API - Pincode metadata
 * 2. HCAT QC Config API - Quick Commerce configuration
 * 3. Polygon/Jio-net API - Store details and polygon data
 * 4. MStar Region API - Region code information
 * 5. Fynd Promise API - Delivery promise calculations
 * 6. Set Delivery API - Distance configuration for MStar systems
 *
 * Details Doc @link : https://rilcloud-my.sharepoint.com/:w:/r/personal/samriddha_samanta_ril_com/_layouts/15/doc.aspx?sourcedoc=%7B0479a173-9e2e-4035-b346-172315bc63b0%7D&action=edit
 *
 * @extends JMBaseNetworkController
 */
class JMServiceablityNetworkController extends JMBaseNetworkController {
  private _homeController: any;

  constructor(options?: JMBaseNetworkControllerOptions) {
    super(options);
    switch (this.options?.appSource) {
      case AppSourceType.JM_JCP:
        this._homeController = new JMJCPServiceablityNetworkController();
        break;
      case AppSourceType.JM_BAU:
        this._homeController = new JMBAUServiceablityNetworkController();
        break;
      default:
        this._homeController = new JMBAUServiceablityNetworkController();
    }
  }

  /**
   * Checks service availability for a given pincode with caching logic.
   *
   * This function orchestrates multiple API calls to determine if delivery services
   * are available for a specific pincode. It includes caching to avoid redundant
   * API calls within a specified refresh interval.
   *
   * @param pincode - The postal code to check serviceability for
   * @param refreshIntervalInMin - Cache refresh interval in minutes (default: 30)
   * @returns Promise containing delivery promise and serviceability information
   *
   * Process Flow:
   * 1. Check cache for existing data within refresh interval
   * 2. Fetch pincode metadata (coordinates) via getPincodeData()
   * 3. Get Quick Commerce configuration via getQCConfig()
   * 4. Find polygon stores and serviceability (if QC config and coordinates exist)
   * 5. Get region code details (if store found)
   * 6. Calculate delivery promises via getFyndPromiseDetails()
   * 7. Cache results for future use
   */
  public checkServiceabilityWithPincode = async (
    pincode: string,
    refreshIntervalInMin: number = 30,
    invalidateCacheAndRefetch:boolean=false
  ):Promise<{
    pincodeMetaData:PinInfo|null,
    fyndPromiseApiResponse: PromiseResult|null
  }> => {
    try {
      // Check cache first to avoid unnecessary API calls
      const cachedResult = await this.getCachedServiceabilityData(
        refreshIntervalInMin,
      );
      if (cachedResult&&!invalidateCacheAndRefetch) {
        console.log('Using cached serviceability data');
        return {
          pincodeMetaData:cachedResult.pinMetadata,
          fyndPromiseApiResponse:cachedResult.fyndPromiseData
        };
      }

      console.log('======== checkServiceabilityWithPincode : Api 1 ==================');
      const pincodeMetaData = await this.getPincodeData(pincode);
      
      console.log('======== checkServiceabilityWithPincode : Api 2 ==================');
      const qcConfigParsed = await this.getQCConfig(pincode);

      let qcStoreAndServiceability: {
        store: JMPolygonStore;
        serviceability: Serviceability;
      } | null = null;

      if (
        qcConfigParsed?.polygon &&
        pincodeMetaData?.lat &&
        pincodeMetaData?.lon
      ) {
        //  3. Polygon/Jio-net API - Store details and polygon data
        console.log('======== checkServiceabilityWithPincode : Api 3 ==================');
        const polygonStoreParsed = await this.getPolygonStoreDetails(
          pincodeMetaData,
        );
        
        qcStoreAndServiceability = polygonStoreParsed
          ? this.findQuickCommerceStoreAndServiceability(polygonStoreParsed)
          : null;

        console.log('======== checkServiceabilityWithPincode : Api 4 ==================');
        //    4. MStar Region API - Region code information
        if (qcStoreAndServiceability?.store) {
          const regionCodeResponse = await this.getRegionCodeDetails(
            qcStoreAndServiceability?.store,
          );
        }
      }

      //    5. Fynd Promise API - Delivery promise calculations
      console.log('======== checkServiceabilityWithPincode : Api 5 ==================');
      const fyndPromiseApiResponse = await this.getFyndPromiseDetails(
        pincode,
        pincodeMetaData,
        qcConfigParsed,
        qcStoreAndServiceability?.serviceability ?? null,
      );

      // console.log('======== Step 6 ==================');
      // const setDistanceResponse = await this.setDeliveryDistanceDetails();
      // if (setDistanceResponse) {
      //   console.log(
      //     'CheckServiceabilityWithPincode Delivery Distance : ',
      //     JSON.stringify(setDistanceResponse, null, 2),
      //   );
      // }
      
      await this.saveCacheData(pincodeMetaData,fyndPromiseApiResponse);
      return{
        pincodeMetaData:pincodeMetaData,
        fyndPromiseApiResponse:fyndPromiseApiResponse
      }
    } catch (error) {
      console.log('CheckServiceabilityWithPincode Error : ', error);
      throw error;
    }
  };

  /**
   * Retrieves cached serviceability data if it exists and is within refresh interval
   */
  private getCachedServiceabilityData = async (
    refreshIntervalInMin: number,
  ) => {
    const cachedData = await getPrefString(
      AsyncStorageKeys.SERVICEABILITY_DATA,
    );

    if (!cachedData) return null;

    try {
      const parsedCache = JSON.parse(cachedData);
      const currentTime = Date.now();
      const cacheAge = currentTime - (parsedCache.timestamp || 0);
      const refreshThreshold = refreshIntervalInMin * 60 * 1000;

      if (cacheAge < refreshThreshold) {
        console.log(`Cache hit: Data is ${Math.round(cacheAge / 1000)}s old`);
        return parsedCache;
      }

      console.log(
        `Cache expired: Data is ${Math.round(cacheAge / 60000)}min old`,
      );
      return null;
    } catch (error) {
      console.warn('Failed to parse cached data, fetching fresh data');
      return null;
    }
  };

  private saveCacheData = async(pincodeMetaData: PinInfo | null,promiseApiResponse: PromiseResult | null)=>{
    if(promiseApiResponse!=null&&promiseApiResponse){
      const cacheData = {
        fyndPromiseData: promiseApiResponse,
        timestamp: Date.now(),
        pinMetadata:pincodeMetaData
        //pincode: pincode // Optional: for debugging purposes
      };
      await addStringPref(
        AsyncStorageKeys.SERVICEABILITY_DATA,
        JSON.stringify(cacheData),
      );
    }
  }

  private getPincodeData = async (pincode: string) => {
    try {
      const response = await this._homeController.getPinMetadataInfo(pincode);
      return this.mapToPincodeMetadataInfoModel(response);
    } catch (error) {
      console.error('getPinMetadataInfo failed:', error);
      return null;
    }
  };

  private getQCConfig = async (pincode: string) => {
    try {
      const response = await this._homeController.getHCATQCConfig(pincode);
      return this.mapToQCConfigInfoModel(response);
    } catch (error) {
      console.error('getHCATQCConfig failed:', error);
      return null;
    }
  };

  private getPolygonStoreDetails = async (pincodeMetaData: PinInfo) => {
    try {
      const queryParams = {
        lat: pincodeMetaData.lat,
        lng: pincodeMetaData.lon,
      };
      let polygonStoreResponse: any =
        await this._homeController.getPolygonStoreDetails(queryParams);
      return this.mapToPolygonStoreDetailsModel(polygonStoreResponse);
    } catch (error) {
      console.error('getPolygonStoreDetails failed:', error);
      return null;
    }
  };

  private getRegionCodeDetails = async (storeDetails: JMPolygonStore) => {
    try {
      const queryParams = {
        vertical_code: 'groceries', // this will be hardcoded for now
        store_code: storeDetails?.store_code ?? '',
      };
      let regionCodeResponse: any =
        await this._homeController.getMStarRegionCode(queryParams);
      return this.mapToStoreCodeModel(regionCodeResponse);
    } catch (error) {
      console.error('getRegionCodeDetails failed:', error);
      return null;
    }
  };

  private getFyndPromiseDetails = async (
    pincode: string,
    pincodeMetaData: PinInfo | null,
    qcConfigParsed: ConfigInfo | null,
    serviceability: Serviceability | null,
  ) => {
    try {
      const userDetails = await JMDatabaseManager.user.getUserDetails()
      let phoneNumber = '';
      if (userDetails) {
        const userSessionData = JSON.parse(userDetails ?? '');
        phoneNumber = userSessionData?.phone_numbers?.[0].phone ?? '';
      }

      const bodyParams = {
        identifier: '4089839d-b181-4698-88ff-2c57d7a9ce84', // TODO : This gets generated via a script.
        to_pincode: pincode,
        customer_details: {
          phone_number: phoneNumber,
          pincode: pincode,
          coordinates: {
            lat: pincodeMetaData?.lat ?? '',
            long: pincodeMetaData?.lon ?? '',
          },
        },
        qc: qcConfigParsed?.qc
          ? 1
          : serviceability && serviceability?.polygon_id
          ? 1
          : 0,
        suspect: qcConfigParsed?.suspect ? 1 : 0,
        channel_id: '',
      };

      const promiseApiResponse = await this._homeController.getFyndPromiseInfo(
        bodyParams,
      );
      return this.mapToPromiseResponseModel(promiseApiResponse);
    } catch (error) {
      console.error('getFyndPromiseDetails failed:', error);
      return null;
    }
  };

  private setDeliveryDistanceDetails = async () => {
    try {
      const userSession = await JMDatabaseManager.user.getUserSession()
      if (!userSession) {
        console.log('User not logged in, skipping delivery distance setup');
        return null;
      }

      const cart = await JMDatabaseManager.cart.readDBCart();
      const queryParams = {
        cart_id: cart?.cart_id ?? '',
        delivery_distance: 789, // TODO
      };

      const setDistanceResponse =
        await this._homeController.setDeliveryDistance(queryParams);
      return setDistanceResponse;
    } catch (error) {
      console.error('setDeliveryDistanceDetails failed:', error);
      return null;
    }
  };

  private mapToPincodeMetadataInfoModel = (data: any): PinInfo | null => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return data;
      case AppSourceType.JM_BAU:
        const parseData = jsonParse<PinInfo>(JSON.stringify(data?.result));
        return parseData;
      default:
        return data;
    }
  };

  private mapToQCConfigInfoModel = (data: any): ConfigInfo | null => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return data;
      case AppSourceType.JM_BAU:
        const parseData = jsonParse<ConfigInfo>(JSON.stringify(data));
        return parseData;
      default:
        return data;
    }
  };

  private mapToPolygonStoreDetailsModel = (
    data: any,
  ): JMPolygonStore[] | null => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return data;
      case AppSourceType.JM_BAU:
        const parseData = jsonParse<JMPolygonStore[]>(
          JSON.stringify(data?.data),
        );
        return parseData;
      default:
        return data;
    }
  };

  private findQuickCommerceStoreAndServiceability(stores: JMPolygonStore[]) {
    for (const store of stores) {
      for (const serviceability of store.serviceabilities) {
        if (serviceability.type === 'quick_commerce') {
          return {
            store,
            serviceability,
          };
        }
      }
    }
    return null;
  }

  private mapToStoreCodeModel = (data: any): JMPStoreCode | null => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return data;
      case AppSourceType.JM_BAU:
        const parseData = jsonParse<JMPStoreCode>(JSON.stringify(data?.result));
        return parseData;
      default:
        return data;
    }
  };

  private mapToPromiseResponseModel = (data: any): PromiseResult | null => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return data;
      case AppSourceType.JM_BAU:
        const parseData = jsonParse<PromiseResult>(JSON.stringify(data));
        return parseData;
      default:
        return data;
    }
  };
}

export default JMServiceablityNetworkController;
