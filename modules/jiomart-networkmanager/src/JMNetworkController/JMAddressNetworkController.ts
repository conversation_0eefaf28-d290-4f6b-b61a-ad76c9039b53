import { updateAddressListInDB } from '../../../jiomart-address/src/utils';
import { JMSharedViewModel } from '../../../jiomart-common/src/JMSharedViewModel';
import { AppSourceType } from '../../../jiomart-common/src/SourceType';
import { JMLogger } from '../../../jiomart-common/src/utils/JMLogger';
import { JMErrorCodes } from '../api/utils/JMErrorCodes';
import JMBaseNetworkController, {
  type JMBaseNetworkControllerOptions,
} from '../base/JMBaseNetworkController';
import { JMDatabaseManager } from '../db/JMDatabaseManager';
import JMBAUAddressNetworkController from '../JMBAUNetworkController/JMBAUAddressNetworkController';
import JMJ<PERSON><PERSON>ddressNetworkController from '../JMJCPNetworkController/JMJCPAddressNetworkController';
import J<PERSON><PERSON><PERSON>Mapper from '../JMMapperManager/JMAddressMapper';

class JMAddressNetworkController extends JMBaseNetworkController {
  private _addressController: any;

  constructor(options?: JMBaseNetworkControllerOptions) {
    super(options);
    switch (this.options?.appSource) {
      case AppSourceType.JM_JCP:
        this._addressController = new JMJCPAddressNetworkController();
        break;
      case AppSourceType.JM_BAU:
        this._addressController = new JMBAUAddressNetworkController();
        break;
      default:
        this._addressController = new JMBAUAddressNetworkController();
    }
  }

  public getAddressList = async (refreshApi: boolean) => {
    try {
      const userLoggedIn = JMDatabaseManager.user.isUserLoggedInFlag()
      if (!userLoggedIn) throw {
        code: JMErrorCodes.UNKNOWN_ERROR,
        message: "Login required"
      };
      if (!refreshApi && JMSharedViewModel.Instance.getAddressListApiCalled()) {
        const addressDbData =
          await JMDatabaseManager.address.getDBAddressList();
        if (addressDbData && addressDbData.length > 0) {
          JMLogger.log(
            'getAddressList ',
            'getAddressList addressDbData' + JSON.stringify(addressDbData),
          );
          const addressList = JSON.parse(addressDbData || '');
          if (addressList) return addressList;
        }
      }
      let data: any = await this._addressController.fetchAddress();
      const addressListMappedData = JMAddressMapper.mapToUnifiedAddresses(data);
      if (addressListMappedData && addressListMappedData.length > 0) {
        updateAddressListInDB(addressListMappedData);
        JMSharedViewModel.Instance.setAddressListApiCalled(true);
      }
      return addressListMappedData;
    } catch (error) {
      throw error;
    }
  };
  public fetchPincodeCity = async (pincode: string) => {
    try {
      const data = await this._addressController.fetchPincodeCity(pincode);
      return JMAddressMapper.mapToUnifiedPincodeCity(data);
    } catch (error) {
      throw error;
    }
  };
  public defaultAddress = async (request: any) => {
    try {
      const { id } = request;
      const data = await this._addressController.defaultAddress(id, request);
      return JMAddressMapper.mapToUnifiedDefaultAddress(data);
    } catch (error) {
      throw error;
    }
  };
  public removeAddress = async (request: any) => {
    try {
      const { id } = request;
      const data = await this._addressController.removeAddress(id);
      return JMAddressMapper.mapToUnifiedRemoveAddress(data);
    } catch (error) {
      throw error;
    }
  };
  public insertAddress = async (body: any) => {
    try {
      const data = await this._addressController.insertAddress(body);
      return JMAddressMapper.mapToUnifiedInsertAddress(data);
    } catch (error) {
      throw error;
    }
  };
  public editAddress = async (requestData: any) => {
    try {
      const { id } = requestData;
      const data = await this._addressController.editAddress(id, requestData);
      return JMAddressMapper.mapToUnifiedEditAddress(data);
    } catch (error) {
      throw error;
    }
  };
}

export default JMAddressNetworkController;
