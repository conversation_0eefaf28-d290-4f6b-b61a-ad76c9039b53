import {FetchAlgoliaIndexSearchProps} from '../models/JMSearch/JMSearchModels';
import JMSearchNetworkController from './JMSearchNetworkController';

const searchController = new JMSearchNetworkController();

const service = () => {

  const fetchDiscoverMoreData = async (
    config?: FetchAlgoliaIndexSearchProps,
  ) => {
    try {
      return searchController.fetchDiscoverMoreData(config);
    } catch (error) {
      console.error('Error fetching discover more data:', error);
      throw error;
    }
  };

  const getSearchResults = async (queryParams: any) => {
    try {
      const result = await searchController.getSearchResults({q: 'oil'});
      return result;
    } catch (error) {
      console.error('API call failed:', error);
    }
  };

  const fetchRecommendedItems = async (queryParams: any) => {
    try {
      return await searchController.getRecommendedProducts(queryParams);
    } catch (error) {
      throw error;
    }
  };

  const fetchRecommendedItemsServiceability = async (bodyParams: any) => {
    try {
      return await searchController.fetchRecommendedItemsServiceability(
        bodyParams,
      );
    } catch (error) {
      throw error;
    }
  };

  const getBuyAgainItems = async (queryParams: any) => {
    try {
      return await searchController.getBuyAgainProducts(queryParams);
    } catch (error) {
      throw error;
    }
  };

  return {
    fetchDiscoverMoreData,
    getSearchResults,
    fetchRecommendedItems,
    getBuyAgainItems,
    fetchRecommendedItemsServiceability,
  };
};

const SearchService = service();

export default SearchService;
