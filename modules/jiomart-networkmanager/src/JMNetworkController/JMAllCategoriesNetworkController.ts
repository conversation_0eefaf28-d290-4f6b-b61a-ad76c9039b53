import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import JMBaseNetworkController, {
  type JMBaseNetworkControllerOptions,
} from '../base/JMBaseNetworkController';
import JMBAUAllCategoriesNetworkController from '../JMBAUNetworkController/JMBAUAllCategoriesNetworkController';
import JMJCPAllCategoriesNetworkController from '../JMJCPNetworkController/JMJCPAllCategoriesNetworkController';
import JMCategoryMapper from '../JMMapperManager/JMCategoryMapper';

class JMAllCategoriesNetworkController extends J<PERSON><PERSON><PERSON>workController {
  private _allCategoriesController: any;

  constructor(options?: JMBaseNetworkControllerOptions) {
    super(options);
    switch (this.options?.appSource) {
      case AppSourceType.JM_JCP:
        this._allCategoriesController =
          new JMJCPAllCategoriesNetworkController();
        break;
      case AppSourceType.JM_BAU:
        this._allCategoriesController =
          new JMBAUAllCategoriesNetworkController();
        break;
      default:
        this._allCategoriesController =
          new JMBAUAllCategoriesNetworkController();
    }
  }

  public fetchDepartments = async () => {
    try {
      let data: any = await this._allCategoriesController.fetchDepartments();

      return data;
    } catch (error) {
      throw error;
    }
  };
  public fetchAllCategories = async () => {
    try {
      let data: any = await this._allCategoriesController.fetchAllCategories();
      return JMCategoryMapper.mapToUnifiedCategoriesResponse(data);
    } catch (error) {
      throw error;
    }
  };
}

export default JMAllCategoriesNetworkController;
