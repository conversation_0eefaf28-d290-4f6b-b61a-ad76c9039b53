import JMBaseNetworkController, {
  JMBaseNetworkControllerOptions,
} from '../base/JMBaseNetworkController';
import JMJCPQCNetworkController from '../JMJCPNetworkController/JMJCPQCNetworkController';
import JMB<PERSON><PERSON><PERSON><PERSON>NetworkController from '../JMBAUNetworkController/JMBAUQCNetworkController';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';

export class JMQCNetworkController extends JMBaseNetworkController {
  private _qcController: any;

  constructor(options?: JMBaseNetworkControllerOptions) {
    super(options);
    switch (this.options?.appSource) {
      case AppSourceType.JM_JCP:
        this._qcController = new JMJCPQCNetworkController();
        break;
      case AppSourceType.JM_BAU:
        this._qcController = new JMBAUQCNetworkController();
        break;
      default:
        this._qcController = new JMBAUQCNetworkController();
    }
  }

  public getQCDetails = async () => {
    try {
      let data: any = await this._qcController.fetchQCDetails();
      return data;
    } catch (error) {
      throw error;
    }
  };
}
