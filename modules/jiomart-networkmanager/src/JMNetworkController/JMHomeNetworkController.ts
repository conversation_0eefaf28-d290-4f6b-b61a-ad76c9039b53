import {jsonParse} from '../../../jiomart-common/src/uiModals/JMResponseParser';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import JMBaseNetworkController, {
  type JMBaseNetworkControllerOptions,
} from '../base/JMBaseNetworkController';
import JMBAUHomeNetworkController from '../JMBAUNetworkController/JMBAUHomeNetworkController';
import JMJCPHomeNetworkController from '../JMJCPNetworkController/JMJCPHomeNetworkController';
import {
  JHTopDealProduct,
  JMCheckServiceabilityData,
  JMMcatInventoryApiResponse,
} from '../models/home/<USER>';
import {
  addStringPref,
  getPrefString,
} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '../../../jiomart-common/src/JMConstants';

class JMHomeNetworkController extends JMBaseNetworkController {
  private _homeController: any;

  constructor(options?: JMBaseNetworkControllerOptions) {
    super(options);
    switch (this.options?.appSource) {
      case AppSourceType.JM_JCP:
        this._homeController = new JMJCPHomeNetworkController();
        break;
      case AppSourceType.JM_BAU:
        this._homeController = new JMBAUHomeNetworkController();
        break;
      default:
        this._homeController = new JMBAUHomeNetworkController();
    }
  }

  /**
   * This is the serviceablity wrapper api that is currently getting used by JioMart webapp team,
   * But we can't use this api from mobile app as this wrapper is not properly maintained.
   * So we are writting the entire wrapper logic in the app end.
   */
  public checkServiceability = async (queryParams: {}) => {
    try {
      let data: any = await this._homeController.checkServiceability(
        queryParams,
      );
      return this.mapToCheckServiceabilityModel(data);
    } catch (error) {
      throw error;
    }
  };

  private mapToCheckServiceabilityModel = (
    data: any,
  ): JMCheckServiceabilityData => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return data;
      case AppSourceType.JM_BAU:
        const parseData = jsonParse<JMCheckServiceabilityData>(
          JSON.stringify(data?.result),
        );

        if (!parseData || parseData == null) {
          throw Error('mapToCheckServiceabilityModel : BAU - parsing error ');
        }

        return parseData;
      default:
        return data;
    }
  };

  public getMcatInventory = async (
    pincode: string,
    refreshIntervalInMin: number = 30,
    invalidateCacheAndRefetch: boolean = false,
  ) => {
    try {
      // Check cache first to avoid unnecessary API calls
      const cachedResult = await this.getCachedMCatInventoryData(
        refreshIntervalInMin,
      );
      if (cachedResult && !invalidateCacheAndRefetch) {
        console.log('Using cached McatInventory data');
        return cachedResult;
      }

      let data: any = await this._homeController.getMcatInventory(pincode);
      const inventoryData = this.mapToMcatInventoryModel(data);
      await this.saveCacheData(inventoryData);
      return inventoryData;
    } catch (error) {
      throw error;
    }
  };

  private getCachedMCatInventoryData = async (refreshIntervalInMin: number) => {
    const cachedData = await getPrefString(
      AsyncStorageKeys.MCAT_INVENTORY_DATA,
    );

    if (!cachedData) return null;

    try {
      const parsedCache = JSON.parse(cachedData);
      const currentTime = Date.now();
      const cacheAge = currentTime - (parsedCache.timestamp || 0);
      const refreshThreshold = refreshIntervalInMin * 60 * 1000;

      if (cacheAge < refreshThreshold) {
        console.log(`Cache hit: Data is ${Math.round(cacheAge / 1000)}s old`);
        return parsedCache.inventoryData;
      }

      console.log(
        `Cache expired: Data is ${Math.round(cacheAge / 60000)}min old`,
      );
      return null;
    } catch (error) {
      console.warn('Failed to parse cached data, fetching fresh data');
      return null;
    }
  };

  private saveCacheData = async (
    inventoryResponse: JMMcatInventoryApiResponse,
  ) => {
    console.log('SaveCacheData : ', inventoryResponse);
    if (inventoryResponse != null && inventoryResponse) {
      const cacheData = {
        inventoryData: inventoryResponse,
        timestamp: Date.now(),
      };
      await addStringPref(
        AsyncStorageKeys.MCAT_INVENTORY_DATA,
        JSON.stringify(cacheData),
      );
    }
  };

  private mapToMcatInventoryModel = (data: any): JMMcatInventoryApiResponse => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return data;
      case AppSourceType.JM_BAU:
        const parseData = jsonParse<JMMcatInventoryApiResponse>(
          JSON.stringify(data),
        );

        if (!parseData || parseData == null) {
          throw Error('mapToMcatInventoryModel : BAU - parsing error ');
        }

        return parseData;
      default:
        return data;
    }
  };

  public getSingleTopDeal = async (queryParams: {}, customHeaders: {}) => {
    try {
      let data: any = await this._homeController.getSingleTopDeal(
        queryParams,
        customHeaders,
      );
      return this.mapToTopDealProductsModel(data);
    } catch (error) {
      throw error;
    }
  };

  public getHomepageData = async (queryParams: {}, customHeaders: {}) => {
    console.log('----- Home Api Called ------ ');
    console.log('setCurrentSelectedSlug : home api called',queryParams)

    try {
      let data: any = await this._homeController.getHomepageData(
        queryParams,
        customHeaders,
      );
      return data;
    } catch (error) {
      throw error;
    }
  };

  public getWidgets = async (queryParams: {}, customHeaders: {}) => {
    console.log('----- getWidgets Api Called ------ ');
    try {
      let data: any = await this._homeController.getWidgets(
        queryParams,
        customHeaders,
      );
      return data;
    } catch (error) {
      throw error;
    }
  };

  public getSingleWidgets = async (queryParams: {}, customHeaders: {}) => {
    console.log('----- getSingleWidgets Api Called ------ ');
    try {
      let data: any = await this._homeController.getSingleWidget(
        queryParams,
        customHeaders,
      );
      return data;
    } catch (error) {
      throw error;
    }
  };

  public getCategoryDetails = async (body: {}, customHeaders: {}) => {
    console.log('----- getCategoryDetails Api Called ------ ');
    try {
      let data: any = await this._homeController.getCategoryDetails(
        body,
        customHeaders,
      );
      return data;
    } catch (error) {
      throw error;
    }
  };

  private mapToTopDealProductsModel = (data: any): JHTopDealProduct[] => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return data;
      case AppSourceType.JM_BAU:
        const parseData = jsonParse<JHTopDealProduct[]>(
          JSON.stringify(data?.products),
        );

        if (!parseData || parseData == null) {
          throw Error('mapToTopDealProductsModel : BAU - parsing error ');
        }

        return parseData;
      default:
        return data;
    }
  };

  public fetchProductDetailsFromAlgolia = async (bodyParams: any) => {
    console.log('fetchProductDetailsFromAlgolia : ', bodyParams);
    try {
      let data: any =
        await this._homeController.fetchProductDetailsFromAlgolia(
          bodyParams,
        );
      return data;
    } catch (error) {
      throw error;
    }
  };
}

export default JMHomeNetworkController;
