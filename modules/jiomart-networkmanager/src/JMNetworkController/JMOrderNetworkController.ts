import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import JMBaseNetworkController, {
  type JMBaseNetworkControllerOptions,
} from '../base/JMBaseNetworkController';
import JMBAU<PERSON>rderNetworkController from '../JMBAUNetworkController/JMBAUOrderNetworkController';
import JMJCP<PERSON>rderNetworkController from '../JMJCPNetworkController/JMJCPOrderNetworkController';
import <PERSON><PERSON><PERSON>r<PERSON>apper from '../JMMapperManager/JMOrderMapper';

class J<PERSON><PERSON>rNetworkController extends JMBaseNetworkController {
  private _orderController: any;

  constructor(options?: JMBaseNetworkControllerOptions) {
    super(options);
    switch (this.options?.appSource) {
      case AppSourceType.JM_JCP:
        this._orderController = new JMJCPOrderNetworkController();
        break;
      case AppSourceType.JM_BAU:
        this._orderController = new JMBAUOrderNetworkController();
        break;
      default:
        this._orderController = new JMBAUOrderNetworkController();
    }
  }

  public getOrderList = async (request: any) => {
    try {
      const data = await this._orderController.fetchOrderList(request);
      return JMOrderMapper.mapToUnifiedOrderResponse(data);
    } catch (error) {
      throw error;
    }
  };

  public getRefundList = async (request: any) => {
    try {
      const data = await this._orderController.fetchRefundList(request);
      return JMOrderMapper.mapToUnifiedRefundResponse(data);
    } catch (error) {
      throw error;
    }
  };

  public getRefundDetails = async (request: any) => {
    try {
      const data = await this._orderController.fetchRefundDetails(request);
      return JMOrderMapper.mapToUnifiedRefundDetailsResponse(data);
    } catch (error) {
      throw error;
    }
  };
}

export default JMOrderNetworkController;
