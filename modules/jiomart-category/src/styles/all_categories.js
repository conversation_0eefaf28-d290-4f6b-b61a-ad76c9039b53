import {StyleSheet} from 'react-native';
import {getScreenDim, rh, rw} from '../../../jiomart-common/src/JMResponsive';

const allCategoriesStyles = StyleSheet.create({
  container: {
    width: getScreenDim.width,
    flex: 1,
    backgroundColor: 'white',
    flexDirection: 'row',
    paddingBottom: 12
  },
  left_container: {
    width: rw(88),
    borderRightWidth: 1,
    borderRightColor: '#E0E0E0',
    paddingBottom: 24,
  },
  right_container: {
    flex: 1,
  },
  scrollview: {
    flex: 1,
    backgroundColor: 'white',
  },
  title_container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    minHeight: rh(80),
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    marginHorizontal: rw(12),
    marginVertical: rh(8),
    paddingHorizontal: rw(16),
    paddingVertical: rh(12),
  },
  title: {
    maxWidth: rw(180),
  },
  divider: {
    width: rw(72),
    height: rh(3),
    alignSelf: 'center',
  },
  verticalDivider: {
    width: '4%',
    height: '100%',
    alignSelf: 'center',
    borderTopRightRadius: 4,
    borderBottomRightRadius: 4,
    backgroundColor: '#0078AD',
  },
  selectedCategoryBox: {
    width: '100%',
    height: rh(108),
    position: 'absolute',
    backgroundColor: '#E5F1F7',
    flexDirection: 'row',
    borderBottomRightRadius: 12,
    borderTopRightRadius: 12,
  },
});

export default allCategoriesStyles;
