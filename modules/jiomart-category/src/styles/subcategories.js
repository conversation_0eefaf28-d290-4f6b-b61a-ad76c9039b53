import {StyleSheet} from 'react-native';
import {rh, rw} from '../../../jiomart-common/src/JMResponsive';

const subCategoriesStyles = StyleSheet.create({
  subcategory_container: {
    // height: 76*Height,
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingRight: rw(16),
    paddingVertical: rh(12),
  },

  content_container: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  toggle_container: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  subcategory_text: {
    flex: 0.95,
    marginLeft: rw(12),
  },
  products_outer_container: {
    flex: 1,
    marginLeft: rw(16),
    marginRight: rw(16),
    marginTop: rh(12),
    marginBottom: rh(14),
    paddingVertical: rh(4),
    paddingLeft: rw(8),
  },
});

export default subCategoriesStyles;
