import React from 'react';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import BottomNavBarAnimationHandler from '../../jiomart-general/src/ui/BottomNavBar/BottomNavBarAnimationHandler';
import ScreenSlot, { DeeplinkHandler } from '../../jiomart-general/src/ui/JMScreenSlot';
import AllCategories from './components/AllCategories';
import useAllCategoriesScreenController from './controller/useAllCategoriesScreenController';
import AllCategoriesShimmer from './skeleton/AllCategoriesShimmer';
import { JMAllCategoriesScreenProps } from './types/JMAllCategoriesScreenProps';

const AllCategoriesScreen = (props: JMAllCategoriesScreenProps) => {
  const {
    navigation,
    navigationBean,
    config,
    category,
    handleL1CategoryPress,
    handleL3CategoryPress,
  } = useAllCategoriesScreenController(props);

  const { handleBottomNavBarAnimation } = BottomNavBarAnimationHandler();

  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={bean => (
        <ScreenSlot
          navigationBean={bean}
          navigation={navigation}
          children={_ => {
            return (
              <GestureHandlerRootView>
                {category.data?.length > 0 ? (
                  <AllCategories
                    allCategory={category.data}
                    handleScroll={e => {
                      if (e.nativeEvent.layoutMeasurement.height +
                        e.nativeEvent.contentOffset.y <=
                        e.nativeEvent.contentSize.height - 80) {
                        handleBottomNavBarAnimation(e);
                      }
                    }}
                    onL1CategoryPress={cat => {
                      handleL1CategoryPress(cat, config?.l1_category?.cta);
                    }}
                    onL3CategoryPress={cat => {
                      handleL3CategoryPress(cat, config?.l3_category?.cta);
                    }}
                  />
                ) : (
                  <>
                    {
                      <AllCategoriesShimmer />
                    }
                  </>
                )}
              </GestureHandlerRootView>
            );
          }}
        />
      )}
    />
  );
};

export default AllCategoriesScreen;
