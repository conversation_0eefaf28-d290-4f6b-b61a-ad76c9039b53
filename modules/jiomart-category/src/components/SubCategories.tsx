import React from 'react';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {useColor} from '@jio/rn_components/src/theme/color/useColor';
import {Image, Pressable, View} from 'react-native';
import Animated, {withTiming} from 'react-native-reanimated';
import useSubCategoriesController from '../controller/useSubCategoriesController';
import {CustomCategoryItems} from '../model/AllCategoriesResponse';
import subCategoriesStyles from '../styles/subcategories';
import Chevron from './Chevron';
import Products from './Products';
import {rh, rw} from '../../../jiomart-common/src/JMResponsive';
import MicroTap from '../../../jiomart-general/src/ui/Animation/Micro/MicroTap';

type PropsType = {
  childElement: any;
  onL3CategoryPress: (cat: CustomCategoryItems) => void;
};

const SubCategories = (props: PropsType) => {
  const {
    heightValue,
    Sel,
    listRef,
    imageUrl,
    imageRatio,
    getSubCategoryText,
    articleColor,
    progress,
    heightAnimationStyle,
    handleSubCategoryPress,
    onL3CategoryPress,
  } = useSubCategoriesController(props);
  const backgroundColor = useColor('primary_grey_20');
  return (
    <View
      style={{
        overflow: 'hidden',
        marginHorizontal: rw(12),
        marginBottom: rh(8),
        borderWidth: 1,
        borderColor: '#E0E0E0',
        borderRadius: 12,
      }}>
      <Pressable
        onPress={() => handleSubCategoryPress()}
        style={subCategoriesStyles.subcategory_container}>
        <View style={[subCategoriesStyles.content_container]}>
          {imageUrl ? (
            <View
              style={[
                {
                  width: 48 * imageRatio,
                  height: 48 * imageRatio,
                  borderRadius: 200,
                  backgroundColor,
                  justifyContent: 'center',
                  marginLeft: 16,
                  overflow: 'hidden',
                  // padding:2
                },
              ]}>
              <Image
                style={{
                  alignSelf: 'center',
                  height: '100%',
                  width: '100%',
                }}
                source={imageUrl}
                resizeMode="contain"
              />
            </View>
          ) : (
            // <JMDefaultImage
            //   width={48 * imageRatio}
            //   height={48 * imageRatio}
            //   style={{
            //     alignSelf: 'center',
            //     justifyContent: 'center',
            //     marginLeft: 16,
            //     overflow: 'hidden',
            //   }}
            // />
            <></>
          )}
          <JioText
            appearance={JioTypography.BODY_XS_BOLD}
            text={getSubCategoryText(props.childElement.name)}
            color="primary_grey_80"
            maxLines={2}
            style={subCategoriesStyles.subcategory_text}
          />
        </View>
        <MicroTap color={articleColor} borderRadius={100} padding={0}>
          <View style={subCategoriesStyles.toggle_container}>
            <Chevron progress={progress} />
          </View>
        </MicroTap>
      </Pressable>

      <Animated.View style={heightAnimationStyle}>
        <Animated.View
          onLayout={event => {
            const {height} = event.nativeEvent.layout;
            heightValue.value = withTiming(height);
          }}
          ref={listRef}
          style={{
            position: 'absolute',
            width: '100%',
            top: 0,
          }}>
          {Sel ? (
            <View style={subCategoriesStyles.products_outer_container}>
              {props.childElement.childs.map(
                (l3Category: CustomCategoryItems, index: number) => {
                  return (
                    <Products
                      key={`l3-cat-${l3Category?.slug}-${index}`}
                      onPress={() => {
                        onL3CategoryPress(l3Category);
                      }}
                      name={l3Category?.name || ''}
                      style={{
                        marginTop: index == 0 ? 0 : 20,
                      }}
                    />
                  );
                },
              )}
            </View>
          ) : null}
        </Animated.View>
      </Animated.View>
    </View>
  );
};

export default SubCategories;
