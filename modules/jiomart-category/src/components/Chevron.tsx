import { JioIcon } from '@jio/rn_components';
import { IconColor } from '@jio/rn_components/src/index.types';
import React from 'react';
import { StyleSheet } from 'react-native';
import Animated, { SharedValue, useAnimatedStyle } from 'react-native-reanimated';

type Props = {
  progress: Readonly<SharedValue<0 | 1>>;
};

const Chevron = ({progress}: Props) => {
  const iconStyle = useAnimatedStyle(() => ({
    transform: [{rotate: `${progress.value * -180}deg`}],
  }));

  return (
    <Animated.View style={iconStyle}>
       <JioIcon ic="IcChevronDown" color={IconColor.PRIMARY60} />
    </Animated.View>
  );
};

export default Chevron;

const styles = StyleSheet.create({
  chevron: {
    width: 24,
    height: 24,
  },
});
