import React from 'react';
import {JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {
  Pressable,
  View,
  type StyleProp,
  type ViewStyle,
} from 'react-native';
import useProductsController from '../controller/useProductsController';
import productsStyles from '../styles/products';
import MicroTap from '../../../jiomart-general/src/ui/Animation/Micro/MicroTap';
type PropsType = {
  onPress: () => void;
  name: string;
  style?: StyleProp<ViewStyle>;
};

const Products = (props: PropsType) => {
  const {name, style, getProductText, articleColor, handleProductPress} =
    useProductsController(props);
  return (
    <Pressable
      onPress={handleProductPress}
      style={[productsStyles.products_container, style]}>
      <JioText
        appearance={JioTypography.BODY_XS}
        text={getProductText(name)}
        color="primary_grey_80"
        maxLines={2}
        style={productsStyles.products_text}
      />
      <MicroTap color={articleColor} borderRadius={100} padding={0}>
        <View style={productsStyles.toggle_container}>
          {/* insert icon right arrow  */}
        </View>
      </MicroTap>
    </Pressable>
  );
};

export default Products;
