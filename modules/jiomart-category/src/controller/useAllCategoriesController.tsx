import { useCallback, useRef, useState } from 'react';
import { Platform, ScrollView } from 'react-native';
import {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { rh } from '../../../jiomart-common/src/JMResponsive';
import handleHapticFeedback from '../../../jiomart-common/src/utils/JMHapticFeedback';
import SubCategories from '../components/SubCategories';
import { CustomCategoryItems } from '../model/AllCategoriesResponse';

const useAllCategoriesController = (props: any) => {
  const { allCategory, handleScroll, shouldShowDeliverToBar, shouldShowBottomNavBar, onL3CategoryPress } =
    props;

  const [selectedIndex, setSelectedIndex] = useState(0);

  const animation = useSharedValue(0);
  const dividerColorAnimation = useSharedValue(1);
  const selectedDividerColorAnimation = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: animation.value }],
  }));

  const dividerColorAnimatedStyle = useAnimatedStyle(() => {
    const backgroundColor =
      dividerColorAnimation.value > 0 && dividerColorAnimation.value < 1
        ? 'transparent'
        : '#F5F5F5';
    return {
      backgroundColor,
    };
  }, []);

  const selectedDividerColorAnimatedStyle = useAnimatedStyle(() => {
    const backgroundColor =
      selectedDividerColorAnimation.value > 0 &&
        selectedDividerColorAnimation.value < 1
        ? 'transparent'
        : selectedDividerColorAnimation.value === 1
          ? 'transparent'
          : '#F5F5F5';
    return {
      backgroundColor,
    };
  }, []);

  const handleAnimation = useCallback((index: number) => {
    selectedDividerColorAnimation.value = 0;
    dividerColorAnimation.value = 0;
    animation.value = withTiming(index * rh(108), { duration: 500 });
    selectedDividerColorAnimation.value = withTiming(1, { duration: 500 });
    dividerColorAnimation.value = withTiming(1, { duration: 500 });
  }, []);

  const scrollViewRef = useRef<ScrollView>(null);

  const scrollToTop = useCallback((index: number) => {
    const bottomPosition = index * rh(108);
    scrollViewRef.current?.scrollTo({
      x: 0,
      y: bottomPosition - 3 * rh(108),
      animated: true,
    });
  }, []);

  const handleCategoryAnimation = (index: number, l1CategoryName: string) => {
    handleHapticFeedback(
      Platform.OS == 'android' ? 'impactMedium' : 'impactLight',
    );

    setSelectedIndex(index);
    handleAnimation(index);
    setTimeout(() => {
      scrollToTop(index);
    }, 600);
  };

  const keyExtractor = (item: CustomCategoryItems, index: number) =>
    item.slug ?? index.toString();

  const renderItem = ({
    item: childElement,
    index,
  }: {
    item: CustomCategoryItems;
    index: number;
  }) => {
    return (
      <SubCategories
        key={`child-${index}-${childElement?.slug}`}
        childElement={childElement}
        onL3CategoryPress={onL3CategoryPress}
      />
    );
  };

  return {
    ...props,
    allCategory,
    handleScroll,
    shouldShowDeliverToBar,
    shouldShowBottomNavBar,
    scrollViewRef,
    animatedStyle,
    selectedIndex,
    selectedDividerColorAnimatedStyle,
    dividerColorAnimatedStyle,
    handleCategoryAnimation,
    keyExtractor,
    renderItem,
  };
};

export default useAllCategoriesController;
