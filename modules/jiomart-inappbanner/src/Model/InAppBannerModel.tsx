import {NavigationBean} from '../../../jiomart-common/src/JMNavGraphUtil';
import type {JMInAppBannerProps} from '../component/JMInAppBanner';

export enum InAppBannerCacheKeys {
  INAPP_BANNER_LIST_DATA = 'PREFERENCE_KEY_IS_INAPP_BANNER_LIST_DATA',
}

export enum InAppBannerViewType {
  LEFT_BANNER = 'left_banner',
  RIGHT_BANNER = 'right_banner',
}

export interface InAppBannerModel {
  id?: number;
  campaign_id?: string;
  orderNo?: number;
  viewType?: number;
  title?: string;
  filterId?: string;
  showShimmer?: boolean;
  subTitle?: string;
  headerTitle?: string;
  type?: string;
  subHeaderTitle?: string;
  profileName?: string;
  profileImage?: string;
  bgImage?: string;
  bgImageSizeDimen?: InAppBannerImageSizeDimen;
  showElement?: boolean;
  bgColor?: string;
  cta?: NavigationBean;
  imageUrl?: string;
  bannerUrl?: string;
  pId?: number;
  iconUrl?: string;
  rnVersion?: string;
  iosVersion?: string;
  androidVersion?: string;
  platform?: string;
  count?: number;
  frequency?: number;
  startDate?: string;
  endDate?: string;
  startTime?: string;
  endTime?: string;
  period?: number;
  pincode?: string;
  bannerClicked?: string;
  bannerDetail: JMInAppBannerProps;
}

export interface InAppBannerCacheModel {
  launchCount?: number;
  count?: number;
  bannerClicked?: boolean;
  currentDate?: string;
  frequency?: number;
  campaign_id?: string;
  period?: number;
}

export interface InAppBannerImageSizeDimen {
  width?: number;
  height?: number;
  viewBox_width?: number;
  viewBox_height?: number;
}
