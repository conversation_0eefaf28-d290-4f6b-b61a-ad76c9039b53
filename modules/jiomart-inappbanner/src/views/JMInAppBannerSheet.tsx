import React, {useEffect} from 'react';
import JMInAppBanner, {AnimationPosition} from '../component/JMInAppBanner';
import JMInAppBannerController from '../controller/JMInAppBannerController';
import type {InAppBannerModel} from '../Model/InAppBannerModel';
import {useGlobalState} from '../../../jiomart-general/src/context/JMGlobalStateProvider';
import {navigateTo} from '../../../jiomart-general/src/navigation/JMNavGraph';
import type {NavigationBean} from '../../../jiomart-common/src/JMNavGraphUtil';
import type {NativeStackNavigationProp} from '@react-navigation/native-stack';

interface JMInAppBannerSheetProps extends InAppBannerModel {
  navigation: NativeStackNavigationProp<any>;
}

const JMInAppBannerSheet = (props: JMInAppBannerSheetProps) => {
  const inAppBannerData = props;

  const {setInAppBanner} = useGlobalState();

  useEffect(() => {
    const initiate = async () => {
      await JMInAppBannerController.updateInAppBannerLocalCacheDataAfterShown(
        inAppBannerData as InAppBannerModel,
      );
    };
    initiate();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <JMInAppBanner
      visible={!!inAppBannerData?.bannerDetail?.media?.mediaUrl}
      {...inAppBannerData?.bannerDetail}
      animationPosition={
        inAppBannerData?.viewType as unknown as AnimationPosition
      }
      onBackDropClick={() => {
        JMInAppBannerController.inAppBannerDataPromise = null;
        setInAppBanner(null);
        inAppBannerData?.bannerDetail?.onBackDropClick?.();
      }}
      onPress={() => {
        JMInAppBannerController.inAppBannerDataPromise = null;
        setInAppBanner(null);
        inAppBannerData?.bannerDetail?.onPress?.();
        navigateTo(inAppBannerData?.cta as NavigationBean, props.navigation);
      }}
      onClose={() => {
        JMInAppBannerController.inAppBannerDataPromise = null;
        inAppBannerData?.bannerDetail?.onClose?.();
        setInAppBanner(null);
      }}
    />
  );
};

export default JMInAppBannerSheet;
