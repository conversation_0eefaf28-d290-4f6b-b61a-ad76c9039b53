import {
  InAppBanner<PERSON>ache<PERSON><PERSON>s,
  InAppBannerCacheModel,
  InAppBannerModel,
} from '../Model/InAppBannerModel';
import {
  addStringPref,
  getPrefString,
} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {isNullOrUndefinedOrEmpty} from '../../../jiomart-common/src/JMObjectUtility';
import {getVersionSpecificFilterListData} from '../../../jiomart-common/src/utils/JMCommonFunctions';
import { getConfigFileDataAsync } from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileManager';
import { JMConfigFileName } from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';

export default class JMInAppBannerController {
  static inAppBannerDataPromise : Promise<InAppBannerModel | null> | null = null;

 static fetchInAppBannerDataPromise = async () : Promise<any|null> =>  {
     const inAppBannerData = await getConfigFileDataAsync(JMConfigFileName.JMInAppBannerFileName)
     if(inAppBannerData?.inAppBanner){
        return await this.filterdInAppBannerData(inAppBannerData?.inAppBanner)
     }
  }

  static fetchInAppBannerData = async () => {
    this.inAppBannerDataPromise = this.fetchInAppBannerDataPromise()
  }

  private static filterdInAppBannerData = async (
    inAppBannerData: InAppBannerModel[],
  ): Promise<InAppBannerModel | undefined | null> => {
    console.log(
      '🚀 ~ useJMInAppBannerController ~ inAppBannerData:',
      inAppBannerData,
    );
    const filterVersionPlatformData =
      getVersionSpecificFilterListData(inAppBannerData);
    if (!filterVersionPlatformData) return;
    const filterDataAfterDateTime =
      await JMInAppBannerController.filterInAppBannerDataTimeBasis(
        filterVersionPlatformData,
      );
    const sortedData = filterDataAfterDateTime.sort();
    const filterInAppData =
      await JMInAppBannerController.filterInAppDataAccordingToLocalCache(
        sortedData,
      );
    return filterInAppData && filterInAppData.length > 0
      ? filterInAppData[0]
      : undefined;
  };
  private static filterInAppBannerDataTimeBasis = async (
    inAppBannerData: InAppBannerModel[],
  ): Promise<InAppBannerModel[]> => {
    const now = new Date();
    const pad = (n: number) => (n < 10 ? `0${n}` : `${n}`);
    const currentTimeStr = `${pad(now.getHours())}:${pad(now.getMinutes())}`;
    const currentMinutes = JMInAppBannerController.parseTime(currentTimeStr);

    return inAppBannerData.filter(banner => {
      const startDate = JMInAppBannerController.parseDateTime(banner.startDate);
      const endDate = JMInAppBannerController.parseDateTime(banner.endDate);
      const startTime = JMInAppBannerController.parseTime(banner.startTime);
      const endTime = JMInAppBannerController.parseTime(banner.endTime);

      // Date check
      if (startDate && now < startDate) return false;
      if (endDate && now > endDate) return false;

      // Time check (if both present)
      if (
        startTime !== undefined &&
        endTime !== undefined &&
        currentMinutes !== undefined
      ) {
        if (currentMinutes < startTime || currentMinutes > endTime)
          return false;
      }
      return true;
    });
  };
  private static filterInAppDataAccordingToLocalCache = async (
    inAppBannerData: InAppBannerModel[],
  ) => {
    const cacheList =
      await JMInAppBannerController.getCacheInAppBannerDataList();
    const results = await Promise.all(
      inAppBannerData.map(async bannerData => {
        if (bannerData.campaign_id) {
          const cacheData =
            await JMInAppBannerController.getCacheInAppBannerDataForCampaign(
              bannerData.campaign_id,
              cacheList,
            );
          if (cacheData) {
            // Count check (lifetime)
            if (
              bannerData.count !== undefined &&
              cacheData?.count !== undefined &&
              cacheData.count >= bannerData.count
            ) {
              return false;
            }
            // Frequency check (per day)
            if (
              bannerData.frequency !== undefined &&
              cacheData?.frequency !== undefined &&
              cacheData.currentDate === JMInAppBannerController.currentDate &&
              cacheData.frequency >= bannerData.frequency
            ) {
              return false;
            }
            // period check
            if (bannerData.period !== undefined) {
              if (
                cacheData?.launchCount !== undefined &&
                cacheData.period !== undefined
              ) {
                if (
                  JMInAppBannerController.isNextOpenLaunchCount(
                    cacheData.period,
                    cacheData.launchCount,
                    bannerData.period,
                  )
                ) {
                  return true;
                } else {
                  await JMInAppBannerController.addCacheInAppBannerDataList({
                    ...cacheData,
                    launchCount: cacheData.launchCount + 1,
                    currentDate: JMInAppBannerController.currentDate,
                  });
                  return false;
                }
              }
            }
          }
          return true;
        }
        return false;
      })
    );
    return inAppBannerData.filter((_, idx) => results[idx]);
  };
  static updateInAppBannerLocalCacheDataAfterShown = async (
    bannerData: InAppBannerModel,
  ) => {
    if (bannerData.campaign_id) {
      const cacheData =
        await JMInAppBannerController.getCacheInAppBannerDataForCampaign(
          bannerData.campaign_id,
        );
      if (cacheData) {
        await JMInAppBannerController.addCacheInAppBannerDataList({
          ...cacheData,
          launchCount: (cacheData?.launchCount ?? 0) + 1,
          count: (cacheData?.count ?? 0) + 1,
          frequency:
            bannerData.frequency !== undefined &&
            cacheData?.frequency !== undefined &&
            cacheData.currentDate !== JMInAppBannerController.currentDate
              ? 1
              : (cacheData?.frequency ?? 0) + 1,
          period:
            bannerData.period !== undefined
              ? (cacheData?.period ?? 0) + 1
              : bannerData?.period,
          currentDate: JMInAppBannerController.currentDate,
        });
      } else {
        await JMInAppBannerController.addCacheInAppBannerDataList({
          launchCount: 2,
          count: 1,
          frequency: 1,
          period: 2,
          currentDate: JMInAppBannerController.currentDate,
          campaign_id: bannerData?.campaign_id,
        });
      }
    }
  };
  private static getCacheInAppBannerDataList = async (): Promise<
    InAppBannerCacheModel[] | undefined
  > => {
    const cacheList = await getPrefString(
      InAppBannerCacheKeys.INAPP_BANNER_LIST_DATA,
    );
    if (cacheList && !isNullOrUndefinedOrEmpty(cacheList)) {
      const listData = JSON.parse(cacheList) as InAppBannerCacheModel[];
      if (listData) {
        return listData;
      }
    }
    return undefined;
  };
  private static getCacheInAppBannerDataForCampaign = async (
    campaignId: string,
    cacheListData: InAppBannerCacheModel[] | undefined = undefined,
  ): Promise<InAppBannerCacheModel | undefined> => {
    const cacheList =
      cacheListData ??
      (await JMInAppBannerController.getCacheInAppBannerDataList());
    if (cacheList) {
      const cacheModel = cacheList.find(it => it.campaign_id === campaignId);
      if (cacheModel) {
        return cacheModel;
      }
    }
    return undefined;
  };
  private static addCacheInAppBannerDataList = async (
    model: InAppBannerCacheModel,
  ) => {
    const cacheList =
      await JMInAppBannerController.getCacheInAppBannerDataList();
    if (cacheList) {
      const index = cacheList.findIndex(
        it => it.campaign_id === model.campaign_id,
      );
      if (index != -1) {
        cacheList[index] = model;
      } else {
        cacheList.push(model);
      }
      await addStringPref(
        InAppBannerCacheKeys.INAPP_BANNER_LIST_DATA,
        JSON.stringify(cacheList),
      );
    } else {
      await addStringPref(
        InAppBannerCacheKeys.INAPP_BANNER_LIST_DATA,
        JSON.stringify([model]),
      );
    }
  };
  // Helper to convert DD/MM/YYYY HH:MM to Date
  private static parseDateTime = (dateTimeStr?: string): Date | undefined => {
    if (!dateTimeStr || isNullOrUndefinedOrEmpty(dateTimeStr)) return undefined;
    const [datePart, timePart] = dateTimeStr.split(' ');
    if (!datePart) return undefined;
    const [day, month, year] = datePart.split('/').map(Number);
    let hour = 0,
      minute = 0;
    if (timePart) {
      [hour, minute] = timePart.split(':').map(Number);
    }
    return new Date(year, month - 1, day, hour, minute);
  };
  // Helper to convert DD/MM/YYYY to Date
  private static parseDateWithoutTime = (dateStr?: string) => {
    if (!dateStr || isNullOrUndefinedOrEmpty(dateStr)) return undefined;
    const [day, month, year] = dateStr.split('/').map(Number);
    return new Date(year, month - 1, day);
  };
  // Helper to convert HH:MM to minutes since midnight
  private static parseTime = (timeStr?: string) => {
    if (!timeStr || isNullOrUndefinedOrEmpty(timeStr)) return undefined;
    const [hour, minute] = timeStr.split(':').map(Number);
    return hour * 60 + minute;
  };
  private static currentDate = (() => {
    const now = new Date();
    const pad = (n: number) => (n < 10 ? `0${n}` : `${n}`);
    return `${pad(now.getDate())}/${pad(
      now.getMonth() + 1,
    )}/${now.getFullYear()}`;
  })();
  private static isNextOpenLaunchCount = (
    count: number,
    totalLaunchCount: number,
    actualPeriod: number,
  ): boolean => {
    const num = 1 + (count - 1) * actualPeriod;
    if (num == totalLaunchCount) return true;
    return false;
  };
}
