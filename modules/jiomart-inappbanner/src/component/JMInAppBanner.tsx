import {
  StyleSheet,
  TouchableWithoutFeedback,
  Pressable,
  Platform,
  StatusBar,
} from 'react-native';
import React, {useEffect, useRef} from 'react';
import Animated, {
  interpolate,
  runOnJS,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import {rh, rw} from '../../../jiomart-common/src/JMResponsive';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {JioIcon} from '@jio/rn_components';
import {IconColor, type JioIconProps} from '@jio/rn_components/src/index.types';
import CustomMediaRendered, {
  type CustomMediaRenderedProps,
} from '../../../jiomart-general/src/ui/CustomMediaRendered';

export type AnimationPosition =
  | 'bottom-left'
  | 'bottom-right'
  | 'top-left'
  | 'top-right';
export type JMInAppBannerProps = {
  visible?: boolean;
  onBackDropClick?: () => void;
  onPress?: () => void;
  onClose?: () => void;
  disabledBackDropClick?: boolean;
  imageWidth?: number;
  imageHeight?: number;
  media?: Omit<CustomMediaRenderedProps, 'width' | 'height'>;
  animationDuration?: number;
  animationPosition?: AnimationPosition;
  icon?: JioIconProps;
  iconPosition?: {
    top?: number;
    right?: number;
    left?: number;
    bottom?: number;
  };
  bannerPosition?: {
    bottom?: number;
    right?: number;
    left?: number;
    top?: number;
  };
};

const JMInAppBanner = (props: JMInAppBannerProps) => {
  const {
    visible = false,
    onBackDropClick,
    onPress,
    onClose,
    disabledBackDropClick = false,
    imageWidth = 200,
    imageHeight = 350,
    animationDuration = 500,
    animationPosition,
    bannerPosition,
    iconPosition,
    icon,
    media,
  } = props;

  const insets = useSafeAreaInsets();
    const statusBarRef = useRef(null);

  let updateBannerPosition = {};
  const bottom = insets.bottom + (bannerPosition?.bottom ?? 16);
  const left = insets.left + (bannerPosition?.left ?? 16);
  const right = insets.right + (bannerPosition?.right ?? 16);
  const top = insets.top + (bannerPosition?.top ?? 16);

  switch (animationPosition) {
    case 'bottom-left':
      updateBannerPosition = {
        bottom,
        left,
      };
      break;
    case 'bottom-right':
      updateBannerPosition = {
        bottom,
        right,
      };
      break;
    case 'top-left':
      updateBannerPosition = {
        top,
        left,
      };
      break;
    case 'top-right':
      updateBannerPosition = {
        top,
        right,
      };
      break;
    default:
      break;
  }

  const fadeValue = useSharedValue(0);
  const imageScale = useSharedValue(0);
  const imageOpacity = useSharedValue(0);
  const position = useDerivedValue(() => {
    const scale = imageScale.value;
    switch (animationPosition) {
      case 'bottom-left':
        return {
          bottom: bottom - ((1 - scale) * imageHeight) / 2,
          left: left + ((scale - 1) * imageWidth) / 2,
        };

      case 'bottom-right':
        return {
          bottom: bottom - ((1 - scale) * imageHeight) / 2,
          right: right - ((1 - scale) * imageWidth) / 2,
        };

      case 'top-left':
        return {
          top: top + ((scale - 1) * imageHeight) / 2,
          left: left + ((scale - 1) * imageWidth) / 2,
        };

      case 'top-right':
        return {
          top: top + ((scale - 1) * imageHeight) / 2,
          right: right - ((1 - scale) * imageWidth) / 2,
        };
      default:
        return {};
    }
  });

  const backDropStyle = useAnimatedStyle(() => ({
    opacity: interpolate(fadeValue.value, [0, 1], [0, 1]),
  }));

  const animatedImageStyle = useAnimatedStyle(() => ({
    transform: [
      {
        scale: imageScale.value,
      },
    ],
    ...position.value,
    opacity: imageOpacity.value,
  }));

  const animateImage = () => {
    'worklet';
    imageOpacity.value = withTiming(1, {duration: animationDuration});
    imageScale.value = withTiming(1, {duration: animationDuration});
  };

  const resetImageAnimation = (callback?: () => void) => {
    'worklet';
    imageScale.value = withTiming(0, {duration: animationDuration}, () => {
      callback ? runOnJS(callback)() : null;
    });
    imageOpacity.value = withTiming(0, {duration: animationDuration});
  };

  const open = () => {
    fadeValue.value = withTiming(1, {duration: animationDuration});
    animateImage();
  };

  const close = (callback?: () => void) => {
    'worklet';
    fadeValue.value = withTiming(0, {duration: animationDuration});
    resetImageAnimation(callback);
  };

  useEffect(() => {
    if (visible && media?.mediaUrl && animationPosition) {
      open();
    }
    return () => {
      statusBarRef.current?.setBackgroundColor?.('rgba(0, 120, 173, 1)', false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible, media?.mediaUrl, animationPosition]);

  const styles = StyleSheet.create({
    modalBackground: {
      ...StyleSheet.absoluteFillObject,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      zIndex: 100000000,
    },
    position: {
      position: 'absolute',
    },
    animatedImageContainer: {
      position: 'absolute',
      zIndex: 100000002,
      ...updateBannerPosition,
      alignItems: 'center',
      justifyContent: 'flex-end',
    },
    animatedImage: {
      width: rw(imageWidth),
      height: rh(imageHeight),
    },
    iconPosition: {
      position: 'absolute',
      top: 0,
      right: 0,
      ...iconPosition,
    },
  });

  return (
    <>
      {Platform.OS === 'android' ? (
        <StatusBar
          ref={statusBarRef}
          barStyle="light-content"
          backgroundColor="rgba(0,0,0,0.95)"
        />
      ) : null}
      <TouchableWithoutFeedback
        onPress={() => {
          if (!disabledBackDropClick) {
            close(onBackDropClick);
          }
        }}>
        <Animated.View style={[styles.modalBackground, backDropStyle]} />
      </TouchableWithoutFeedback>

      <TouchableWithoutFeedback style={styles.position}>
        <>
          {media?.mediaUrl && (
            <Animated.View
              style={[styles.animatedImageContainer, animatedImageStyle]}>
              <Pressable
                onPress={() => {
                  close(onPress);
                }}>
                <CustomMediaRendered
                  width={rw(imageWidth)}
                  height={rh(imageHeight)}
                  {...media}
                  customStyles={styles.animatedImage}
                />
                <Pressable
                  onPress={() => {
                    close(onClose);
                  }}
                  style={styles.iconPosition}>
                  <JioIcon
                    ic={'IcClose'}
                    color={IconColor.PRIMARY60}
                    {...icon}
                  />
                </Pressable>
              </Pressable>
            </Animated.View>
          )}
        </>
      </TouchableWithoutFeedback>
    </>
  );
};

export default JMInAppBanner;
