import Geolocation, {
  GeoPosition,
  PositionError,
} from 'react-native-geolocation-service';

export const getCurrentPosition = async () => {
  return await new Promise<GeoPosition | PositionError>((resolve, reject) => {
    Geolocation.getCurrentPosition(
      val => {
        resolve(val);
      },
      error => {
        reject(error);
      },
      {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 10000,
        showLocationDialog: true,
      },
    );
  });
};
