import {NavigationBean} from './JMNavGraphUtil';

export interface DashBoardData {
  dashBoardContent: JMTemplateData[];
}

export interface BottomNavigationData {
  bottomNavData: JMTemplateData[];
}

export interface JMTemplateData {
  id?: number;
  rule_id?: string;
  orderNo?: number;
  viewType?: number;
  title?: string;
  filterId?: string;
  showShimmer?: boolean;
  subTitle?: string;
  headerTitle?: string;
  type?: string;
  subHeaderTitle?: string;
  profileName?: string;
  profileImage?: string;
  bgImage?: string;
  bgImageSizeDimen?: ImageSizeDimen;
  showElement?: boolean;
  bgColor?: string;
  cta?: NavigationBean;
  items?: Item[];
  imageUrl?: string;
  bannerUrl?: string;
  actionContent?: ActionContent;
  actionContentItems?: ActionContent[];
  itemList?: Item[];
  pId?: number;
  iconUrl?: string;
  rnVersion?: string;
  iosVersion?: string;
  androidVersion?: string;
  uiContent?: UIContent[];
  margin?: Margin;
}

export interface ImageSizeDimen {
  width?: number;
  height?: number;
  viewBox_width?: number;
  viewBox_height?: number;
}

export interface Margin {
  top?: number;
  bottom?: number;
  left?: number;
  right?: number;
}

export interface Item {
  id?: number;
  title?: string;
  subTitle?: string;
  bannerUrl?: string;
  bannerDimen?: ImageSizeDimen;
  iconUrl?: string;
  filterId?: string;
  type?: string;
  description?: string;
  headerContent?: string[];
  footerContent?: string[];
  position?: number;
  actionContent?: ActionContent;
  isBig?: boolean;
  color?: string;
  bgColor?: string;
  bgImage?: string;
  cta?: NavigationBean;
  count?: number;
  rnVersion?: string;
  iosVersion?: string;
  androidVersion?: string;
}

export interface UIContent {
  bgImage?: string;
  iconUrl?: string;
  filterId?: string;
  bgColor?: string;
}

export interface ActionContent {
  id?: number;
  orderIndex?: number;
  title?: string;
  iconUrl: string;
  buttonType?: string;
  cta?: NavigationBean;
}
