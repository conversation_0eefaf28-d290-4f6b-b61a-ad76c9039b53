import EventEmitter from 'events';

const eventEmitter = new EventEmitter();

export const subscribeToRNEvent = (
  eventName: string,
  callback: (eventData: any) => void,
) => {
  eventEmitter.on(eventName, callback);
};

export const unsubscribeToRNEvent = (
  eventName: string,
  callback: (event?: any) => void,
) => {
  return eventEmitter.off(eventName, callback);
};

export const emitRNEvent = (eventName: string, eventData?: any) => {
  eventEmitter.emit(eventName, eventData);
};
