import GooglePlacesSDK, {
  type FieldsParam,
  type PredictionFiltersParam,
} from 'react-native-google-places-sdk';

export const initializeGooglePlacesSdk = (key: string) => {
  GooglePlacesSDK.initialize(key);
};

export const fetchPredictions = async (
  query: string,
  filters: PredictionFiltersParam = {},
) => {
  return await GooglePlacesSDK.fetchPredictions(query, filters);
};

export const fetchPlaceByID = async (
  placeID: string,
  fields: FieldsParam = [],
) => {
  return await GooglePlacesSDK.fetchPlaceByID(placeID, fields);
};
