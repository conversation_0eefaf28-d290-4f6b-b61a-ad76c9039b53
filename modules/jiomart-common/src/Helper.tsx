import { NullableString } from './JMObjectUtility';
import * as CryptoJ<PERSON> from 'crypto-js';

export function base64EncodeValue(contents: NullableString): NullableString {
  if (!contents) {
    return null;
  }
  const utf8Bytes = CryptoJS.enc.Utf8.parse(contents);
  const base64String = CryptoJS.enc.Base64.stringify(utf8Bytes);
  return base64String;
}

export function base64Decode(value?: string) {
  if (value) {
    const parsedWords = CryptoJS.enc.Base64.parse(value);
    return JSON.parse(CryptoJS.enc.Utf8.stringify(parsedWords));
  } else return '';
}

export function base64DecodeValue(contents: NullableString): NullableString {
  if (!contents) {
    return null;
  }
  const decodedBytes = CryptoJS.enc.Base64.parse(contents);
  return CryptoJS.enc.Utf8.stringify(decodedBytes);
}
// date to string conversion in this format DD/MM/YYYY
export function dateToStringFormat(date: Date): string {
  if (date) {
    const options: Intl.DateTimeFormatOptions = {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    };
    const formatedString = date.toLocaleDateString('en-GB', options);
    return formatedString;
  } else return '';
}

// string to date conversion in this format YYYY-MM-DD
export function stringToDateFormat(date: string): Date | undefined {
  if (date && date.length > 0) {
    const [day, month, year] = date.split('/');
    return new Date(`${year}-${month}-${day}`);
  }
  return;
}

export function dateDiffInDays(date1: Date, date2: Date): number {
  const difference = date2.getTime() - date1.getTime();
  const diffInMs = Math.abs(difference);
  const diffInDays = Math.ceil(diffInMs / (1000 * 60 * 60 * 24));
  return diffInDays;
}

// true if date1 is after than date2
export function dateComparsion(date1: Date, date2: Date): number {
  const difference = date1.getTime() - date2.getTime();
  return difference;
}

