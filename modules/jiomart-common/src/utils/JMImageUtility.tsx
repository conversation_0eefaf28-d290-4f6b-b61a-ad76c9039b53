import {Image} from 'react-native';
import {getScreenDim} from '../JMResponsive';
import {getBaseURL} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';

export const getImageData = (iconPath: string) => {
  if (iconPath.startsWith('http://') || iconPath.startsWith('https://')) {
    return iconPath;
  } else if (iconPath.startsWith('Ic')) {
    return iconPath;
  }
};

export const getImageHeight = (uri: string): Promise<number> =>
  new Promise((resolve, reject) => {
    Image.getSize(
      uri,
      (width, height) => {
        if (width && height) {
          const h = getScreenDim?.width / (width / height);
          resolve(h);
        } else {
          reject('Invalid image size');
        }
      },
      reject,
    );
  });

export const getAnimationHeight = async (filename: string): Promise<number> => {
  const url = `${getBaseURL()}/images/cms/aw_rbslider/slides/animations/${filename}`;
  const response = await fetch(url);
  const json = await response.json();
  if (json?.w && json?.h) {
    return getScreenDim?.width / (json.w / json.h);
  }
  throw new Error('Invalid animation dimensions');
};
