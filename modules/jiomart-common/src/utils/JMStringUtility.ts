export const capitalizeFirstLetter = (str: string) => {
  if (!str) {
    return '';
  }
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

export const isValidString = (value: any): boolean => {
  try {
    if (typeof value !== 'string') return false;

    const trimmed = value.trim().toLowerCase();
    return trimmed !== '' && trimmed !== 'null' && trimmed !== 'undefined';
  } catch (error) {
    return false
  }
};

export const getValidString = (value: any): string => {
  try {
    if (typeof value !== 'string') return '';

    const trimmed = value.trim().toLowerCase();
    return (trimmed !== '' && trimmed !== 'null' && trimmed !== 'undefined') ? trimmed : ''
  } catch (error) {
    return ''
  }
};

export const splitStringIntoLowerCase = (value: any, separator: string = ','): string[] => {
  try {
    if (typeof value !== 'string') return [];

    const trimmed = value.trim().toLowerCase();
    return trimmed.split(separator).map(item => item.trim().toLowerCase());
  } catch (error) {
    return []
  }
};