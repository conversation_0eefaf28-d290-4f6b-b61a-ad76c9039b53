export const checkValueIsExistInArray = (str: any, val: any[]) => {
  return val?.includes(str);
};

export const arraysEqual = (arr1: string[], arr2: string[]) => {
  if (arr1.length !== arr2.length || arr1.length === 0 || arr2.length === 0)
    return false;

  const sorted1 = [...arr1].sort();
  const sorted2 = [...arr2].sort();

  return sorted1.every((val, index) => val === sorted2[index]);
};

export const arrayIntoLowerCase = (value: string[]): string[] => {
  try {
    if (value && value?.length > 0) {
      return value?.map(item => item.trim().toLowerCase());
    }
    return [];
  } catch (error) {
    return [];
  }
};
