export const calculateExpiryTimestamp = (expiry: number): number => {
  // expiry - in mins
  return Math.floor(Date.now() / 1000) + expiry * 60;
};
export const formatDate = (date: Date, format: string): string => {
  try {
    if (!date) {
      return '';
    }
    // Extract date parts manually
    const dayNamesShort = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const dayNamesLong = [
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ];
    const monthNamesShort = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    const monthNamesLong = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];

    const dayIndex = date.getDay();
    const monthIndex = date.getMonth();
    const day = date.getDate();
    const year = date.getFullYear();

    // Replace format tokens with correct values
    let formattedDate = format
      .replace('[dddd]', dayNamesLong[dayIndex]) // Full weekday
      .replace('[ddd]', dayNamesShort[dayIndex]) // Short weekday
      .replace('[MMMM]', monthNamesLong[monthIndex]) // Full month
      .replace('[MMM]', monthNamesShort[monthIndex]) // Short month
      .replace('[MM]', String(monthIndex + 1).padStart(2, '0')) // Two-digit month
      .replace('[M]', String(monthIndex + 1)) // Numeric month
      .replace('[DD]', String(day).padStart(2, '0')) // Two-digit day
      .replace('[D]', String(day)) // Numeric day
      .replace('[YYYY]', String(year)) // Full year
      .replace('[YY]', String(year).slice(-2)); // Two-digit year

    return formattedDate;
  } catch (error) {
    return '';
  }
};
