import {
  isDebugMode,
  isNullOrUndefinedOrEmpty,
} from '../JMObjectUtility';

type logObject = any;
export class JMLogger {
  static log(data: logObject, source: logObject = null) {
    if (!isDebugMode()) {
      return;
    }
    if (!isNullOrUndefinedOrEmpty(source)) {
      console.log('\n\n', JSON.stringify(data, null, 2), source);
    } else {
      console.log('\n\n', JSON.stringify(data, null, 2));
    }
  }
}

export class JHExceptionLogger {
  static log(data: logObject, source: logObject = null) {
    if (!isDebugMode()) {
      return;
    }
    if (!isNullOrUndefinedOrEmpty(source)) {
      console.log('\n\n', JSON.stringify(data, null, 2), source);
    } else {
      console.log('\n\n', JSON.stringify(data, null, 2));
    }
  }
}
