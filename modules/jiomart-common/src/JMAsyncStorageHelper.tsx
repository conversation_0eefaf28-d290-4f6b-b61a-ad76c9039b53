import AsyncStorage from '@react-native-async-storage/async-storage';
import {<PERSON><PERSON>og<PERSON>} from './utils/JMLogger';
import {base64DecodeValue, base64EncodeValue} from './Helper';
import {MMKV} from 'react-native-mmkv';
const storage = new MMKV();

export const TRANSACTION_REF_NUMBER = 'transactionRefNum';
export const JH_TOKEN = 'JHToken';

export enum AppPrefernceKeys {
  PREFERENCE_KEY_IS_BIOMETRIC_ENABLED = 'PREFERENCE_KEY_IS_BIOMETRIC_ENABLED',
  ENTER_PIN_COMPLETED = 'PREFERENCE_KEY_IS_ENTER_PIN_DONE',
  HOME_DASHBOARD_DATA = 'PREFERENCE_KEY_HOME_DASHBAORD_DATA',
  DISCOVERY_DASHBOARD_DATA = 'PREFERENCE_KEY_DISCOVERY_DASHBAORD_DATA',
  DISCOVERY_DASHBOARD_HASHVALUE = 'PREFERENCE_KEY_DISCOVERY_DASHBAORD_HASHVALUE',
  HOME_DASHBOARD_HASHVALUE = 'PREFERENCE_KEY_HOME_DASHBAORD_HASHVALUE',
  HEALTH_LOCKER_CACHED_DATA = 'HEALTH_LOCKER_CACHED_DATA',
}

export const addStringPref = async (
  key: string,
  value: string,
  encryptData: boolean = false,
) => {
  try {
    const finalvalue =
      encryptData && value ? base64EncodeValue(value) ?? value : value;
    storage.set(key, finalvalue);
  } catch (e) {
    // saving error
    console.log('This is error -- '+key, e);
    JMLogger.log(e);
  }
};
export const removeStringPref = async (key: string) => {
  try {
    const value = storage.delete(key);
  } catch (e) {
    JMLogger.log(e);
  }
};

export const getPrefString = async (
  key: string,
  decryptData: boolean = false,
) => {
  try {
    const value = storage.getString(key);
    const finalvalue =
      decryptData && value ? base64DecodeValue(value) ?? value : value;
    return finalvalue;
  } catch (e) {
    JMLogger.log(e);
    return null; // Explicitly return null on error
  }
};

export const addNumberPref = async (key: string, value: number) => {
  try {
    storage.set(key, value.toString());
    // console.log("hashmap saved")
  } catch (e) {
    // saving error
    JMLogger.log(e);
  }
};
export const removePrefAllKeys = async () => {
  try {
    const value = storage.clearAll();
    JMLogger.log('removePrefAllKeys');
  } catch (e) {
    JMLogger.log(e);
  }
};
export const removeNumberPref = async (key: string) => {
  try {
    const value = storage.delete(key);
  } catch (e) {
    JMLogger.log(e);
  }
};

export const getPrefNumber = async (key: string) => {
  try {
    const value = storage.getString(key);
    return value ? parseInt(value) : -1;
  } catch (e) {
    // error reading value
    JMLogger.log(e);
  }
};

export const setRecordsToUpload = async (key: string, data: string) => {
  try {
    storage.set(key, data);
  } catch (e) {
    // error reading value
    JMLogger.log(e);
  }
};

export const getRecordsToUpload = async (key: string) => {
  try {
    const data = storage.getString(key);
    return data;
  } catch (e) {
    // error reading value
    JMLogger.log(e);
  }
};

export const setDirectoryData = async (key: string, data: string) => {
  try {
    storage.set(key, data);
  } catch (e) {
    // error reading value
    JMLogger.log(e);
  }
};

export const getDirectoryData = async (key: string) => {
  try {
    const data = storage.getString(key);
    return data;
  } catch (e) {
    // error reading value
    JMLogger.log(e);
  }
};
 