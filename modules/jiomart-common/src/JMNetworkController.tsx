import {useState, useEffect} from 'react';
import NetInfo, {
  NetInfoState,
  NetInfoSubscription,
} from '@react-native-community/netinfo';
import {JMSharedViewModel} from './JMSharedViewModel';

const useNetworkController = () => {
  const [isNetworkConnected, setIsNetworkConnected] = useState<boolean | null>(
    null,
  );
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      console.log('networkChange ' + state.isConnected);
      if (isNetworkConnected !== state.isConnected) {
        JMSharedViewModel.Instance.setNetworkAvailableStatus(
          state.isConnected || false,
        );
        setIsNetworkConnected(state.isConnected || false);
      }
    });
    return () => {
      unsubscribe();
    };
  }, []);

  return {isNetworkConnected};
};

export default useNetworkController;
