// Dimensions.ts

import ScreenPercentageConverter from './JMScreenPercentageConverter';
import {PixelRatio} from 'react-native';

const CustomDimensions = new ScreenPercentageConverter(360, 800);

// Create aliases for methods
const rw = CustomDimensions.responsiveWidth.bind(CustomDimensions);
const rh = CustomDimensions.responsiveHeight.bind(CustomDimensions);
const rf = CustomDimensions.responsiveFontSize.bind(CustomDimensions);
const top = CustomDimensions.getTopBar();
const bottom = CustomDimensions.getBottomBar();
const getScreenDim = CustomDimensions.getDimension();
const getDivisionDimension = CustomDimensions.getDivisionDimension();

export const dp = (px: number) => {
  return px / PixelRatio.get();
};

export const sp = (px: number) => {
  return px / (PixelRatio.getFontScale() * PixelRatio.get());
};


export default CustomDimensions;
export {rw, rh, rf, top, bottom, getScreenDim, getDivisionDimension};
