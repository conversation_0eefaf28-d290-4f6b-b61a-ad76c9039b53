export enum AnalyticsParams {
  CATAGORY = 'new_Category',
  ACTION = 'new_Action',
  LABEL = 'new_Label',
  GENERAL_DATA = 'General_Data',
  OPTIONAL_DATA = 'Optional_Data',
  INTERNET_CONNECTION = 'Internet_Connection',
  JOURNEY_SOURCE = 'Journey_Source',
  ENTRY_SOURCE = 'Entry_Source',
  APP_NAME = 'App_name'
}

export enum EventTriggerChannel {
  FIREBASE = 'firebase',
  SENSE = 'sense',
  FIREBASE_AND_SENSE = 'firebase_and_sense',
}
