import {StackAnimationTypes} from 'react-native-screens';
import {AppScreens} from './JMAppScreenEntry';
import {HeaderType} from './JMScreenSlot.types';
import {getLocalJMCommonFlags} from './utils/JMCommonFunctions';
import {JMSharedViewModel} from './JMSharedViewModel';

let navAddressGraph = new Map<string, string>([
  [AppScreens.ADDRESS_LIST_SCREEN, AppScreens.ADDRESS_SCREEN],
  [AppScreens.ADDRESS_SEARCH_SCREEN, AppScreens.ADDRESS_SCREEN],
  [AppScreens.ADDRESS_MAP_SCREEN, AppScreens.ADDRESS_SCREEN],
  [AppScreens.ADDRESS_FORM_SCREEN, AppScreens.ADDRESS_SCREEN],
  [AppScreens.ADDRESS_FORM_V1_SCREEN, AppScreens.ADDRESS_SCREEN],
]);

let navOrderGraph = new Map<string, string>([
  [AppScreens.ORDER_LIST_SCREEN, AppScreens.ORDER_SCREEN],
  [AppScreens.REFUND_DETAIL_SCREEN, AppScreens.ORDER_SCREEN],
]);

let navCategoryGraph = new Map<string, string>([
  [AppScreens.ALL_CATEGORIES, AppScreens.ALL_CATEGORIES],
]);

let navProductListingGrap = new Map<string, string>([
  [AppScreens.PRODUCT_LISTING_SCREEN, AppScreens.PRODUCT_LISTING_SCREEN_START],
  [
    AppScreens.PRODUCT_GRID_LISTING_SCREEN,
    AppScreens.PRODUCT_LISTING_SCREEN_START,
  ],
  [
    AppScreens.PRODUCT_SEARCH_LISTING_SCREEN,
    AppScreens.PRODUCT_LISTING_SCREEN_START,
  ],
]);

let navWishlistGraph = new Map<string, string>([
  [AppScreens.WISHLIST_SCREEN, AppScreens.WISHLIST_SCREEN],
]);

let navFeedbackGraph = new Map<string, string>([
  [AppScreens.RATING_AND_REVIEW_FORM_SCREEN, AppScreens.FEEDBACK_SCREEN],
  [AppScreens.RATING_AND_REVIEW_SUCCESS_SCREEN, AppScreens.FEEDBACK_SCREEN],
]);

let navCommonGraph = new Map<string, string>([
  [AppScreens.COMMON_WEB_VIEW, AppScreens.COMMON_WEB_VIEW],
  [AppScreens.SPLASH_SCREEN, AppScreens.SPLASH_SCREEN],
  [AppScreens.HOME_SCREEN, AppScreens.HOME_SCREEN],
  [AppScreens.ONE_RETAIL_SCREEN, AppScreens.ONE_RETAIL_SCREEN],
  [AppScreens.SEARCH_SCREEN, AppScreens.SEARCH_SCREEN],
  [AppScreens.SCANNER_SCREEN, AppScreens.SCANNER_SCREEN],
  [AppScreens.ACCOUNT_SCREEN, AppScreens.ACCOUNT_SCREEN],
  [AppScreens.NOTIFICATION_SCREEN, AppScreens.NOTIFICATION_SCREEN],
  [AppScreens.HOME_DASHBOARD_SCREEN, AppScreens.HOME_DASHBOARD_SCREEN],
]);

export let navGraph = new Map<string, string>([
  ...navCommonGraph,
  ...navCategoryGraph,
  ...navAddressGraph,
  ...navProductListingGrap,
  ...navWishlistGraph,
  ...navOrderGraph,
  ...navFeedbackGraph,
]);

export type NavigationStackData = {
  [K in (typeof AppScreens)[keyof typeof AppScreens]]: NavigationBean;
};

export interface OtpParam {
  number: string;
}

export interface NavigationBean {
  navTitle?: string;
  destination: string;
  loginRequired?: boolean;
  actionUrl?: string;
  actionType: string;
  headerVisibility?: HeaderType;
  userJourneyRequiredState?: number;
  bundle?: string;
  userAuthenticationRequired?: number;
  tokenType?: TokenType;
  params?: any;
  navigationType?: NavigationType;
  source?: string;
  screenName?: string;
  gaModel?: GAModel;
  headerType?: number;
  shouldShowDeliverToBar?: boolean;
  shouldShowBottomNavBar?: boolean;
  rnVersion?: string;
  androidVersion?: string;
  iosVersion?: string;
  deeplinkIdentifier?: string;
  deeplinkParam?: string;
  type?: string;
  animation?: StackAnimationTypes;
  abTestingparams?: ABTestingparams[];
}

export interface ABTestingparams {
  abTestingKey: string;
  urlKey: string;
  defaultValue?: string;
}

export interface CommonBean {
  id?: string;
  screenName: string;
  title?: string;
  icon?: string;
  cta?: NavigationBean;
}

export enum UserJourneyRequiredState {
  LOGIN_NOT_REQUIRED = 0,
  LOGIN_REQUIRED = 1,
}

export interface GAModel {
  category: string;
  label: string;
  action: string;
  commonCustomDimesion: string;
  appName: string;
  entrySource: string;
}

export enum AuthenticationType {
  USER_PROFILE_CREATED = 0,
  SPECIAL_TOKEN = 1,
}

export enum TokenType {
  GENERAL_TOKEN = 0,
  SPECIAL_TOKEN = 1,
}

export enum ActionType {
  OPEN_NATIVE = 'T001',
  OPEN_EXTERNAL = 'T002',
  OPEN_WEB_URL = 'T003',
  OPEN_WEB_URL_WITH_TOKEN = 'T004',
  OPEN_WEB_HTML = 'T005',
  OPEN_DEEPLINK = 'T006',
}

export enum NavigationType {
  NAVIGATE = 'navigate',
  REPLACE = 'replace',
  RESET = 'reset',
  PUSH = 'push',
}

export function navBeanObj(bean: NavigationBean): NavigationBean {
  return {
    actionUrl: '',
    userAuthenticationRequired: 2,
    navTitle: '',
    headerVisibility: HeaderType.VISIBLE,
    loginRequired: true,
    params: null,
    navigationType: NavigationType.NAVIGATE,
    bundle: '',
    source: '',
    headerType: 2,
    userJourneyRequiredState: 0,
    shouldShowDeliverToBar: false,
    shouldShowBottomNavBar: false,
    ...bean,
  };
}

export function navBeanCopy(navBean: NavigationBean): NavigationBean {
  return {
    navTitle: navBean.navTitle,
    destination: navBean.destination,
    loginRequired: navBean.loginRequired,
    actionUrl: navBean.actionUrl,
    actionType: navBean.actionType,
    headerVisibility: navBean.headerVisibility,
    bundle: navBean.bundle,
    navigationType: navBean.navigationType,
    params: navBean.params,
    source: navBean.source,
  };
}

export const getHomeAppScreenDestination = (
  navigateToHome: boolean = false,
) => {
  if (JMSharedViewModel.Instance.homeScreenDestination.length > 0) {
    return JMSharedViewModel.Instance.homeScreenDestination;
  }
  const isHomeRN = getLocalJMCommonFlags('homeRN') ?? true;
  if (navigateToHome) {
    JMSharedViewModel.Instance.homeScreenDestination =
      isHomeRN === true
        ? AppScreens.HOME_DASHBOARD_SCREEN
        : AppScreens.HOME_SCREEN;
  }
  return isHomeRN === true
    ? AppScreens.HOME_DASHBOARD_SCREEN
    : AppScreens.HOME_SCREEN;
};
