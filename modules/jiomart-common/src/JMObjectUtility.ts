export const JMObjectTypes = {
  STRING: 'string',
  OBJECT: 'object',
  NUMBER: 'number',
};

export const PlatformType = {
  ANDROID: 'android',
  IOS: 'ios',
};

export type NullableString = string | null | undefined;
export type NullableNumber = number | null | undefined;
export type NullableObject = object | null | undefined;

// object | null | undefined
export const isNullOrUndefinedOrEmpty = (value: any): boolean => {
  return (
    value === null ||
    value === undefined ||
    (typeof value === JMObjectTypes.STRING && value.trim() === '') ||
    (Array.isArray(value) && value.length === 0) ||
    (typeof value === JMObjectTypes.OBJECT &&
      !Array.isArray(value) &&
      Object.keys(value).length === 0)
  );
};

// number | null | undefined
export const isNullOrUndefinedOrInvalidNumber = (obj: any): boolean => {
  return (
    obj === null ||
    obj === undefined ||
    (typeof obj === JMObjectTypes.NUMBER && isNaN(obj))
  );
};

export const isDebugMode = (): boolean => {
  return __DEV__;
};
