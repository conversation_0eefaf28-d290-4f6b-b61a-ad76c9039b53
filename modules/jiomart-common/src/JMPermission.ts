import RNPermissions, {
  Permission,
  RESULTS,
  type PermissionStatus,
} from 'react-native-permissions';
import type {AndroidPermission, IOSPermission} from './JMConstants';
import {Platform} from 'react-native';
import PushNotificationIOS from '@react-native-community/push-notification-ios';

export type JMPermissionType =
  | keyof typeof AndroidPermission
  | keyof typeof IOSPermission
  | any;

export const checkSinglePermission = async (permission: JMPermissionType) => {
  return await RNPermissions.check(permission as Permission);
};
export const checkMultiPermission = async (permissions: JMPermissionType[]) => {
  if (Platform.OS === 'android') {
    return await RNPermissions.checkMultiple(permissions);
  } else {
    const validPermissions = permissions.filter(
      (perm): perm is JMPermissionType => {
        return (
          (
            Object.keys(AndroidPermission) as Array<
              keyof typeof AndroidPermission
            >
          ).includes(perm as keyof typeof AndroidPermission) ||
          (
            Object.keys(IOSPermission) as Array<keyof typeof IOSPermission>
          ).includes(perm as keyof typeof IOSPermission)
        );
      },
    );
    return await RNPermissions.checkMultiple(validPermissions);
  }
};

export const requestSinglePermission = async (permission: JMPermissionType) => {
  return await RNPermissions.request(permission as Permission);
};

export const requestMultiPermissions = async (
  permission: JMPermissionType[],
) => {
  return await RNPermissions.requestMultiple(permission);
};

export const checkAndRequestSinglePermission = async (
  permission: JMPermissionType,
) => {
  const status = await checkSinglePermission(permission);

  if (status === RESULTS.DENIED) {
    return await requestSinglePermission(permission);
  }
  return status;
};

export const checkAndRequestMultiPermission = async (
  permissions: JMPermissionType[],
) => {
  const results: Record<JMPermissionType, PermissionStatus> = {} as Record<
    JMPermissionType,
    PermissionStatus
  >;

  for (const permission of permissions) {
    switch (permission) {
      case 'IOS_PUSH_NOTIFICATION':
        if (Platform.OS === 'ios') {
          await PushNotificationIOS.requestPermissions({
            alert: true,
            badge: true,
            sound: true,
          });
        }
        break;
      default:
        const status = await checkSinglePermission(permission);
        if (status === RESULTS.DENIED) {
          results[permission] = await requestSinglePermission(permission);
        } else {
          results[permission] = status;
        }
        break;
    }
  }

  return results;
};

export const openAppSetting = () => {
  return RNPermissions.openSettings();
};
