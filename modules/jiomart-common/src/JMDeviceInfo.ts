
 
import { Platform } from 'react-native';
import {getDeviceInfo} from '../../jiomart-general/src/bridge/JMRNBridge';
import { JMLogger } from './utils/JMLogger';
 
export type DeviceInfoData = {
  device_model: string;
  device_name: string;
  device_os: string;
  os_version: string;
  device_type: number;
  unique_id: string;
  app_version: string;
  location_enabled: boolean;

};
 
let cachedDeviceInfo: DeviceInfoData | null = null;
 
export default class JMDeviceInfoData {
  static async getAllDeviceInfo(): Promise<DeviceInfoData> {
    if (!cachedDeviceInfo) {
      cachedDeviceInfo = await getDeviceInfo();
    }
    JMLogger.log("getAllDeviceInfo", "getAllDeviceInfo "+cachedDeviceInfo ? JSON.stringify(cachedDeviceInfo) : "NA")
    return cachedDeviceInfo!;
  }
 
  static getDeviceName(): string {
    const info = cachedDeviceInfo;
    return info?.device_model ?? "";
  }
 
  static getDeviceModel(): string {
    const info = cachedDeviceInfo;
    return info?.device_name ?? "";
  }
 
  static getSystemName(): string {
    return Platform.OS === 'ios' ? 'iOS' : 'Android';
  }
 
  static getSystemVersion(): string {
    const info = cachedDeviceInfo;
    return info?.os_version ?? "";
  }
 
  static getDeviceType(): number {
    const info = cachedDeviceInfo;
    return info?.device_type ?? 2;
  }
 
  static getUniqueId(): string {
    const info = cachedDeviceInfo;
    return info?.unique_id ?? "";
  }
 
  static getVersion(): string {
    const info = cachedDeviceInfo;
    return info?.app_version ?? "";
  }

  static async getLocationEnabled(): Promise<boolean> {
    const deviceInfo = await getDeviceInfo();
    return await deviceInfo?.location_enabled ?? false
  }
}
 
 