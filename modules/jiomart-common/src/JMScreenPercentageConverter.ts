import {Dimensions, StatusBar} from 'react-native';

interface ScreenDimension {
  width: number;
  height: number;
}
interface SafeAreaInsets {
  top: number;
  bottom: number;
}

export default class ScreenPercentageConverter {
  private screenDimension: ScreenDimension;
  private figmaDimension: ScreenDimension;
  private safeAreaInset: SafeAreaInsets;
  private fontSize: number;

  constructor(screenWidth?: number, screenHeight?: number) {
    const {width, height} = Dimensions.get('window');
    const statusBarHeight = StatusBar.currentHeight || 0;
    const bottomBarHeight = Dimensions.get('screen').height - height;
    this.screenDimension = {
      width,
      height,
    };
    this.fontSize = Dimensions.get('window').fontScale;
    this.safeAreaInset = {
      top: statusBarHeight,
      bottom: bottomBarHeight,
    };
    if (screenWidth && screenHeight) {
      this.figmaDimension = {
        width: screenWidth,
        height: screenHeight,
      };
    } else {
      this.figmaDimension = {
        width,
        height,
      };
    }
  }

  public responsiveWidth(pixel: number) {
    return pixel * (this.screenDimension.width / this.figmaDimension.width);
  }

  public responsiveHeight(pixel: number) {
    return pixel * (this.screenDimension.height / this.figmaDimension.height);
  }
  public responsiveFontSize(pixel: number) {
    return pixel * this.fontSize;
  }

  public getTopBar() {
    return this.safeAreaInset.top;
  }
  public getBottomBar() {
    return this.safeAreaInset.bottom;
  }
  public getDimension() {
    return this.screenDimension;
  }

  public getDivisionDimension() {
    const division = {
      width: this.screenDimension.width / this.figmaDimension.width,
      height: this.screenDimension.height / this.figmaDimension.height,
    };
    return division;
  }
}
