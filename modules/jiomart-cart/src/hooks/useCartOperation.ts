import JMCartNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMCartNetworkController';
import {useMutation} from '@tanstack/react-query';
import {useDispatch} from 'react-redux';
import {setCart} from '../slices/cartSlice';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import {useGlobalState} from '../../../jiomart-general/src/context/JMGlobalStateProvider';
import {
  GenericToast,
  genericToastTypeData,
  mergeGenericToastTypeData,
} from '../../../jiomart-common/src/JMScreenSlot.types';
import {handleDeeplinkIntent} from '../../../jiomart-general/src/deeplink/JMDeeplinkUtility';
import {getBaseURL} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';

const cartController = new JMCartNetworkController();

const useCartOperation = () => {
  const dispatch = useDispatch();
  const {setToastTypeData} = useGlobalState();
  const getCart = useMutation({
    mutationFn: cartController.fetchCart,
    onSuccess: (response: any) => {
      dispatch(setCart(response));
    },
    retry: 0,
  });

  const addToCart = useMutation({
    mutationFn: cartController.addToCart,
    onSuccess: async (response: any) => {
      if (response?.success || response?.status === 'success') {
        getCart.mutate();

        let modifiedToastData: any;
        const deeplinkData = {
          mUri: `${getBaseURL()}/checkout/cart`,
        };
        const cta = await handleDeeplinkIntent(deeplinkData);
        modifiedToastData = {
          showButton: true,
          buttonText: 'VIEW',
          cta,
        };
        modifiedToastData = {
          message: 'Add To Cart',
        };

        setToastTypeData(
          mergeGenericToastTypeData(GenericToast.SUCCESS, modifiedToastData),
        );
      }
    },
    onError: (error: any) => {
      setToastTypeData(
        genericToastTypeData(
          GenericToast.ERROR,
          error?.response?.reason?.reason_eng ??
            'Something went wrong while adding to cart',
        ),
      );
    },
    retry: 0,
  });

  const removeFromCart = useMutation({
    mutationFn: cartController.removeFromCart,
    onSuccess: (response: any) => {
      if (response?.success || response?.status === 'success') {
        getCart.mutate();
      }
    },
    onError: (error: any) => {
      setToastTypeData(
        genericToastTypeData(
          GenericToast.ERROR,
          error?.response?.reason?.reason_eng ??
            'Something went wrong while removing the item',
        ),
      );
    },
    retry: 0,
  });
  const generateAddToCartRequest = (request: any) => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return {
          slug: request?.slug,
          item_id: request?.uid,
          item_size: request?.size,
          quantity: request?.quantity,
          product_group_tag: request?.product_group_tag,
          meta: request?.meta,
          identifiers: request?.identifiers,
          parent_item_identifiers: request?.parent_item_identifiers,
        };

      case AppSourceType.JM_BAU:
        return {
          product_code: request?.uid,
          qty: request?.quantity,
          seller_id: request?.sellerId,
          store_code: request?.store_id,
        };
      default:
        throw new Error('app source not found');
    }
  };

  const generateRemoveFromCartRequest = (request: any) => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return {
          item_id: request?.uid,
          item_size: request?.size,
          quantity: request?.quantity,
          meta: request?.meta,
          identifiers: request?.identifiers,
          parent_item_identifiers: request?.parent_item_identifiers,
          slug: request?.slug,
          product_group_tag: request?.product_group_tag,
        };
      case AppSourceType.JM_BAU:
        return {
          product_code: request?.uid,
          qty: request?.quantity,
          seller_id: request?.sellerId,
          store_code: request?.store_id,
        };
      default:
        throw new Error('app source not found');
    }
  };

  return {
    getCart,
    addToCart,
    removeFromCart,
    generateAddToCartRequest,
    generateRemoveFromCartRequest,
  };
};

export default useCartOperation;
