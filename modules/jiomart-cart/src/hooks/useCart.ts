import {useSelector} from 'react-redux';
import {cart, cartCount, cartId, cartItems} from '../slices/cartSlice';

export const useCartCount = () => {
  return useSelector(cartCount);
};
export const useCartItems = ({uid}: any) => {
  const cartData = useSelector(cartItems);
  return cartData?.filter((item: any) => item?.uid === uid)?.[0];
};
export const useCartId = () => {
  return useSelector(cartId);
};
export const useCart = () => {
  return useSelector(cart);
};
