import type {JMCartModel} from '../../../jiomart-networkmanager/src/models/Cart/JMCartModel';
import {createSlice, type PayloadAction} from '@reduxjs/toolkit';

const initialState: JMCartModel = {
  cart_id: '',
  success: '',
  message: '',
  items: [],
  cartCount: 0,
};

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    setCart: (_, action: PayloadAction<JMCartModel>) => {
      return action.payload;
    },
    resetCart: () => {
      return initialState;
    },
  },
});

export const cartCount = (state: any) => state.cart.cartCount;
export const cartItems = (state: any) => state.cart.items;
export const cartId = (state: any) => state.cart.cart_id;
export const cart = (state: any) => state.cart;
export const {setCart, resetCart} = cartSlice.actions;
export default cartSlice.reducer;
