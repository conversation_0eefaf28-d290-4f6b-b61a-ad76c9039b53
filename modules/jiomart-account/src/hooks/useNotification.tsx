import {useEffect} from 'react';
import CleverTap from 'clevertap-react-native';

const useNotification = (callback?: (value: number) => void) => {
  useEffect(() => {
    CleverTap?.initializeInbox();
    CleverTap?.addListener(CleverTap.CleverTapInboxDidInitialize, (_: any) => {
      CleverTap.getInboxMessageUnreadCount((err, unReadMessageCount: any) => {
        callback?.(unReadMessageCount);
      });
    });
    CleverTap?.addListener(
      CleverTap.CleverTapInboxMessagesDidUpdate,
      (_: any) => {
        CleverTap.getInboxMessageUnreadCount((err, unReadMessageCount: any) => {
          callback?.(unReadMessageCount);
        });
      },
    );

    return () => {
      CleverTap?.removeListener(CleverTap.CleverTapInboxDidInitialize);
      CleverTap?.removeListener(CleverTap?.CleverTapInboxMessagesDidUpdate);
    };
  }, []);
};

export default useNotification;
