import React, { useEffect, useState } from 'react';
import CleverTap from 'clevertap-react-native';
import { useGlobalState } from '../../../jiomart-general/src/context/JMGlobalStateProvider';
import { Platform } from 'react-native';

const extractDataFromMessages = (messages: any) => {
    return messages?.map((message: any) => {
        return {
            messageId: message?.id,
            isRead: message?.isRead,
            backgroundColor: message?.msg?.bg,
            title: {
                text: message?.msg?.content?.[0]?.title?.text,
                color: message?.msg?.content?.[0]?.title?.color
            },
            description: {
                text: message?.msg?.content?.[0]?.message?.text,
                color: message?.msg?.content?.[0]?.message?.color
            },
            action: {
                android: {
                    hasUrl: message?.msg?.content?.[0]?.action?.url?.android?.text!= "",
                    deeplinkUrl: message?.msg?.content?.[0]?.action?.url?.android?.text
                },
                ios: {
                    hasUrl: message?.msg?.content?.[0]?.action?.url?.ios?.text!= "",
                    deeplinkUrl: message?.msg?.content?.[0]?.action?.url?.ios?.text
                }
            }
        }
    })
}

const useNotificationScreenController = (props: any) => {
    const {navigation} = props;
    const [notificationData, setNotificationData] = useState<any>();
    const {setDeeplinkData} = useGlobalState();
    useEffect(() => {
        CleverTap?.initializeInbox();
        CleverTap?.addListener(CleverTap.CleverTapInboxDidInitialize, (event) => {
            CleverTap?.getAllInboxMessages((error , messages) => {
                if(messages && messages?.length > 0){
                    const notifications = extractDataFromMessages(messages)
                    setNotificationData(notifications);
                }
            })
        })
        CleverTap?.addListener(CleverTap.CleverTapInboxMessagesDidUpdate, (event) => {
            CleverTap?.getAllInboxMessages((error , messages) => {
                if(messages && messages?.length > 0){
                    const notifications = extractDataFromMessages(messages)
                    setNotificationData(notifications);
                }
            })
        })
        return () => {
            CleverTap?.removeListener(CleverTap.CleverTapInboxDidInitialize);
            CleverTap?.removeListener(CleverTap?.CleverTapInboxMessagesDidUpdate)
        }
    }, [])


    const onPressOfNotification = (msgId: any, action: any) => {
        const platformSpecificAction = action?.[Platform.OS];
        if(platformSpecificAction?.hasUrl){
            const deeplinkData = {
                mUri: platformSpecificAction.deeplinkUrl, 
                payload: ""
            }
            setDeeplinkData(deeplinkData)
        }
        CleverTap.markReadInboxMessageForId(msgId)
    }


    return {
        ...props,
        navigationBean: props.route.params,
        notificationData,
        onPressOfNotification
    }
}

export default useNotificationScreenController;