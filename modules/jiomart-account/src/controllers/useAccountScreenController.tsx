import {useConfigFile} from '../../../jiomart-general/src/hooks/useJMConfig';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {useCallback, useEffect, useState} from 'react';
import {BackHandler, NativeModules} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {JMAccountScreenProps} from '../types/JMAccountScreenType';
import JMUserApiNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMUserAPINetworkController';
import {
  addStringPref,
  getPrefString,
} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '../../../jiomart-common/src/JMConstants';
import {getOneRetailActionUrl, navigateTo} from '../../../jiomart-general/src/navigation/JMNavGraph';
import {
  ActionType,
  getHomeAppScreenDestination,
  navBeanObj,
  NavigationType,
} from '../../../jiomart-common/src/JMNavGraphUtil';
import {getBaseURL, getOneRetailsConfig} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import {useGlobalState} from '../../../jiomart-general/src/context/JMGlobalStateProvider';
import JMWebGlobalInfo from '../../../jiomart-webmanager/src/WebGlobalInfo';
import {useDispatch} from 'react-redux';
import {resetCart} from '../../../jiomart-cart/src/slices/cartSlice';
import {AppScreens} from '../../../jiomart-common/src/JMAppScreenEntry';
import {HeaderType} from '../../../jiomart-common/src/JMScreenSlot.types';
import {resetWishlist} from '../../../jiomart-wishlist/src/slices/wishlistSlice';
import { JMStorageService } from '../../../jiomart-networkmanager/src/api/utils/JMStorageService';
import { JMDatabaseManager } from '../../../jiomart-networkmanager/src/db/JMDatabaseManager';
import { useFocusEffect } from '@react-navigation/native';
import { useQueryClient } from '@tanstack/react-query';

const {JMRNNavigatorModule} = NativeModules;
const userApiNetworkController = new JMUserApiNetworkController();

const useAccountScreenController = (props: JMAccountScreenProps) => {
  const {setUserInitials, notificationCount} = useGlobalState();
  const dispatch = useDispatch();
  const {navigation} = props;

  //   const [allCategoryDataResponse, setAllCategoryDataResponse] = useState<
  //     CustomCategoryItems[] | null | undefined
  //   >(null);

  const myProfileConfig = useConfigFile(
    JMConfigFileName.JMProfileConfigurationFileName,
  );

  const [userDataResponse, setUserData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [signOutBtmSheet, setSignOutBtmSheet] = useState(false)

  useEffect(() => {
    const backAction = () => {
      if (navigation.canGoBack()) {
        navigation.goBack();
        return true;
      }
      return false;
    };
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );

    return () => {
      backHandler.remove();
    };
  }, []);


  useFocusEffect(
    useCallback(() => {
      fetchUserDetials();
    }, []),
  );


  const fetchUserDetials = async () => {
    const storedData = await getPrefString(AsyncStorageKeys.PROFILE_DETAILS);

    if (storedData) {
      setUserData(JSON.parse(storedData));
      setLoading(false);
    }

    const userDetails = await userApiNetworkController.fetchUserDetails();
    if (userDetails) {
      setUserData(userDetails);
      setLoading(false);
    }
  };
  const {setAddress} = useGlobalState();
  const queryClient = useQueryClient();
  const clearOnLogout = async() => {
    await JMStorageService.clearAllKeys()
    setUserInitials('');
    dispatch(resetCart());
    dispatch(resetWishlist());
    queryClient.clear()
    userApiNetworkController.fetchGuestUserSession();
  }

  const handleAddress = async () => {
    let address: any = await JMDatabaseManager.address.getDefaultAddress();
    address = JSON.parse(address ?? '');
    setAddress({
      pin: address?.pin,
      city: address?.city,
      state: address?.state
    });
  };

  const signOutUser = async () => {
    try {
      if (JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU) {
        await userApiNetworkController.logoutUser();
      }
      handleAddress();
      await clearOnLogout()
      if (navigation && navigation.canGoBack!!) {
        navigateTo(
          navBeanObj({
            actionType: ActionType.OPEN_WEB_URL,
            destination: getHomeAppScreenDestination(),
            headerVisibility: HeaderType.CUSTOM,
            navigationType: NavigationType.RESET,
            loginRequired: false,
            actionUrl: getBaseURL(),
            shouldShowBottomNavBar: false,
            shouldShowDeliverToBar: true,
            headerType: 5,
          }),
          navigation,
        );
      }
    } catch (error) {}
  };
  var statusBarColor = '#0078ac';
  const insets = useSafeAreaInsets();
  const statusBarHeight = insets.top;

  const oneRetailConfig = getOneRetailsConfig();

  function redirectTo(item: any) {
    console.log('item', item);
    if (item?.deeplink?.DeeplinkIdentifier === 'sign_out') {
      openSignOutBtmSheet();
      return;
    }

    if(item?.type === "AccountCard"){
      navigateTo(
        navBeanObj({
          ...item.cta,
          actionUrl: getOneRetailActionUrl("PROFILE")
        }),
        navigation,
      );
      return;
    }

    navigateTo(
      navBeanObj({
        ...item?.cta,
        actionUrl: `${item?.cta?.actionUrl}`,
      }),
      navigation,
    );

    // const currentScreen =
    //   navigation.getState()?.routes?.[navigation.getState()?.index]?.name;
    // if (currentScreen === item?.rnActionScrren) {
    //   return;
    // }

    // if (item?.openInRN) {
    //   navigation.push(item?.rnActionScrren, {
    //     webViewUrl: item?.webUrl,
    //     ...item?.header,
    //     ...item?.params,
    //   });
    //   return;
    // }
  }

  const openSignOutBtmSheet = () => {
    setSignOutBtmSheet(true);
  };
  const closeSignOutBtmSheet = () => {
    setSignOutBtmSheet(false);
  };

  return {
    statusBarHeight,
    statusBarColor,
    shouldShowDeliverToBar,
    userDataResponse,
    JMRNNavigatorModule,
    myProfileConfig,
    loading,
    insets,
    ...props,
    navigationBean: props.route.params,
    redirectTo,
    notificationCount,
    openSignOutBtmSheet,
    signOutBtmSheet,
    closeSignOutBtmSheet,
    signOutUser
  };
};

// const fetchAllCategoryApi = async setAllCategoryDataResponse => {
//   try {
//     const data = await getAllCategoryApi();
//     if (data && data.length > 0) {
//       setAllCategoryDataResponse(data);
//     } else {
//       setAllCategoryDataResponse(undefined);
//     }
//   } catch (error) {
//     // FirebaseCrashlytics.sendCrashlogEvent(error.message.toString());
//     setAllCategoryDataResponse(undefined);
//   }
// };

const shouldShowDeliverToBar = false;

export default useAccountScreenController;
