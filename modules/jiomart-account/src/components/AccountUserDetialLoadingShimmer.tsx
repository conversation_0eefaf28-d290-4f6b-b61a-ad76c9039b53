import Skeleton from "@jio/rn_components/src/utils/Shimmer";
import { Dimensions, StyleSheet, View } from "react-native";
import { useSharedValue } from "react-native-reanimated";
 
 
const AccountUserDetialLoadingShimmer = () => {
  const translateX = useSharedValue(-220);
  return (
     <View style={styles.container}>
       <View style={styles.profileContainer}>
       <Skeleton height={50} width={50} style={{borderRadius:4}} />
       <View style={[styles.profileImage, {marginTop:1}]}>
       <View style={styles.textContainer}>
        <Skeleton height={25} width={Dimensions.get('window').width - 102} style={styles.name} />
        <Skeleton height={25} width={Dimensions.get('window').width - 102} style={styles.name} />
        <Skeleton height={25} width={Dimensions.get('window').width - 102} style={styles.name} />
      </View>
      </View>
      </View>
      <View style={styles.borderBottom}></View>
    </View>
  )
}
 
export default AccountUserDetialLoadingShimmer;
 
const windowWidth = Dimensions.get('window').width;
const windowHeight = Dimensions.get('window').height;
const figmaHeight = 800;
const figmaWidth = 360;
const Height = windowHeight / figmaHeight;
const Width = windowWidth / figmaWidth;
 
export { Height, Width };
 
const styles = StyleSheet.create({
  card: {flexDirection:'row',
  height:57*Height,
  alignItems:'center'},
  profileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    height: 118,
    // backgroundColor: 'red',
  },
  container: {
      flexDirection: 'column',
      padding: 10,
      height: 118,
    },
  profileImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 16,
  },
  textContainer: {
    flex: 1,
    marginRight: 16,
    // backgroundColor: 'green',
    marginTop: -20,
  },
  name: {
    marginTop: 0,
    marginLeft: 16,
    marginRight: 16,
    marginBottom: 8,
  },
  email: {
    marginLeft: 16,
    marginRight: 16,
    marginBottom: 5,
  },
  phoneNumber: {
      fontSize: 14,
      marginRight: 16,
      fontWeight: '500',
      color: '#000000A6',
      fontFamily: "JioType-Medium", 
      marginBottom: 5,
  },
  arrowIcon: {
    width: 20,
    height: 20,
    tintColor: 'gray',
  },
  borderBottom: {
      borderBottomWidth: 1, 
      borderBottomColor: '#E0E0E0',
      width:  Dimensions.get('window').width - 32, 
      marginLeft: 16,
      marginRight: 16,
      alignSelf: 'center',
    },

},
  
  
)