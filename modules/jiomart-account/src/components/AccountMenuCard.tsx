import React from 'react';
import {View, StyleSheet, Pressable, Dimensions, FlatList} from 'react-native';
import {JioIcon, JioText} from '@jio/rn_components';
import {IconSize, JioTypography} from '@jio/rn_components/src/index.types';

interface AccountMenuCardProps {
  item: {
    id: string;
  };
}

const AccountMenuCard: React.FC<AccountMenuCardProps> = ({item, onPress}) => {
  const renderItem = ({item}) =>
    item.isVisible ? (
      <Pressable
        style={styles.item}
        onPress={() => {
          onPress?.(item);
        }}>
        <JioIcon ic={item.iconName} size={IconSize.MEDIUM} />
        <JioText
          style={styles.label}
          appearance={JioTypography.BODY_XS}
          color="primary_grey_80"
          text={item.title}
        />
      </Pressable>
    ) : null;

  return item.isVisible ? (
    <View style={styles.container}>
      <FlatList
        data={item.quickOptions}
        renderItem={renderItem}
        keyExtractor={item => item?.title}
        horizontal
        contentContainerStyle={styles.menuContainer}
      />
    </View>
  ) : null;
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    paddingTop: 24,
    height: Dimensions.get('window').width * 0.17 + 36, //100,
    width: '100%',
    marginBottom: 5,
  },
  menuContainer: {
    justifyContent: 'space-evenly',
    flexGrow: 1,
  },
  item: {
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    height: Dimensions.get('window').width * 0.28 * 0.63, //64,
    width: Dimensions.get('window').width * 0.2, // 101,
    borderRadius: 12,
    paddingTop: 10,
    paddingBottom: 10,
  },
  image: {
    width: 50,
    height: 50,
    marginBottom: 5,
  },
  label: {
    paddingTop: 4,
    textAlign: 'center',
    alignSelf: 'center',
  },
});

export default AccountMenuCard;
