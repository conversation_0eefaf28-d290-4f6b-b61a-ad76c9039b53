import {StyleSheet, View} from 'react-native';
import React from 'react';
import JMBtmSheetHeader from '../../../jiomart-general/src/ui/JMBtmSheetHeader';
import {JioButton, JioText} from '@jio/rn_components';
import {
  ButtonKind,
  ButtonSize,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import type {BottomSheetChildren} from '../../../jiomart-general/src/ui/BottomSheet/types/BottomSheetType';

interface JMSignOutBtmSheetProps extends BottomSheetChildren {
  onSignOut: () => void;
  onCancel: () => void;
}

const JMSignOutBtmSheet = (props: JMSignOutBtmSheetProps) => {
  const {onSignOut, onCancel, close} = props;
  const insets = useSafeAreaInsets();
  return (
    <View
      style={[{rowGap: 16, marginBottom: 24}, {paddingBottom: insets.bottom}]}>
      <JMBtmSheetHeader
        title={'Wish to Sign Out?'}
        onPress={() => {
          close?.(onCancel);
        }}
      />
      <JioText
        text="You might miss out deals and offers made just for you. Do you still want to exit?"
        appearance={JioTypography.BODY_XS}
        color={'primary_grey_80'}
        style={{marginHorizontal: 24}}
      />
      <JioButton
        title="Sign Out"
        stretch
        size={ButtonSize.LARGE}
        style={{
          marginHorizontal: 24,
        }}
        onClick={() => {
          close?.(onSignOut);
        }}
      />
      <JioButton
        title="Cancel"
        stretch
        kind={ButtonKind.SECONDARY}
        size={ButtonSize.LARGE}
        style={{marginHorizontal: 24}}
        onClick={() => {
          close?.(onCancel);
        }}
      />
    </View>
  );
};

export default JMSignOutBtmSheet;

const styles = StyleSheet.create({});
