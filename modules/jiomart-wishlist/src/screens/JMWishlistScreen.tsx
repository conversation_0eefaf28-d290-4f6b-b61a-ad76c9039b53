import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import ScreenSlot, {
  DeeplinkHandler,
} from '../../../jiomart-general/src/ui/JMScreenSlot';
import type {JMWishlistScreenProps} from '../types/JMWishlistScreenType';

const JMWishlistScreen = (props: JMWishlistScreenProps) => {
  const {navigationBean, navigation} = props;
  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={bean => {
        return (
          <ScreenSlot
            navigationBean={bean}
            navigation={navigation}
            children={_ => {
              return (
                <View>
                  <Text>JMWishlistScreen</Text>
                </View>
              );
            }}
          />
        );
      }}
    />
  );
};

export default JMWishlistScreen;

const styles = StyleSheet.create({});
