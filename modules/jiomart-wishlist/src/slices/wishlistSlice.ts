import {createSlice, type PayloadAction} from '@reduxjs/toolkit';

const initialState = {
  items: [],
};

const wishlistSlice = createSlice({
  name: 'wishlist',
  initialState,
  reducers: {
    setWishlist: (_, action: PayloadAction<any>) => {
      return action.payload;
    },
    resetWishlist: () => {
      return initialState;
    },
  },
});

export const wishlistItems = (state: any) => state.wishlist.ids;
export const wishlist = (state: any) => state.wishlist;
export const {setWishlist, resetWishlist} = wishlistSlice.actions;
export default wishlistSlice.reducer;
