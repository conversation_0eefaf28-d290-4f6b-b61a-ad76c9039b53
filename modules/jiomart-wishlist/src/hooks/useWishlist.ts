import {useSelector} from 'react-redux';
import {wishlist, wishlistItems} from '../slices/wishlistSlice';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';

export const useWishlistItems = ({uid}: any) => {
  const wishlistIds = useSelector(wishlistItems);
  return wishlistIds?.includes(
    JMSharedViewModel.Instance.appSource === AppSourceType.JM_BAU
      ? `${uid}`
      : uid,
  );
};
export const useWishlist = () => {
  return useSelector(wishlist);
};
