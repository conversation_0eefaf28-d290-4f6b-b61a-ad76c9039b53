import {useMutation} from '@tanstack/react-query';
import {useDispatch} from 'react-redux';
import {setWishlist} from '../slices/wishlistSlice';
import JMWishlistNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMWishlistNetworkController';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';
import {useGlobalState} from '../../../jiomart-general/src/context/JMGlobalStateProvider';
import {
  GenericToast,
  genericToastTypeData,
} from '../../../jiomart-common/src/JMScreenSlot.types';

const wishlistController = new JMWishlistNetworkController();
const useWishlistOperation = () => {
  const {setToastTypeData} = useGlobalState();
  const dispatch = useDispatch();

  const getWishlistIds = useMutation({
    mutationFn: wishlistController.fetchWishlistIds,
    onSuccess: (response: any) => {
      dispatch(setWishlist(response));
    },
    retry: 0,
  });

  const addToWishlist = useMutation({
    mutationFn: wishlistController.addToWishlist,
    onSuccess: (data: any) => {
      if (data?.status === 'success') {
        setToastTypeData(
          genericToastTypeData(
            data?.added === false ? GenericToast.ERROR : GenericToast.SUCCESS,
            data?.result?.message ??
              (data?.added === false
                ? 'Something went wrong'
                : 'Added to Wishlist'),
          ),
        );
      }
      getWishlistIds.mutate();
    },
    retry: 0,
  });

  const removeFromWishlist = useMutation({
    mutationFn: wishlistController.removeFromWishlist,
    onSuccess: (data: any) => {
      if (data?.status === 'success') {
        setToastTypeData(
          genericToastTypeData(
            GenericToast.ERROR,
            data?.result?.message ?? 'Removed Successfully.',
          ),
        );
      }
      getWishlistIds.mutate();
    },
    retry: 0,
  });

  const generateAddToWishlistRequest = (request: any) => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return {
          item_id: request?.uid,
        };

      case AppSourceType.JM_BAU:
        return {
          productCode: request?.uid,
        };
      default:
        return {
          productCode: request?.uid,
        };
    }
  };
  const generateRemoveToWishlistRequest = (request: any) => {
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return {
          item_id: request?.uid,
        };

      case AppSourceType.JM_BAU:
        return {
          productCode: request?.uid,
        };
      default:
        return {
          productCode: request?.uid,
        };
    }
  };

  return {
    getWishlistIds,
    addToWishlist,
    removeFromWishlist,
    generateAddToWishlistRequest,
    generateRemoveToWishlistRequest,
  };
};

export default useWishlistOperation;
