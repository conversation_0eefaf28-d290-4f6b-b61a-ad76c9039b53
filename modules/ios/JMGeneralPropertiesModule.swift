import Foundation
import React
import CoreImage

 
@objc(JMGeneralPropertiesModule)
class JMGeneralPropertiesModule: NSObject {
 
    var resolver: RCTPromiseResolveBlock?
     var rejecter: RCTPromiseRejectBlock?
    override init() {
        super.init()
    }
    
    
 
   @objc
  func getDeviceInfo(_ resolve: @escaping RCTPromiseResolveBlock,
                     rejecter reject: @escaping RCTPromiseRejectBlock) {
    do {
      let device = UIDevice.current
      let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? ""
 
      let result: [String: Any] = [
        "device_model": device.model,
        "device_name": device.name,
        "device_os": "iOS",
        "os_version": device.systemVersion,
        "device_type": 3,
        "unique_id": device.identifierForVendor?.uuidString ?? "",
        "app_version": appVersion
      ]
 
      resolve(result)
    } catch {
      reject("DEVICE_INFO_ERROR", "Failed to get device info", error)
    }
  }

    @objc
    func decode(_ imageUri: String, resolver resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
        guard let url = URL(string: imageUri), let data = try? Data(contentsOf: url), let ciImage = CIImage(data: data) else {
            reject("QR_DECODE_ERROR", "Invalid image", nil)
            return
        }

        let detector = CIDetector(ofType: CIDetectorTypeQRCode, context: nil, options: [CIDetectorAccuracy: CIDetectorAccuracyHigh])
        let features = detector?.features(in: ciImage) ?? []

        if let qrFeature = features.first as? CIQRCodeFeature, let message = qrFeature.messageString {
            resolve(message)
        } else {
            reject("QR_DECODE_ERROR", "No QR code found", nil)
        }
    }
}
 
 