#import <React/RCTBridgeModule.h>
 
@interface RCT_EXTERN_MODULE(JMGeneralPropertiesModule, NSObject)

 
RCT_EXTERN_METHOD(getDeviceInfo:
    (RCTPromiseResolveBlock)resolve
    rejecter:(RCTPromiseRejectBlock)reject)

RCT_EXTERN_METHOD(decode:(NSString *)imageUri
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)

+ (BOOL)requiresMainQueueSetup
{
    return NO;
}
 
@end

 