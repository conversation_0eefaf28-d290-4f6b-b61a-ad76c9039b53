arguments=--init-script /var/folders/xl/7qmh372x47s3zgzj0hyzp7b1pcyyk0/T/d146c9752a26f79b52047fb6dc6ed385d064e120494f96f08ca63a317c41f94c.gradle --init-script /var/folders/xl/7qmh372x47s3zgzj0hyzp7b1pcyyk0/T/52cde0cfcf3e28b8b7510e992210d9614505e0911af0c190bd590d7158574963.gradle
auto.sync=false
build.scans.enabled=false
connection.gradle.distribution=GRADLE_DISTRIBUTION(VERSION(8.1.1))
connection.project.dir=
eclipse.preferences.version=1
gradle.user.home=
java.home=/Users/<USER>/Library/Java/JavaVirtualMachines/JHr-17.0.9/Contents/Home
jvm.arguments=
offline.mode=false
override.workspace.settings=true
show.console.view=true
show.executions.view=true
