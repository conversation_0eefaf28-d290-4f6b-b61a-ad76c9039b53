package com.jiomart.module
 
import android.content.Context
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.view.WindowManager
import com.facebook.react.module.annotations.ReactModule
import com.facebook.react.bridge.*

import android.location.LocationManager
import android.provider.Settings
import android.graphics.BitmapFactory
import android.net.Uri
import android.util.Base64
import android.util.Log
import com.google.zxing.*
import com.google.zxing.common.HybridBinarizer
import com.google.zxing.qrcode.QRCodeReader
import java.security.KeyFactory
import java.security.PublicKey
import java.security.spec.X509EncodedKeySpec
import javax.crypto.Cipher


@ReactModule(name = "JMGeneralPropertiesModule")
class JMGeneralPropertiesModule(var context: ReactApplicationContext) : ReactContextBaseJavaModule(context) {
    override fun getName(): String {
        return "JMGeneralPropertiesModule"
    }
 
    override fun canOverrideExistingModule(): <PERSON>olean {
        return true
    }
 
    @ReactMethod
    fun setResizeMode(flag : <PERSON>olean) {
        val activity = currentActivity
        Handler(Looper.getMainLooper()).post {
            if(flag){
                activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
            }
            else{
                activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN or
                 WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
            }
        }
    }

    @ReactMethod
    fun getDeviceInfo(promise: Promise) {
        try {
            val deviceInfo = Arguments.createMap()
            deviceInfo.putString("device_model", Build.MODEL ?: "")
            deviceInfo.putString("device_name", Build.DEVICE ?: "")
            deviceInfo.putString("device_os", "Android")
            deviceInfo.putString("os_version", Build.VERSION.RELEASE ?: "")
            deviceInfo.putInt("device_type", 2)
            deviceInfo.putString("unique_id", Settings.Secure.getString(
                reactApplicationContext.contentResolver,
                Settings.Secure.ANDROID_ID
            ))
 
            val packageInfo = reactApplicationContext.packageManager.getPackageInfo(
                reactApplicationContext.packageName, 0
            )
            deviceInfo.putString("app_version", packageInfo.versionName)
            deviceInfo.putBoolean("location_enabled", isLocationEnabled())
 
            promise.resolve(deviceInfo)
        } catch (e: Exception) {
            promise.reject("DEVICE_INFO_ERROR", "Failed to get device info", e)
        }
    }

    @ReactMethod
    fun decode(imageUri: String, promise: Promise) {
        try {
            val uri = Uri.parse(imageUri)
            val inputStream = context.contentResolver.openInputStream(uri)
            val bitmap = BitmapFactory.decodeStream(inputStream)
            inputStream?.close()

            val width = bitmap.width
            val height = bitmap.height
            val pixels = IntArray(width * height)
            bitmap.getPixels(pixels, 0, width, 0, 0, width, height)

            val source = com.google.zxing.RGBLuminanceSource(width, height, pixels)
            val binaryBitmap = BinaryBitmap(HybridBinarizer(source))
            val reader = QRCodeReader()
            val result = reader.decode(binaryBitmap)

            promise.resolve(result.text)
        } catch (e: Exception) {
            promise.reject("QR_DECODE_ERROR", e.message, e)
        }
    }


    private fun isLocationEnabled(): Boolean {
        return try {
            val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
            locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)
        } catch (e: Exception) {
            false
        }
    }


}
 