<resources>

    <style name="CustomDatePickerDialogTheme" parent="ThemeOverlay.AppCompat.Dialog">
        <!-- BackgroundColor of the date modal -->
        <item name="colorSecondary">@color/golden</item>
        <item name="android:colorAccent">@color/golden</item>
        <item name="colorAccent">@color/golden</item>
        <item name="android:textColor">@color/golden</item>
        <!-- textColor of the date modal -->
        <item name="android:textColorPrimary">@color/black</item>
   <item name="buttonBarButtonStyle">@style/CustomButtonBarButtonStyle</item>

    </style>
    <style name="CustomButtonBarButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/golden</item>
        <!-- Add more attributes as needed -->
    </style>

</resources>
