import {StyleSheet, View} from 'react-native';
import React from 'react';
import JMShimmer from '../../../../jiomart-general/src/ui/JMShimmer';
import {ShimmerKind} from '@jio/rn_components/src/index.types';
import {rh, rw} from '../../../../jiomart-common/src/JMResponsive';

const JMAddressLocationShimmer = () => {
  return (
    <View style={styles.container}>
      <JMShimmer
        kind={ShimmerKind.PARAGRAPH}
        width={rw(312)}
        height={rh(16)}
        style={styles.title}
      />
      <View style={styles.wrapper}>
        <JMShimmer
          kind={ShimmerKind.CIRCLE}
          width={rh(40)}
          height={rh(40)}
          style={styles.icon}
        />
        <JMShimmer
          kind={ShimmerKind.PARAGRAPH}
          width={rw(264)}
          height={rh(40)}
          style={styles.address}
        />
      </View>
    </View>
  );
};

export default JMAddressLocationShimmer;

const styles = StyleSheet.create({
  container: {marginTop: 8, marginBottom: 12},
  title: {
    marginBottom: rh(12),
    borderRadius: 32,
    backgroundColor: '#F5F5F5',
  },
  wrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '90%',
  },
  icon: {marginRight: 10, backgroundColor: '#F5F5F5'},
  address: {borderRadius: 32, backgroundColor: '#F5F5F5'},
});
