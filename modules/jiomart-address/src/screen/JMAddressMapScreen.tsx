import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>o<PERSON>con, JioText } from '@jio/rn_components';
import { ButtonState, IconColor, IconKind, IconSize, JioTypography } from '@jio/rn_components/src/index.types';
import { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  BackHandler,
  FlatList,
  Keyboard,
  Modal,
  Pressable,
  StyleSheet,
  TouchableWithoutFeedback,
  View
} from 'react-native';
import MapView, { Marker } from 'react-native-maps';
import { rh } from '../../../jiomart-common/src/JMResponsive';
import { getVersionSpecificFilterListData } from '../../../jiomart-common/src/utils/JMCommonFunctions';
import Divider, {
  DividerGap,
  DividerType,
} from '../../../jiomart-general/src/ui/Divider';
import JMMessageBox from '../../../jiomart-general/src/ui/JMMessageBox';
import ScreenSlot, {
  Deeplink<PERSON>andler,
} from '../../../jiomart-general/src/ui/JMScreenSlot';
import JMSearchBar from '../../../jiomart-general/src/ui/JMSearchBar';
import JMSearchSuggestionItem from '../../../jiomart-general/src/ui/JMSearchSugestionItem';
import {
  ErrorAlertIcon,
  MapLocationMarker,
  MapTooltipNegative,
  MapTooltipPositive,
} from '../assets/icons';
import JMAddressLocationShimmer from '../components/shimmer/JMAddressLocationShimmer';
import useJMAddressMapScreenController from '../controller/useJMAddressMapScreenController';
import type { JMAddressMapScreenProps } from '../types/JMAddressMapScreenType';

const JMAddressMapScreen = (props: JMAddressMapScreenProps) => {
  const {
    navigation,
    navigationBean,
    config,
    addressBoxConfig,
    isFetchingLocation,
    animateCamera,
    mapViewRef,
    regionCamera,
    location,
    search,
    handleChangeText,
    suggestion,
    currentAddress,
    insets,
    isMoving,
    isLocationLoading,
    isOrderReview,
    onRegionChange,
    onRegionChangeComplete,
    onMapReady,
    handleSearchSuggestionClick,
    suggestionClick,
    setSuggestionClick,
    handleConfirmLocation,
  } = useJMAddressMapScreenController(props);

  const [showDialog, setShowDialog] = useState(false);

  useEffect(() => {
    const backAction = () => {
      setShowDialog(true); // Show your custom dialog
      return true; // Prevent default back action
    };

    if (isOrderReview) {
      const backHandler = BackHandler.addEventListener(
        "hardwareBackPress",
        backAction
      );


      return () => backHandler.remove();
    }
  }, []);

  const renderItemSeperator = () => (
    <Divider
      type={DividerType.THIN}
      horizontal={DividerGap.GAP24}
      top={DividerGap.GAP12}
    />
  );
  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={bean => (
        <ScreenSlot
          navigationBean={bean}
          navigation={navigation}
          showCustomViewOnBackPress={isOrderReview ? () => {
            setShowDialog(true);
          } : undefined}
          children={() => (
            <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
              <View style={{ ...styles.container }}>
                {isLocationLoading && (
                  <View style={styles.loaderContainer}>
                    <ActivityIndicator size="small" />
                  </View>
                )}

                <>
                  {/* Custom Dialog */}
                  <Modal
                    transparent={true}
                    visible={showDialog}
                    animationType="fade"
                    onRequestClose={() => setShowDialog(false)}
                  >
                    <View style={styles.modalOverlay}>
                      <View style={styles.dialogBox}>
                        <View style={styles.dialogTextView}>
                          <JioText
                            // style={styles.name}
                            appearance={JioTypography.BODY_XS_BOLD}
                            text={'Please select location on map to proceed'}
                          />

                          <JioIcon
                            ic={'IcClose'}
                            color={IconColor.GREY100}
                            kind={IconKind.DEFAULT}
                            size={IconSize.SMALL}
                            isClickable={true}
                            onClick={() => {
                              setShowDialog(false)
                            }}
                          />
                        </View>

                        <JioButton
                          style={{ marginTop: 20 }}
                          title={'OK'}
                          stretch={true}
                          state={ButtonState.NORMAL}
                          onClick={() => {
                            setShowDialog(false)
                          }}
                        />
                      </View>
                    </View>
                  </Modal>


                  <View style={styles.locationSearchBar}>
                    {isOrderReview ? (
                      <View style={styles.mapErrorTextView}>
                        <ErrorAlertIcon width={25} height={25} />
                        <JioText
                          style={{ width: '100%', paddingLeft: 10, paddingRight: 50 }}
                          color={'feedback_error_50'}
                          appearance={JioTypography.BODY_XS}
                          text={'You havent set your location pin yet! Please confirm your location to proceed to payment.'}
                        />
                      </View>
                    ) : null}
                    <JMSearchBar
                      style={styles.search}
                      textInput={{
                        enablesReturnKeyAutomatically: true,
                        value: search,
                        onChangeText: handleChangeText,
                        placeholder: config?.searchBar?.placeholder,
                        onFocus: () => {
                          setSuggestionClick(false);
                        },
                      }}
                    />
                    {search && !suggestionClick && suggestion?.length > 0 ? (
                      <FlatList
                        data={suggestion}
                        bounces={false}
                        renderItem={({ item }) => {
                          return (
                            <JMSearchSuggestionItem
                              icon={{ ...getVersionSpecificFilterListData<any>(config?.searchSuggestion)?.[0]?.icon }}
                              title={{
                                ...getVersionSpecificFilterListData<any>(config?.searchSuggestion)?.[0]?.title,
                                text: item?.primaryText,
                              }}
                              subTitle={{
                                ...getVersionSpecificFilterListData<any>(config?.searchSuggestion)?.[0]?.subTitle,
                                text: item?.description,
                              }}
                              onPress={handleSearchSuggestionClick(
                                item?.placeID,
                              )}
                              style={styles.searchSuggestion}
                            />
                          );
                        }}
                        ItemSeparatorComponent={renderItemSeperator}
                        contentContainerStyle={{ marginBottom: 8 }}
                      />
                    ) : null}
                  </View>

                  <View style={styles.mapContainer}>
                    <MapView
                      ref={mapViewRef}
                      {...config?.map}
                      initialCamera={regionCamera}
                      style={styles.map}
                      onMapReady={onMapReady}
                      onRegionChange={onRegionChange}
                      onRegionChangeComplete={onRegionChangeComplete}>
                      <Marker
                        draggable
                        tappable
                        opacity={0}
                        coordinate={{
                          latitude: location.latitude,
                          longitude: location.longitude,
                        }}
                      />
                    </MapView>
                    <Pressable style={styles.fabIcon} onPress={animateCamera}>
                      <View style={styles.cameraFocus}>
                        {isFetchingLocation ? (
                          <ActivityIndicator {...config?.cameraFocus?.loader} />
                        ) : (
                          <JioIcon {...config?.cameraFocus?.icon} />
                        )}
                      </View>
                    </Pressable>

                    <View style={[styles.markerFixed]} pointerEvents="none">
                      {currentAddress ? (
                        <MapTooltipPositive />
                      ) : (
                        <MapTooltipNegative />
                      )}
                      <MapLocationMarker />
                    </View>
                  </View>

                  <View
                    style={[
                      styles.footer,
                      { paddingBottom: insets.bottom + 16 },
                    ]}>
                    {isMoving ? (
                      <JMAddressLocationShimmer />
                    ) : currentAddress ? (
                      <>
                        <JioText {...addressBoxConfig?.title} />
                        <View style={{ paddingVertical: 10 }}>
                          <JMSearchSuggestionItem
                            icon={{ ...addressBoxConfig?.address?.icon }}
                            subTitle={{
                              ...addressBoxConfig?.address?.subTitle,
                              text: currentAddress?.formattedAddress ? currentAddress?.formattedAddress : currentAddress?.address,
                            }}
                          />
                        </View>
                      </>
                    ) : (
                      <JMMessageBox
                        {...config?.alert?.invalidLocation}
                        style={{ marginBottom: 4 }}
                      />
                    )}
                    <JioButton
                      {...getVersionSpecificFilterListData<any>(addressBoxConfig?.address?.button)?.[0]}
                      state={
                        !currentAddress || isMoving
                          ? ButtonState.DISABLED
                          : ButtonState.NORMAL
                      }
                      onClick={handleConfirmLocation}
                    />
                  </View>
                </>
              </View>
            </TouchableWithoutFeedback>
          )}
        />
      )}
    />
  );
};

export default JMAddressMapScreen;

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1, backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center', alignItems: 'center'
  },
  dialogBox: {
    width: '90%', padding: 20, backgroundColor: 'white',
    borderRadius: 10, alignItems: 'center'
  },
  dialogTextView: {
    flexDirection: 'row', justifyContent: 'space-between', width: '100%', paddingVertical: 5
  },
  mapErrorTextView: {
    flexDirection: 'row', alignContent: 'center', alignItems: 'center', justifyContent: 'space-between', width: '100%', paddingVertical: 5, left: 24, right: 24,
  },
  map: {
    flex: 1,
    width: '100%',
  },
  cameraFocus: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  locationSearchBar: {
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    position: 'absolute',
    zIndex: 50,
  },

  fabIcon: {
    width: 56,
    height: 56,
    borderRadius: 45,
    position: 'absolute',
    bottom: '30%',
    right: 10,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  break: {
    height: rh(8),
    width: '100%',
    backgroundColor: '#F5F5F5',
  },
  container: {
    justifyContent: 'flex-start',
    flex: 1,
    flexDirection: 'column',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  column: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 1,
  },
  markerFixed: {
    marginTop: -100,
    position: 'absolute',
    top: '50%',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
  },
  footer: {
    backgroundColor: '#fff',
    bottom: 0,
    position: 'absolute',
    width: '100%',
    paddingTop: 16,
    paddingHorizontal: 24,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    rowGap: 8,
  },
  region: {
    color: '#fff',
    lineHeight: 20,
    margin: 20,
  },
  search: {
    marginHorizontal: 24,
    marginTop: 16,
    marginBottom: 8,
  },
  searchSuggestion: {
    marginHorizontal: 24,
  },
  loaderContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
    zIndex: 999,
  },
});
