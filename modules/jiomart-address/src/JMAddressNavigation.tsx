import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import type {NavigationStackData} from '../../jiomart-common/src/JMNavGraphUtil';
import {AppScreens} from '../../jiomart-common/src/JMAppScreenEntry';
import JMAddressListScreen from './screen/JMAddressListScreen';
import JMAddressSearchScreen from './screen/JMAddressSearchScreen';
import JMAddressMapScreen from './screen/JMAddressMapScreen';
import JMAddressFormScreen from './screen/JMAddressFormScreen';
import JMAddressFormV1Screen from './screen/JMAddressFormV1Screen';
import { Platform } from 'react-native';

const Stack = createNativeStackNavigator<NavigationStackData>();

const JMAddressNavigation = () => {
  return (
    <Stack.Navigator screenOptions={{
      headerShown: false,
      gestureEnabled: Platform.OS==="ios" ? false : true
      }}>
      <Stack.Screen
        name={AppScreens.ADDRESS_LIST_SCREEN}
        component={JMAddressListScreen}
        options={{
          animation: 'default',
        }}
      />
      <Stack.Screen
        name={AppScreens.ADDRESS_SEARCH_SCREEN}
        component={JMAddressSearchScreen}
        options={{
          animation: 'default',
        }}
      />
      <Stack.Screen
        name={AppScreens.ADDRESS_MAP_SCREEN}
        component={JMAddressMapScreen}
        options={{
          animation: 'default',
        }}
      />
      <Stack.Screen
        name={AppScreens.ADDRESS_FORM_SCREEN}
        component={JMAddressFormScreen}
        options={{
          animation: 'default',
        }}
      />
      <Stack.Screen
        name={AppScreens.ADDRESS_FORM_V1_SCREEN}
        component={JMAddressFormV1Screen}
        options={{
          animation: 'default',
        }}
      />
    </Stack.Navigator>
  );
};

export default JMAddressNavigation;
