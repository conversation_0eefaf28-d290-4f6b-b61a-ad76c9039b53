import {useQuery} from '@tanstack/react-query';
import {queryOptions} from '@tanstack/react-query';
import {RQKey} from '../../../jiomart-common/src/JMConstants';
import JMAddressNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMAddressNetworkController';

const addressController = new JMAddressNetworkController();

export const addressListOption = (refreshApi: boolean = false) => {
  return queryOptions({
    queryKey: [RQKey.GET_ADDRESS],
    queryFn: () => addressController.getAddressList(refreshApi),
    retry: 0,
  });
};

const useAddressList = (refreshApi: boolean = false) => {
  return useQuery(addressListOption(refreshApi));
};

export default useAddressList;
