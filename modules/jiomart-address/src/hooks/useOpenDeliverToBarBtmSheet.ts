import {useEffect} from 'react';
import {
  subscribeToRNEvent,
  unsubscribeToRNEvent,
} from '../../../jiomart-common/src/Emitter';
import {EventEmitterKeys} from '../../../jiomart-common/src/JMConstants';

const useOpenDeliverToBarBtmSheet = (callback?: (event: any) => void) => {
  useEffect(() => {
    const handleOpenPincodeChangeEvent = (event: any) => {
      callback?.(event);
    };
    subscribeToRNEvent(
      EventEmitterKeys.OPEN_PINCODE_BTM_SHEET,
      handleOpenPincodeChangeEvent,
    );
    return () => {
      unsubscribeToRNEvent(
        EventEmitterKeys.OPEN_PINCODE_BTM_SHEET,
        handleOpenPincodeChangeEvent,
      );
    };
  }, []);
};

export default useOpenDeliverToBarBtmSheet;
