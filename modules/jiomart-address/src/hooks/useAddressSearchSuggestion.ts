import {useState} from 'react';
import {
  PLACE_FIELDS,
  type PlacePrediction,
  type PredictionFiltersParam,
} from 'react-native-google-places-sdk';
import {
  fetchPlaceByID,
  fetchPredictions,
} from '../../../jiomart-common/src/GoogleSdkUtility';
import {getReverseGeoCodeFromLatLongNB} from '../../../jiomart-general/src/bridge/JMRNBridge';

interface UseAddressSearchSuggestionProps {
  shouldShowNoOfSearchSuggestion?: number;
  predictionFilter?: PredictionFiltersParam;
}

const useAddressSearchSuggestion = (props: UseAddressSearchSuggestionProps) => {
  const {
    shouldShowNoOfSearchSuggestion = 5,
    predictionFilter = {
      countries: ['in'],
    },
  } = props;

  const [search, setSearch] = useState('');
  const [searchSuggestion, setSearchSuggestion] = useState<PlacePrediction[]>(
    [],
  );
  const suggestion =
    searchSuggestion?.slice(0, shouldShowNoOfSearchSuggestion) ?? [];

  const handleChangeText = async (value: string) => {
    try {
      setSearch(value);
      if (!value.trim()) {
        setSearchSuggestion([]);
        return;
      }
      const addressListData = await fetchPredictions(value, predictionFilter);
      setSearchSuggestion(addressListData);
    } catch (error) {
      setSearchSuggestion([]);
    }
  };
  const handleSearchSuggestion = async (placeId: string) => {
    try {
      const place = await fetchPlaceByID(placeId, [PLACE_FIELDS.COORDINATE]);
      const lat = place?.coordinate?.latitude ?? 0;
      const long = place?.coordinate?.longitude ?? 0;
      const address = await getReverseGeoCodeFromLatLongNB(lat, long);
      return {place, address};
    } catch (error) {
      return null;
    }
  };
  return {
    search,
    suggestion,
    handleChangeText,
    handleSearchSuggestion,
  };
};

export default useAddressSearchSuggestion;
