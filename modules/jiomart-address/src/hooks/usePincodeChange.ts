import {useEffect} from 'react';
import {
  subscribeToRNEvent,
  unsubscribeToRNEvent,
} from '../../../jiomart-common/src/Emitter';
import {EventEmitterKeys} from '../../../jiomart-common/src/JMConstants';

const usePincodeChange = (callback?: (event: any) => void) => {
  useEffect(() => {
    const handlePincodeChangeEvent = (event: any) => {
      callback?.(event);
    };
    subscribeToRNEvent(
      EventEmitterKeys.PINCODE_CHANGE,
      handlePincodeChangeEvent,
    );
    return () => {
      unsubscribeToRNEvent(
        EventEmitterKeys.PINCODE_CHANGE,
        handlePincodeChangeEvent,
      );
    };
  }, []);
};

export default usePincodeChange;
