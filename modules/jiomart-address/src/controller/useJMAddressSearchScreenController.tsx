import { useState } from 'react';
import type { GeoCoordinates } from 'react-native-geolocation-service';
import { navBeanObj } from '../../../jiomart-common/src/JMNavGraphUtil';
import type { JMAddressModel } from '../../../jiomart-common/src/uiModals/JMAddressModel';
import { getVersionSpecificFilterListData } from '../../../jiomart-common/src/utils/JMCommonFunctions';
import { useConfigFile } from '../../../jiomart-general/src/hooks/useJMConfig';
import { navigateTo } from '../../../jiomart-general/src/navigation/JMNavGraph';
import { JMConfigFileName } from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import useAddressSearchSuggestion from '../hooks/useAddressSearchSuggestion';
import useCurrentLocation from '../hooks/useCurrentLocation';
import type { UseJMAddressSearchScreenProps } from '../types/JMAddressSearchScreenType';

const useJMAddressSearchScreenController = (
  props: UseJMAddressSearchScreenProps,
) => {
  const { navigation, route } = props;
  const config: any = getVersionSpecificFilterListData(useConfigFile(
    JMConfigFileName.JMAddressConfigurationFileNAme,
  )?.screen?.addressSearch)?.[0];

  const addressListConfig: any = getVersionSpecificFilterListData(useConfigFile(
    JMConfigFileName.JMAddressConfigurationFileNAme,
  )?.screen?.addressList)?.[0];

  const { isFetchingLocation, fetchLocationFromReverseGeoCodeFromLatLong } =
    useCurrentLocation({
      alertBlocked: config?.alert?.blockedLocation,
    });
  const { search, suggestion, handleChangeText, handleSearchSuggestion } =
    useAddressSearchSuggestion({
      shouldShowNoOfSearchSuggestion: config?.shouldShowNoOfSearchSuggestion,
      predictionFilter: config?.googlePredictionFilter,
    });
  const [suggestionClick, setSuggestionClick] = useState(false);

  const handleSearchSuggestionClick = (placeId: string) => {
    return async () => {
      setSuggestionClick(true);
      const response = await handleSearchSuggestion(placeId);
      if (response) {
        handleChangeText('');
        handleMapRedirection(
          response?.place?.coordinate as GeoCoordinates,
          JSON.parse(response?.address ?? ''),
        );
      }
    };
  };

  const handleCurrentLocation = async () => {
    const { address, coords }: any =
      await fetchLocationFromReverseGeoCodeFromLatLong();
    handleMapRedirection(coords, address);
  };

  const handleMapRedirection = (
    geoCoords: GeoCoordinates,
    address?: JMAddressModel,
  ) => {
    const ctaConfig: any = getVersionSpecificFilterListData(config?.searchSuggestion)?.[0]
    navigateTo(
      navBeanObj({
        ...ctaConfig?.cta,
        params: { coords: geoCoords, address },
      }),
      navigation,
    );
  };
  return {
    ...props,
    search,
    suggestion,
    handleChangeText,
    handleSearchSuggestionClick,
    handleCurrentLocation,
    isFetchingLocation,
    config,
    addressListConfig,
    suggestionClick,
    setSuggestionClick,
    navigationBean: route.params,
  };
};

export default useJMAddressSearchScreenController;
