import { useEffect, useRef, useState } from 'react';
import { Platform } from 'react-native';
import type { GeoPosition } from 'react-native-geolocation-service';
import type MapView from 'react-native-maps';
import type { Region } from 'react-native-maps';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { fetchPredictions } from '../../../jiomart-common/src/GoogleSdkUtility';
import {
  ActionType,
  getHomeAppScreenDestination, navBeanObj,
  NavigationType
} from '../../../jiomart-common/src/JMNavGraphUtil';
import { PlatformType } from '../../../jiomart-common/src/JMObjectUtility';
import { HeaderType } from '../../../jiomart-common/src/JMScreenSlot.types';
import { JMAddressModel } from '../../../jiomart-common/src/uiModals/JMAddressModel';
import { getVersionSpecificFilterListData, hideKeyboard } from '../../../jiomart-common/src/utils/JMCommonFunctions';
import { getReverseGeoCodeFromLatLongNB } from '../../../jiomart-general/src/bridge/JMRNBridge';
import { useConfigFile } from '../../../jiomart-general/src/hooks/useJMConfig';
import { navigateTo } from '../../../jiomart-general/src/navigation/JMNavGraph';
import { JMDatabaseManager } from '../../../jiomart-networkmanager/src/db/JMDatabaseManager';
import { JMConfigFileName } from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import { getBaseURL } from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import useAddressOperation from '../hooks/useAddressOperation';
import useAddressSearchSuggestion from '../hooks/useAddressSearchSuggestion';
import useCurrentLocation from '../hooks/useCurrentLocation';
import type { UseJMAddressMapScreenProps } from '../types/JMAddressMapScreenType';

const useJMAddressMapScreenController = (props: UseJMAddressMapScreenProps) => {
  const defaultLocation = {
    latitude: 18.9348965,
    longitude: 72.8161317,
  };
  const { route, navigation } = props;
  const isOrderReview = route?.params?.params?.orderReviewFlow ?? false;
  let getCurrentLocation = false;

  const config: any = getVersionSpecificFilterListData(useConfigFile(
    JMConfigFileName.JMAddressConfigurationFileNAme
  )?.screen?.addressMap)?.[0];
  const addressBoxConfig = getVersionSpecificFilterListData<any>(config?.addressBox)?.[0]
  let location = route.params?.params?.coords;
  const address = route.params?.params?.address;
  if (!location) {
    location = {
      latitude: config?.defaultLocation?.latitude ?? defaultLocation.latitude,
      longitude:
        config?.defaultLocation?.longitude ?? defaultLocation.longitude,
    };
    getCurrentLocation = true;
  } else {
    getCurrentLocation = false;
  }

  useEffect(() => {
    if (!getCurrentLocation) {
      if (location.latitude == 0.0) {
        setLatLngFromPincode(address.pin)
      } else
        setIsLocationLoading(false);
    }
  }, [getCurrentLocation]);

  const regionCamera = {
    ...config?.map?.initialCamera,
    center: {
      latitude: location.latitude,
      longitude: location.longitude,
    },
  };

  const insets = useSafeAreaInsets();
  const { isFetchingLocation, fetchCurrentLocation } = useCurrentLocation({
    alertBlocked: config?.alert?.blockedLocation,
  });

  const { search, suggestion, handleChangeText, handleSearchSuggestion } =
    useAddressSearchSuggestion({
      shouldShowNoOfSearchSuggestion: config?.shouldShowNoOfSearchSuggestion,
      predictionFilter: config?.googlePredictionFilter,
    });

  const [currentAddress, setCurrentAddress] = useState<JMAddressModel | null>(
    address,
  );

  const [isLocationLoading, setIsLocationLoading] = useState(true);
  const [isMoving, setIsMoving] = useState(false);
  const [suggestionClick, setSuggestionClick] = useState(false);

  const savedAddress = useRef<JMAddressModel>(address);
  const isInitialRegionChange = useRef(0);
  const mapViewRef = useRef<MapView | null>(null);

  const setLatLngFromPincode = async (pinCode: string) => {
    const predictionFilter = {
      countries: ['in'],
    }
    const addressListData = await fetchPredictions(pinCode, predictionFilter);
    if (addressListData) {
      const response = await handleSearchSuggestion(addressListData[0].placeID);
      if (response?.place?.coordinate) {
        location = {
          latitude: response?.place?.coordinate?.latitude,
          longitude:
            response?.place?.coordinate?.longitude,
        };

        const cameraFocus = await mapViewRef.current?.getCamera();
        if (cameraFocus) {
          cameraFocus.center.latitude = response?.place?.coordinate?.latitude;
          cameraFocus.center.longitude = response?.place?.coordinate?.longitude;
          mapViewRef.current?.animateCamera(cameraFocus);
        }

        setIsLocationLoading(false);

      }
    }
  }

  const handleSearchSuggestionClick = (placeId: string) => {
    return async () => {
      setSuggestionClick(true);
      const cameraFocus = await mapViewRef.current?.getCamera();
      const response = await handleSearchSuggestion(placeId);
      if (response?.place?.coordinate && cameraFocus) {
        hideKeyboard()
        cameraFocus.center.latitude = response?.place?.coordinate?.latitude;
        cameraFocus.center.longitude = response?.place?.coordinate?.longitude;
        mapViewRef.current?.animateCamera(cameraFocus);
      }
    };
  };
  const animateCamera = async () => {
    try {
      if (isInitialRegionChange.current === 0) {
        isInitialRegionChange.current = 2;
      }
      const position = (await fetchCurrentLocation()) as GeoPosition;
      const cameraFocus = await mapViewRef.current?.getCamera();
      if (position?.coords && cameraFocus) {
        cameraFocus.center.latitude = position?.coords?.latitude;
        cameraFocus.center.longitude = position?.coords?.longitude;
        // Reset zoom to default from config
        if (config?.map?.initialCamera?.zoom) {
          cameraFocus.zoom = config.map.initialCamera.zoom;
        }
        mapViewRef.current?.animateCamera(cameraFocus);
      }
    } catch (error) {
      console.log('🚀 ~ animateCamera ~ error:', error);
    }
  };
  const onRegionChange = () => {
    console.log('🚀 ~ onRegionChange ~ onRegionChange:');
    if (isInitialRegionChange.current == 0) {
      console.log(
        '🚀 ~ onRegionChange ~ skipping initial region change on Android',
      );
      return;
    }
    setIsMoving(true);
  };
  const onRegionChangeComplete = async (region: Region) => {
    try {
      console.log('🚀 ~ onRegionChangeComplete ~ onRegionChangeComplete:');
      if (isInitialRegionChange.current == 0) {
        console.log(
          '🚀 ~ onRegionChangeComplete ~ skipping initial region change on Android',
        );
        isInitialRegionChange.current = 1;
        if (getCurrentLocation) {
          animateCamera();
          getCurrentLocation = false;
        }
        return;
      }
      console.log('🚀 ~ onRegionChangeComplete ~ processing region change');
      setIsLocationLoading(false);
      const lat = region?.latitude ?? 0;
      const long = region?.longitude ?? 0;
      if (lat && long) {
        let latestAddress = await getReverseGeoCodeFromLatLongNB(lat, long);
        latestAddress = JSON.parse(latestAddress ?? '');
        if (latestAddress?.address) {
          setCurrentAddress(prev => {
            return {
              ...savedAddress.current,
              ...prev,
              ...latestAddress,
            };
          });
        } else {
          setCurrentAddress(null);
        }
      }
    } catch (error) {
      console.log('🚀 ~ onRegionChangeComplete ~ error:', error);
      setCurrentAddress(null);
    } finally {
      setIsMoving(false);
    }
  };


  const { checkAndSetPincode } = useAddressOperation();
  const handleConfirmLocation = async () => {
    if (!JMDatabaseManager.user.isUserLoggedInFlag()) {
      await checkAndSetPincode(currentAddress, true);
      navigateTo(
        navBeanObj({
          actionType: ActionType.OPEN_WEB_URL,
          destination: getHomeAppScreenDestination(),
          headerVisibility: HeaderType.CUSTOM,
          navigationType: NavigationType.RESET,
          loginRequired: false,
          actionUrl: getBaseURL(),
          shouldShowBottomNavBar: false,
          shouldShowDeliverToBar: true,
          headerType: 5,
        }),
        navigation,
      );
      return;
    }
    const ctaConfig: any = getVersionSpecificFilterListData(addressBoxConfig?.address?.button)?.[0]
    const cta: any = address?.id
      ? ctaConfig?.ctaAlt
      : ctaConfig?.cta;

    const navParams = {
      ...cta,
      params: {
        address: currentAddress,
        isOrderReview,
      },
    };

    navigateTo(navBeanObj(navParams), navigation);
  };

  const onMapReady = async () => {
    console.log('🚀 ~ onMapReady ~ Map is ready');
    if (Platform.OS === PlatformType.ANDROID) {
      setTimeout(() => {
        if (mapViewRef.current) {
          mapViewRef.current.getCamera().then(camera => {
            mapViewRef.current?.animateCamera(camera);
          });
        }
      }, Platform.OS === PlatformType.ANDROID ? 300 : 1200);
    }
  };

  return {
    ...props,
    config,
    addressBoxConfig,
    mapViewRef,
    regionCamera,
    isFetchingLocation,
    location,
    animateCamera,
    search,
    handleChangeText,
    handleSearchSuggestionClick,
    suggestion,
    currentAddress,
    insets,
    isMoving,
    isLocationLoading,
    onRegionChange,
    onRegionChangeComplete,
    onMapReady, // Add the onMapReady handler
    suggestionClick,
    setSuggestionClick,
    handleConfirmLocation,
    navigationBean: route.params,
    isOrderReview,
  };
};

export default useJMAddressMapScreenController;
