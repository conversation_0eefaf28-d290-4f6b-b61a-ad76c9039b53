import type {UseJMRatingAndReviewSuccessScreenProps} from '../types/JMRatingAndReviewSuccessScreenType';
import {useConfigFile} from '../../../jiomart-general/src/hooks/useJMConfig';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useEffect} from 'react';
import {BackHandler} from 'react-native';
import {useGlobalState} from '../../../jiomart-general/src/context/JMGlobalStateProvider';

const useRatingAndReviewSuccessScreenController = (
  props: UseJMRatingAndReviewSuccessScreenProps,
) => {
  const {route, navigation} = props;

  const {productName, productImage, review} = route.params.params;

  const config = useConfigFile(JMConfigFileName.JMFeedbackConfigurationFileName)
    ?.ratingAndReviewSuccess;
  const {setRefreshScreen} = useGlobalState();

  const insets = useSafeAreaInsets();

  const handleDoneClick = () => {
    navigation.goBack();
    navigation.goBack();
    setRefreshScreen(true);
  };

  useEffect(() => {
    const backAction = () => {
      handleDoneClick();
      return true;
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );

    return () => {
      backHandler.remove();
    };
  }, []);

  return {
    ...props,
    navigationBean: route.params,
    config,
    productName,
    productImage,
    review,
    insets,
    handleDoneClick,
  };
};

export default useRatingAndReviewSuccessScreenController;
