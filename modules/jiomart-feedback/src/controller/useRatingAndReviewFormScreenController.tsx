import {useConfigFile} from '../../../jiomart-general/src/hooks/useJMConfig';
import type {UseRatingAndReviewFormScreenProps} from '../types/JMRatingAndReviewFormScreenType';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {BackHandler, Keyboard} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import JMFeedbackNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMFeedbackNetworkController';
import {useQuery} from '@tanstack/react-query';
import {RQKey} from '../../../jiomart-common/src/JMConstants';
import {useEffect, useRef, useState} from 'react';
import useFeedbackOperation from '../hooks/useFeedbackOperation';
import {launchImageLibrary} from 'react-native-image-picker';
import {navigateTo} from '../../../jiomart-general/src/navigation/JMNavGraph';
import {navBeanObj} from '../../../jiomart-common/src/JMNavGraphUtil';
import axios from 'axios';
// import {splitUrl} from '../../../jiomart-networkmanager/src/JMNetworkController/SearchService';
import { useGlobalState } from '../../../jiomart-general/src/context/JMGlobalStateProvider';

export enum FormNameField {
  RATE = 'rate',
  TITLE = 'title',
  CONTENT = 'content',
  IMAGE = 'image',
}

const feedbackController = new JMFeedbackNetworkController();

const useRatingAndReviewFormScreenController = (
  props: UseRatingAndReviewFormScreenProps,
) => {
  const {route, navigation} = props;
  const {
    productId,
    alternateCode1,
    alternateCode2,
    productURL,
    image,
    productImage,
    categoryL1,
    categoryL2,
    categoryL3,
    categoryL4,
    rating,
    ingressPoint,
    productName,
    title,
    content,
    id,
  } = route.params?.params;
  const config = useConfigFile(JMConfigFileName.JMFeedbackConfigurationFileName)
    ?.ratingAndReviewForm;
  const {setRefreshScreen} = useGlobalState();

  const insets = useSafeAreaInsets();
  const feedbackConfig = useQuery({
    queryKey: [RQKey.FEEDBACK_CONFIG],
    queryFn: () => feedbackController.getFeedbackConfig(),
    retry: 0,
  })?.data?.data?.data;

  const {
    uploadImage,
    saveRatingAndReviews,
    editRatingAndreviews,
    generateSaveRatingAndReviewRequestBody,
    generateEditRatingAndReviewRequestBody,
  } = useFeedbackOperation();

  const [loading, setLoading] = useState(false);
  const [images, setImages] = useState<any[]>([]);

  const [continueReviewBottomSheet, setContinueReviewBottomSheet] =
    useState(false);

  const [editImages, setEditImages] = useState<any[]>(image ?? []);
  const [formData, setFormData] = useState({
    [FormNameField.RATE]: rating,
    [FormNameField.TITLE]: id ? title : '',
    [FormNameField.CONTENT]: id ? content : '',
  });
  const [formError, setFormError] = useState({
    [FormNameField.RATE]: '',
    [FormNameField.TITLE]: '',
    [FormNameField.CONTENT]: '',
  });
  const inputRef = useRef({
    [FormNameField.RATE]: null,
    [FormNameField.TITLE]: null,
    [FormNameField.CONTENT]: null,
  });
  const mappedConfig: any = {
    [FormNameField.RATE]: {
      hidden: feedbackConfig?.rating?.hidden,
      max: feedbackConfig?.rating?.max,
      min: feedbackConfig?.rating?.min,
      required: feedbackConfig?.rating?.required,
      step: feedbackConfig?.rating?.step,
    },
    [FormNameField.IMAGE]: {
      countLimit: feedbackConfig?.image?.countLimit,
      hidden: feedbackConfig?.image?.hidden,
      required: feedbackConfig?.image?.required,
      sizeLimitInMegaBytes: feedbackConfig?.image?.sizeLimitInMegaBytes,
    },
    [FormNameField.TITLE]: {
      hidden: feedbackConfig?.title?.hidden,
      maxCharLimit: feedbackConfig?.title?.maxCharLimit,
      minCharLimit: feedbackConfig?.title?.minCharLimit,
      required: feedbackConfig?.title?.required,
    },
    [FormNameField.CONTENT]: {
      hidden: feedbackConfig?.content?.hidden,
      maxCharLimit: feedbackConfig?.content?.maxCharLimit,
      minCharLimit: feedbackConfig?.content?.minCharLimit,
      required: feedbackConfig?.content?.required,
    },
  };

  const errorMessage: any = {
    [FormNameField.RATE]: {
      max: feedbackConfig?.rating?.max,
      min: feedbackConfig?.rating?.min,
      required: feedbackConfig?.rating?.required,
    },
    [FormNameField.IMAGE]: {
      countLimit: feedbackConfig?.image?.countLimit ?? 3,
      hidden: feedbackConfig?.image?.hidden ?? false,
      required: feedbackConfig?.image?.required ?? false,
      sizeLimitInMegaBytes: feedbackConfig?.image?.sizeLimitInMegaBytes ?? 2,
    },
    [FormNameField.TITLE]: {
      maxCharLimit: config?.WriteATitle?.validation?.maxCharLimit?.replace(
        '[CHAR]',
        mappedConfig[FormNameField.TITLE]?.maxCharLimit ?? '',
      ),
      minCharLimit: config?.WriteATitle?.validation?.minCharLimit?.replace(
        '[CHAR]',
        mappedConfig[FormNameField.TITLE]?.minCharLimit ?? '',
      ),
      required: config?.WriteATitle?.validation?.required,
    },
    [FormNameField.CONTENT]: {
      maxCharLimit: config?.ShareAReview?.validation?.maxCharLimit?.replace(
        '[CHAR]',
        mappedConfig[FormNameField.CONTENT]?.maxCharLimit ?? '',
      ),
      minCharLimit: config?.ShareAReview?.validation?.minCharLimit?.replace(
        '[CHAR]',
        mappedConfig[FormNameField.CONTENT]?.maxCharLimit ?? '',
      ),
      required: config?.ShareAReview?.validation?.required,
    },
  };

  const isGalleryOpen = useRef(false);

  const imageSelectionLimit =
    mappedConfig[FormNameField.IMAGE]?.countLimit -
    (editImages?.length + images?.length);

  const imageLibOption = {
    mediaType: 'photo',
    selectionLimit: imageSelectionLimit,
    includeExtra: true,
    restrictMimeTypes: ['image/png', 'image/jpeg', 'image/jpg'],
  };

  const handleFormData = (field: string) => {
    return (text: string) => {
      setFormData({
        ...formData,
        [field]: text,
      });
      clearFieldError(field);
    };
  };

  const handleFormError = (field: string, message: string) => {
    return () => {
      setFormError({
        ...formError,
        [field]: message,
      });
    };
  };

  const clearFieldError = (field: string) => {
    setFormError(prevErrors => ({
      ...prevErrors,
      [field]: '',
    }));
  };

  const handleInputRef = (field: string) => {
    return (ref: any) => {
      if (ref) {
        inputRef.current[field] = ref;
      }
    };
  };

  const onBlurValidata = (field: string) => {
    switch (field) {
      case FormNameField.TITLE:
        if (
          formData[FormNameField.TITLE]?.length === 0 &&
          mappedConfig[FormNameField.TITLE]?.required
        ) {
          return errorMessage[FormNameField.TITLE]?.required;
        }
        if (
          formData[FormNameField.TITLE]?.length > 0 &&
          mappedConfig[FormNameField.TITLE]?.minCharLimit >
            formData[FormNameField.TITLE]?.length
        ) {
          return errorMessage[FormNameField.TITLE]?.minCharLimit;
        }
        if (
          formData[FormNameField.TITLE]?.length >
            mappedConfig[FormNameField.TITLE]?.minCharLimit &&
          mappedConfig[FormNameField.TITLE]?.maxCharLimit <
            formData[FormNameField.TITLE]?.length
        ) {
          return errorMessage[FormNameField.TITLE]?.maxCharLimit;
        }
        return '';
      case FormNameField.CONTENT:
        if (
          formData[FormNameField.CONTENT]?.length === 0 &&
          mappedConfig[FormNameField.CONTENT]?.required
        ) {
          return errorMessage[FormNameField.CONTENT]?.required;
        }
        if (
          formData[FormNameField.CONTENT]?.length > 0 &&
          mappedConfig[FormNameField.CONTENT]?.minCharLimit >
            formData[FormNameField.CONTENT]?.length
        ) {
          return errorMessage[FormNameField.CONTENT]?.minCharLimit;
        }
        if (
          formData[FormNameField.CONTENT]?.length >
            mappedConfig[FormNameField.CONTENT]?.minCharLimit &&
          mappedConfig[FormNameField.CONTENT]?.maxCharLimit <
            formData[FormNameField.CONTENT]?.length
        ) {
          return errorMessage[FormNameField.CONTENT]?.maxCharLimit;
        }
        return '';
      default:
        return '';
    }
  };
  const validateFormData = () => {
    const title = onBlurValidata(FormNameField.TITLE);
    const content = onBlurValidata(FormNameField.CONTENT);

    if (title) {
      handleFormError(FormNameField.TITLE, title)();
      inputRef.current?.[FormNameField.TITLE]?.focus?.();
    }

    if (content) {
      handleFormError(FormNameField.CONTENT, content)();
      inputRef.current?.[FormNameField.CONTENT]?.focus?.();
    }

    return title || content;
  };

  const handleFAQLink = () => {
    navigateTo(
      navBeanObj({
        ...config?.FAQSection?.cta,
      }),
      navigation,
    );
  };

  const selectGalleryImage = async (option: any) => {
    try {
      return await launchImageLibrary(option, response => {
        isGalleryOpen.current = false;
        return response;
      });
    } catch (error) {
      isGalleryOpen.current = false;
      return null;
    }
  };

  const validateFileMimeType = types => {
    return types?.every(type =>
      imageLibOption.restrictMimeTypes?.includes(type),
    );
  };

  const validateFileSize = (size: number) => {
    return (
      size <=
      mappedConfig[FormNameField.IMAGE].sizeLimitInMegaBytes * 1024 * 1024
    );
  };

  const validateFilePath = async (uri: string) => {
    try {
      if (!uri) {
        return false;
      }

      const normalizedPath = uri.startsWith('file://')
        ? decodeURIComponent(uri.replace(/^file:\/\//, '').trim())
        : decodeURIComponent(uri.trim());

      const exists = await RNFS.exists(normalizedPath);

      if (!exists) {
        return false;
      }
      return true;
    } catch (error) {
      return false;
    }
  };

  const uploadImages = async () => {
    try {
      if (isGalleryOpen.current) {
        return;
      }
      isGalleryOpen.current = true;
      const response = await selectGalleryImage(imageLibOption);

      if (response?.assets && response?.assets?.length > 0) {
        let validSizeImages = response?.assets?.filter(img =>
          validateFileMimeType([img.type]),
        );

        validSizeImages = response?.assets?.filter(img =>
          validateFileSize(img?.fileSize),
        );

        // validSizeImages = validSizeImages?.filter(async img => {
        //   return await validateFilePath(img?.uri);
        // });

        if (validSizeImages?.length != response.assets?.length) {
          // showToast({
          //   visible: true,
          //   message: config?.AddPhotosBlock?.sizeLimitErrorToast?.replace?.(
          //     '[SIZE_LIMIT]',
          //     mappedConfig[FormNameField.IMAGE].sizeLimitInMegaBytes,
          //   ),
          //   position: ToastPosition.BOTTOM_CENTER,
          //   offset: 100,
          //   type: ToastType.ALERT,
          //   haultDuration: 2500,
          //   duration: 400,
          //   screens: [Route.RATING_AND_REVIEW_FORM_SCREEN],
          // });
        }
        setImages((prev: any[]) => {
          return [...prev, ...validSizeImages];
        });
      }
    } catch (error) {
      console.log('🚀 ~ uploadImages ~ error:', error);
      return null;
    }
  };

  const removeImage = (index: number, edit?: boolean) => {
    if (edit) {
      const newArray = [
        ...editImages?.slice(0, index),
        ...editImages?.slice(index + 1),
      ];
      setEditImages(newArray);
    } else {
      const newArray = [
        ...images?.slice(0, index),
        ...images?.slice(index + 1),
      ];
      setImages(newArray);
    }
  };

  const openContinueReviewBottomSheet = () => {
    Keyboard.dismiss();
    setContinueReviewBottomSheet(true);
  };
  const closeContinueReviewBottomSheet = () => {
    setContinueReviewBottomSheet(false);
  };

  const uploadSingleImage = async (image: any) => {
    return new Promise((resolve, reject) => {
      uploadImage.mutate(image, {
        onSuccess: response => {
          resolve(response);
          console.log('Review submitted successfully', response);
        },
        onError: error => {
          reject(error);
          console.error('Error submitting review', error);
        },
      });
    });
  };
  const callUploadImage = async image => {
    try {
      // const generateImageSignedUrlReq = image.map(img => ({
      //   fileName: img.fileName,
      //   size: img.size,
      //   contentType: 'image/jpeg',
      // }));
      const imageResponse = await Promise.all(image?.map(uploadSingleImage));
      console.log('🚀 ~ imageResponse:', imageResponse);
      return imageResponse;
      // return new Promise((resolve, reject) => {
      //   let uploadedImagesProgress = [];

      //   generateImageSignedUrl.mutate(generateImageSignedUrlReq, {
      //     onSuccess: async data => {
      //       try {
      //         const uploadPromises = data?.data?.map(async uploadSignature => {
      //           console.log('🚀 ~ uploadSignature:', uploadSignature);

      //           const imgObject = image.find(
      //             img => img.fileName === uploadSignature.originalFileName,
      //           );

      //           if (!imgObject) return null;

      // const formData = new FormData();
      // formData.append('file', {
      //   uri: imgObject.uri,
      //   name: imgObject.fileName,
      //   type: 'image/jpeg',
      // });

      //           const splitUrlData = splitUrl(uploadSignature?.uploadUrl);
      //           console.log('🚀 ~ splitUrlData:', JSON.stringify(splitUrlData));

      //           try {
      //             const response = await ImageApiService(
      //               splitUrlData?.host + splitUrlData?.path,
      //               formData,
      //               splitUrlData?.params?.signature ?? '',
      //             );

      //             if (response.data?.resultInfo?.status === 'SUCCESS') {
      //               console.log(
      //                 '🚀 ~ status:',
      //                 response.data?.resultInfo?.status,
      //               );

      //               uploadedImagesProgress.push(uploadSignature);
      //               return uploadSignature;
      //             }
      //           } catch (err) {
      //             console.error('🚀 ~ Upload Error:', err);
      //           }
      //           return null;
      //         });

      //         // Wait for all uploads to complete
      //         const results = await Promise.all(uploadPromises);
      //         resolve(results.filter(Boolean)); // Remove null values
      //       } catch (err) {
      //         reject(err);
      //       }
      //     },
      //     onError: error => {
      //       console.error('Error generating signed URL:', error);
      //       reject(error);
      //     },
      //   });
      // });
    } catch (error) {
      console.error('<> Upload error:', error);
      return [];
    }
  };

  const onPressOfSubmit = async () => {
    if (validateFormData()) {
      return;
    }
    setLoading(true);
    let successfullUploadImage: any[] = [];

    if (id) {
      successfullUploadImage = editImages?.map(img => {
        return {
          fileName: img?.fileName,
          url: img?.url,
        };
      });
    }

    if (images?.length > 0) {
      const uploadImages: any = (await callUploadImage(images)) ?? [];
      const extractImages = uploadImages?.map(img => {
        return {
          fileName: img?.data?.[0]?.fileName,
          fileUrl: img?.data?.[0]?.fileUrl,
        };
      });
      successfullUploadImage = [...successfullUploadImage, ...extractImages];
    }

    const data = {
      productId: productId ?? '',
      alternateCode1: alternateCode1 ?? '',
      title: formData[FormNameField.TITLE],
      content: formData[FormNameField.CONTENT],
      rating: formData[FormNameField.RATE],
      images: successfullUploadImage,
      alternateCode2: alternateCode2 ?? '',
      productURL: productURL,
      categoryL1: categoryL1,
      categoryL2: categoryL2,
      categoryL3: categoryL3,
      categoryL4: categoryL4,
      ingressPoint,
      id,
    };

    if (id) {
      editRatingAndreviews.mutate(
        {param: {id}, body: generateEditRatingAndReviewRequestBody(data)},
        {
          onSuccess: results => {
            console.log('Review edited successfully', results);
            setLoading(false);
            if (results?.resultInfo?.status === 'SUCCESS') {
              // queryClient.invalidateQueries({
              //   queryKey: [RQKeyConstant.PRODUCT_USER_REVIEW],
              // });
              // queryClient.invalidateQueries({
              //   queryKey: [RQKeyConstant.PRODUCT_AVERAGE_RATING],
              // });
              navigation.goBack();
              setRefreshScreen(true);
              // setTimeout(() => {
              //   showToast({
              //     visible: true,
              //     message: config?.FAQSection?.EditToast ?? '',
              //     position: ToastPosition.BOTTOM_CENTER,
              //     offset: 100,
              //     type: ToastType.SUCCESS,
              //     haultDuration: 5000,
              //     duration: 500,
              //     screens: [Route.PRODUCT_DETAILS_SCREEN],
              //   });
              // }, 500);
            }
            if (results?.info?.resultInfo?.status === 'FAILURE') {
              // showToast({
              //   visible: true,
              //   message: results?.info?.resultInfo?.message ?? '',
              //   position: ToastPosition.BOTTOM_CENTER,
              //   offset: 100,
              //   type: ToastType.ALERT,
              //   haultDuration: 2500,
              //   duration: 500,
              //   screens: [Route.RATING_AND_REVIEW_FORM_SCREEN],
              // });
            }
            console.log('Product details screen toast');
          },
          onError: error => {
            setLoading(false);
            // showToast({
            //   visible: true,
            //   message: 'Something Went Wrong',
            //   position: ToastPosition.BOTTOM_CENTER,
            //   offset: 100,
            //   type: ToastType.ALERT,
            //   haultDuration: 2500,
            //   duration: 500,
            //   screens: [Route.RATING_AND_REVIEW_FORM_SCREEN],
            // });
            console.error('Error editing review', error);
          },
        },
      );
    } else {
      saveRatingAndReviews.mutate(
        {body: generateSaveRatingAndReviewRequestBody(data)},
        {
          onSuccess: response => {
            console.log('Review submitted successfully', response);
            setLoading(false);
            if (response?.resultInfo?.status === 'SUCCESS') {
              navigateTo(
                navBeanObj({
                  actionType: 'T001',
                  navTitle: 'Rate & Review',
                  source: '',
                  destination: 'JMRatingAndReviewSuccessScreen',
                  bundle: '',
                  headerType: 1,
                  params: {
                    productName: productName,
                    productImage: productImage,
                    review: response,
                  },
                }),
                navigation,
              );
              // queryClient.invalidateQueries({
              //   queryKey: [RQKeyConstant.PRODUCT_USER_REVIEW],
              // });
              // queryClient.invalidateQueries({
              //   queryKey: [RQKeyConstant.PRODUCT_AVERAGE_RATING],
              // });
            }
            if (response?.resultInfo?.status === 'FAILURE') {
              // showToast({
              //   visible: true,
              //   message: response?.resultInfo?.message ?? '',
              //   position: ToastPosition.BOTTOM_CENTER,
              //   offset: 100,
              //   type: ToastType.ALERT,
              //   haultDuration: 2500,
              //   duration: 500,
              //   screens: [Route.RATING_AND_REVIEW_FORM_SCREEN],
              // });
            }
          },
          onError: error => {
            setLoading(false);
            // showToast({
            //   visible: true,
            //   message: 'Something Went Wrong',
            //   position: ToastPosition.BOTTOM_CENTER,
            //   offset: 100,
            //   type: ToastType.ALERT,
            //   haultDuration: 2500,
            //   duration: 500,
            //   screens: [Route.RATING_AND_REVIEW_FORM_SCREEN],
            // });
            console.error('Error submitting review', error);
          },
        },
      );
    }
  };

  useEffect(() => {
    const backAction = () => {
      openContinueReviewBottomSheet();
      return true;
    };
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );
    return () => {
      return backHandler.remove();
    };
  }, []);

  return {
    config,
    ...props,
    navigationBean: route.params,
    insets,
    productName,
    productImage,
    formData,
    formError,
    handleFormData,
    mappedConfig,
    handleInputRef,
    handleFormError,
    onBlurValidata,
    images,
    editImages,
    imageSelectionLimit,
    uploadImages,
    removeImage,
    continueReviewBottomSheet,
    openContinueReviewBottomSheet,
    closeContinueReviewBottomSheet,
    onPressOfSubmit,
    loading,
    handleFAQLink,
  };
};

export default useRatingAndReviewFormScreenController;
const ImageApiService = async (
  endPoint: string,
  multipartFormData: FormData,
  signatureData: string,
) => {
  try {
    console.log('multipartFormData', JSON.stringify(multipartFormData));
    const response = await axios.put(
      endPoint + `?signature=${encodeURIComponent(signatureData)}`,
      multipartFormData,
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'multipart/form-data',
        },
        transformRequest: (data, _) => {
          return data;
        },
        onUploadProgress: progressEvent => {
          console.log('Upload Progress:', progressEvent);
        },
      },
    );

    console.log('Upload Success:', response.data);
    return response;
  } catch (error) {
    console.log('Upload Error:', JSON.stringify(error));
    throw error;
  }
};
