import {StyleSheet, View, type StyleProp, type ViewStyle} from 'react-native';
import React from 'react';
import {JioTypography} from '@jio/rn_components/src/index.types';
import {JioText} from '@jio/rn_components';
import {rw} from '../../../jiomart-common/src/JMResponsive';
import CustomMediaRendered, {
  Kind,
} from '../../../jiomart-general/src/ui/CustomMediaRendered';

interface ReviewProductProps {
  style?: StyleProp<ViewStyle>;
  image?: string;
  name?: string;
}

const ReviewProduct = (props: ReviewProductProps) => {
  const {image, name, style} = props;
  return (
    <View style={[styles.ProductTopContainer, style]}>
      {image ? (
        <CustomMediaRendered
          kind={Kind.IMAGE}
          mediaUrl={image}
          customStyles={styles.ProductImage}
          width={rw(84)}
          height={rw(84)}
        />
      ) : null}
      {name ? (
        <JioText
          text={name ?? ''}
          appearance={JioTypography.BODY_XXS}
          color={'black'}
          style={{flexShrink: 1}}
        />
      ) : null}
    </View>
  );
};

export default React.memo(ReviewProduct);
const styles = StyleSheet.create({
  ProductTopContainer: {
    flexDirection: 'row',
    columnGap: rw(12),
    marginBottom: 16,
  },
  ProductImage: {
    width: rw(84),
    height: rw(84),
    borderRadius: 16,
    borderWidth: 0.8,
    borderColor: '#E0E0E0',
  },
});
