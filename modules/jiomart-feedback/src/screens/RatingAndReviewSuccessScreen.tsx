import React from 'react';
import {DeeplinkHandler} from '../../../jiomart-general/src/ui/JMScreenSlot';
import ScreenSlot from '../../../jiomart-general/src/ui/JMScreenSlot';
import type {JMRatingAndReviewSuccessScreenProps} from '../types/JMRatingAndReviewSuccessScreenType';
import useRatingAndReviewSuccessScreenController from '../controller/useRatingAndReviewSuccessScreenController';
import {Pressable, ScrollView, StyleSheet, View} from 'react-native';
import {JioIcon, JioText} from '@jio/rn_components';
import {
  IconColor,
  IconSize,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import {rh, rw} from '../../../jiomart-common/src/JMResponsive';
import ReviewProduct from '../components/ReviewProduct';
import StarRating from '../../../jiomart-general/src/ui/StarRating';
import CustomMediaRendered, {
  Kind,
} from '../../../jiomart-general/src/ui/CustomMediaRendered';

const RatingAndReviewSuccessScreen = (
  props: JMRatingAndReviewSuccessScreenProps,
) => {
  const {
    config,
    navigationBean,
    navigation,
    productName,
    productImage,
    review,
    insets,
    handleDoneClick,
  } = useRatingAndReviewSuccessScreenController(props);

  return (
    <DeeplinkHandler
      navigationBean={navigationBean}
      navigation={navigation}
      children={bean => {
        return (
          <ScreenSlot
            navigationBean={bean}
            navigation={navigation}
            statusBarColor={config?.statusBarColor}
            children={_ => {
              return (
                <>
                  <ScrollView
                    style={{flex: 1}}
                    contentContainerStyle={{flex: 1}}
                    showsVerticalScrollIndicator={false}
                    bounces={false}>
                    <View style={styles.successMsgContainer}>
                      <JioIcon
                        ic={'IcSuccess'}
                        color={IconColor.SUCCESS}
                        size={IconSize.XXL}
                      />
                      <JioText
                        text={config?.successMsg?.title}
                        appearance={JioTypography.BODY_L_BOLD}
                        color={'black'}
                      />
                      <JioText
                        text={config?.successMsg?.subTitle}
                        appearance={JioTypography.BODY_S}
                        color={'black'}
                        style={{marginTop: rh(12)}}
                      />
                    </View>

                    <View style={{marginHorizontal: 24}}>
                      <ReviewProduct
                        name={productName ?? ''}
                        image={productImage}
                      />
                      <JioText
                        text={config?.reviewDetails?.title}
                        appearance={JioTypography.HEADING_XXS}
                        color={'primary_grey_100'}
                        style={{marginBottom: 8}}
                      />
                      <StarRating rate={review?.data?.rating} />
                      <View style={styles.imageContainer}>
                        {review?.data?.images &&
                          review?.data?.images?.map(
                            (img: any, index: number) => {
                              return (
                                <CustomMediaRendered
                                  kind={Kind.IMAGE}
                                  mediaUrl={img?.url}
                                  customStyles={styles.image}
                                  height={rh(72)}
                                  width={rw(72)}
                                />
                              );
                            },
                          )}
                      </View>

                      <JioText
                        text={review?.data?.title}
                        appearance={JioTypography.BODY_XS_BOLD}
                        color={'primary_grey_100'}
                        style={{marginTop: rh(8)}}
                      />
                      <JioText
                        text={review?.data?.content}
                        appearance={JioTypography.BODY_XXS}
                        color={'primary_grey_80'}
                        style={{marginTop: rh(4)}}
                      />
                    </View>
                  </ScrollView>
                  <View
                    style={[
                      styles.doneBtnContainer,
                      {paddingBottom: insets.bottom + 12},
                    ]}>
                    <Pressable onPress={handleDoneClick} style={styles.doneBtn}>
                      <JioText
                        text={config?.doneBtn?.title}
                        appearance={JioTypography.BODY_S}
                        color={'white'}
                      />
                    </Pressable>
                  </View>
                </>
              );
            }}
          />
        );
      }}
    />
  );
};

export default RatingAndReviewSuccessScreen;

const styles = StyleSheet.create({
  productDetailsContainer: {
    flexDirection: 'row',
    gap: 12,
    marginTop: rh(24),
  },
  successMsgContainer: {
    padding: rw(24),
    backgroundColor: 'rgba(0, 178, 89, 0.1)',
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    marginBottom: 24,
  },
  imageContainer: {
    flexDirection: 'row',
    gap: 16,
    marginTop: rh(8),
    flexWrap: 'wrap',
  },
  imageBorder: {
    borderWidth: 0.8,
    borderColor: 'rgba(224, 224, 224, 1)',
    width: 84,
    height: 84,
    borderRadius: 16,
  },
  doneBtnContainer: {
    paddingVertical: rh(8),
    paddingHorizontal: rw(24),
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: 'rgba(224, 224, 224, 1)',
  },
  doneBtn: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 120, 173, 1)',
    borderRadius: 1000,
    paddingVertical: rh(12),
  },
  image: {
    width: 72,
    height: 72,
    borderRadius: 12,
  },
});
