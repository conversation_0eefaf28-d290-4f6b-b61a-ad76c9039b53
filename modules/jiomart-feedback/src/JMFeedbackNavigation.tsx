import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import type {NavigationStackData} from '../../jiomart-common/src/JMNavGraphUtil';
import {AppScreens} from '../../jiomart-common/src/JMAppScreenEntry';
import RatingAndReviewFormScreen from './screens/RatingAndReviewFormScreen';
import RatingAndReviewSuccessScreen from './screens/RatingAndReviewSuccessScreen';
import { Platform } from 'react-native';

const Stack = createNativeStackNavigator<NavigationStackData>();

const JMFeedbackNavigation = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: Platform.OS==="ios" ? false : true,
      }}>
      <Stack.Screen
        name={AppScreens.RATING_AND_REVIEW_FORM_SCREEN}
        component={RatingAndReviewFormScreen}
        options={{
          animation: 'default',
        }}
      />
      <Stack.Screen
        name={AppScreens.RATING_AND_REVIEW_SUCCESS_SCREEN}
        component={RatingAndReviewSuccessScreen}
        options={{
          animation: 'default',
        }}
      />
    </Stack.Navigator>
  );
};

export default JMFeedbackNavigation;
