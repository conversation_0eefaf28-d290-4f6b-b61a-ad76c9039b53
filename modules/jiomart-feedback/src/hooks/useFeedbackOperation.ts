import {useMutation} from '@tanstack/react-query';
import JMFeedbackNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMFeedbackNetworkController';
import {JMSharedViewModel} from '../../../jiomart-common/src/JMSharedViewModel';
import {AppSourceType} from '../../../jiomart-common/src/SourceType';

const feedbackController = new JMFeedbackNetworkController();

const useFeedbackOperation = () => {
  const saveRatingAndReviews = useMutation({
    mutationFn: feedbackController.submitReview,
    retry: 0,
  });
  const editRatingAndreviews = useMutation({
    mutationFn: feedbackController.updateReview,
    retry: 0,
  });

  const generateImageSignedUrl = useMutation({
    mutationFn: feedbackController.generateSignedUrl,
    retry: 0,
  });

  const uploadImage = useMutation({
    mutationFn: feedbackController.uploadImage,
    retry: 0,
  });

  const getRequestBodyRatingAndReview = useMutation({
    mutationFn: feedbackController.fetchRequestBodyRatingAndReview,
    retry: 0,
  });

  const generateSaveRatingAndReviewRequestBody = (data: any) => {
    const {
      productId,
      alternateCode1,
      alternateCode2,
      productURL,
      image,
      categoryL1,
      categoryL2,
      categoryL3,
      categoryL4,
      rating,
      ingressPoint,
      title = '',
      content = '',
    } = data;
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return {
          productId,
          alternateCode1,
          alternateCode2,
          productURL,
          images: image,
          categoryL1,
          categoryL2,
          categoryL3,
          categoryL4,
          rating,
          ingressPoint,
          title,
          content,
        };
      case AppSourceType.JM_BAU:
        return {
          productId,
          alternateCode1,
          alternateCode2,
          productURL,
          images: image,
          categoryL1,
          categoryL2,
          categoryL3,
          categoryL4,
          rating,
          ingressPoint,
          title,
          content,
        };
      default:
        return {
          productId,
          alternateCode1,
          alternateCode2,
          productURL,
          images: image,
          categoryL1,
          categoryL2,
          categoryL3,
          categoryL4,
          rating,
          ingressPoint,
          title,
          content,
        };
    }
  };
  const generateEditRatingAndReviewRequestBody = (data: any) => {
    const {
      productId,
      alternateCode1,
      alternateCode2,
      productURL,
      image,
      categoryL1,
      categoryL2,
      categoryL3,
      categoryL4,
      rating,
      ingressPoint,
      title = '',
      content = '',
      id,
    } = data;
    switch (JMSharedViewModel.Instance.appSource) {
      case AppSourceType.JM_JCP:
        return {
          productId,
          alternateCode1,
          alternateCode2,
          productURL,
          images: image,
          categoryL1,
          categoryL2,
          categoryL3,
          categoryL4,
          rating,
          ingressPoint,
          title,
          content,
          id,
        };
      case AppSourceType.JM_BAU:
        return {
          productId,
          alternateCode1,
          alternateCode2,
          productURL,
          images: image,
          categoryL1,
          categoryL2,
          categoryL3,
          categoryL4,
          rating,
          ingressPoint,
          title,
          content,
          id,
        };
      default:
        return {
          productId,
          alternateCode1,
          alternateCode2,
          productURL,
          images: image,
          categoryL1,
          categoryL2,
          categoryL3,
          categoryL4,
          rating,
          ingressPoint,
          title,
          content,
          id,
        };
    }
  };

  return {
    uploadImage,
    saveRatingAndReviews,
    editRatingAndreviews,
    generateImageSignedUrl,
    getRequestBodyRatingAndReview,
    generateSaveRatingAndReviewRequestBody,
    generateEditRatingAndReviewRequestBody,
  };
};

export default useFeedbackOperation;
