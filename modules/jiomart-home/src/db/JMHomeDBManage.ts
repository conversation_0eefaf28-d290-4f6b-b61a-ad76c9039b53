import {
  addStringPref,
  getPrefString,
  removeStringPref,
} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '../../../jiomart-common/src/JMConstants';
import {isNullOrUndefinedOrEmpty} from '../../../jiomart-common/src/JMObjectUtility';

export class JMHomeDBManager {
  static getCityName = async () => {
    return await getPrefString(AsyncStorageKeys.HOME_CITY_NAME);
  };
  static updateCityName = async (city: string) => {
    if (!isNullOrUndefinedOrEmpty(city)) {
      await addStringPref(AsyncStorageKeys.HOME_CITY_NAME, city);
    }
  };

  static deleteCityName = async () => {
    await removeStringPref(AsyncStorageKeys.HOME_CITY_NAME);
  };
}
