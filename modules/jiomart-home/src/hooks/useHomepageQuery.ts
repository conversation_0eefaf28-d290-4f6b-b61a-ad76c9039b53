// hooks/useHomepageQuery.ts
import {useInfiniteQuery} from '@tanstack/react-query';
import JMHomeNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMHomeNetworkController';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useEffect} from 'react';
import {getPersistString} from '../utils/HomeUtils';

const homeController = new JMHomeNetworkController();

export const homepageQueryKeys = {
  homepage: (slug: string, journey: number, pin: string, addStr: string) =>
    ['homepage', slug, journey, pin, addStr] as const,
};

export const useHomepageQuery = (
  slug: string,
  journey: number,
  headerJson: any,
  enabled: boolean = false,
) => {
  const pinString = JSON.stringify(headerJson);
  const pageSize = 10;

  const query = useInfiniteQuery({
    queryKey: homepageQueryKeys.homepage(
      slug,
      journey,
      pinString,
      getPersistString(slug),
    ),
    queryFn: async ({pageParam}) => {
      console.log('[ReactQuery] QueryFn called with pageParam:', pageParam);
      const queryParams = {
        slug,
        pageno: pageParam,
        journey,
        platform: 2,
        pagesize: pageSize,
      };
      const headers = {location_details: pinString};
      const result = await homeController.getHomepageData(queryParams, headers);
      console.log('[ReactQuery] API result:', result);
      return result;
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages) => {
      const totalCount = lastPage?.resultData?.totalCount || 0;
      const totalLoadedRecords = allPages.length * pageSize;
      const hasMoreData = totalLoadedRecords < totalCount;
      return hasMoreData ? allPages.length + 1 : undefined;
    },
    enabled,
    staleTime: 60 * 1000 * 3, // 2 minutes - balanced freshness for homepage
    gcTime: 10 * 60 * 1000, // 10 minutes - longer cache retention
    retry: 0,
  });

  useEffect(() => {
    if (query.isSuccess) {
      console.log('[ReactQuery] Query onSuccess, data:', query.data);
    }
  }, [query.isSuccess, query.data]);

  useEffect(() => {
    if (query.data) {
      console.log('[ReactQuery] Query data (from cache or API):', query.data);
    }
    if (query.isFetching) {
      console.log('[ReactQuery] Query is fetching (API call in progress)');
    }
  }, [query.data, query.isFetching]);

  const totalPagesLoaded = query.data?.pages?.length || 0;

  return {
    ...query,
    totalPagesLoaded,
  };
};
