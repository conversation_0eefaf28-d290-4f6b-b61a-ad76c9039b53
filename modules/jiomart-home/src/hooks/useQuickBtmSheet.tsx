import React, { useState } from 'react';

const useQuickBtmSheet = () => {
    const [showQuickBtmSheet, setShowQuickBtmSheet] = useState(false);
    const openQuickBtmSheet = () => {
       setShowQuickBtmSheet(true);
    }
    const closeQuickBtmSheet = () => {
       setShowQuickBtmSheet(false);
    }

    return {
        showQuickBtmSheet,
        openQuickBtmSheet,
        closeQuickBtmSheet
    }
}

export default useQuickBtmSheet;