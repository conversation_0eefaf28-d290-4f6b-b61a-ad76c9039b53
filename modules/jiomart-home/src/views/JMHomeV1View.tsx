import {StyleSheet, View} from 'react-native';
import React from 'react';
import JMImageBanner from '../components/JMImageBanner';

interface JMHomeV1ViewProps {
  items?: any[];
  onPress?: (item: any) => void;
}

const JMHomeV1View = (props: JMHomeV1ViewProps) => {
  const {items, onPress} = props;
  return (
    <View style={styles.container}>
      {items?.map(item => (
        <JMImageBanner
          key={item?.bannerUrl}
          bannerUrl={item?.bannerUrl}
          image={item?.image}
          description={item?.description}
          icon={item?.icon}
          position={item?.position}
          height={item?.height}
          color={item?.color}
          onPress={() => onPress?.(item)}
        />
      ))}
    </View>
  );
};

export default JMHomeV1View;

const styles = StyleSheet.create({
  container: {flex: 1, padding: 24, rowGap: 20}
});
