import {Pressable, StyleSheet, View} from 'react-native';
import React from 'react';
import {
  IconColor,
  JioTypography,
  type JioColor,
  type JioIconProps,
} from '@jio/rn_components/src/index.types';
import CustomMediaRendered, {
  Kind,
} from '../../../jiomart-general/src/ui/CustomMediaRendered';
import {rw} from '../../../jiomart-common/src/JMResponsive';
import {JioIcon, JioText, useColor} from '@jio/rn_components';
import type {IconKey} from '@jio/rn_components/src/utils/IconUtility';

interface JMImageBannerProps {
  bannerUrl: string;
  image?: string;
  icon?: JioIconProps;
  description?: string;
  height?: number;
  color?: JioColor;
  position?: {
    top?: number;
    bottom?: number;
    left?: number;
    right?: number;
  };
  onPress?: () => void;
}

const JMImageBanner = (props: JMImageBannerProps) => {
  const {
    bannerUrl,
    description,
    image,
    icon,
    height = 200,
    color = 'sparkle_70',
    onPress,
    position,
  } = props;
  const iconBackgroundColor = useColor(color);
  return (
    <Pressable style={{height: rw(height), borderRadius: 12}} onPress={onPress}>
      <CustomMediaRendered
        kind={Kind.IMAGE}
        mediaUrl={bannerUrl}
        imageResizeMode={'cover'}
        customStyles={{width: '100%', height: '100%'}}
      />
      <View style={[styles.overlap, {...position}]}>
        <View style={styles.wrapper}>
          <CustomMediaRendered
            kind={Kind.IMAGE}
            mediaUrl={image}

            customStyles={{height: 20}}
          />
          <View
            style={[
              {
                backgroundColor: iconBackgroundColor,
              },
              styles.iconBackground,
            ]}>
            <JioIcon
              color={IconColor.INVERSE}
              ic={icon?.ic as IconKey}
              {...icon}
            />
          </View>
        </View>
        <JioText
          text={description ?? ''}
          appearance={JioTypography.BODY_XS}
          color={color}
        />
      </View>
    </Pressable>
  );
};

export default JMImageBanner;

const styles = StyleSheet.create({
  overlap: {
    padding: 16,
    rowGap: 8,
    position: 'absolute',
    left: 0,
    right: 0,
  },
  iconBackground: {
    borderRadius: 100,
    paddingHorizontal: 8,
    alignSelf: 'flex-start',
  },
  wrapper: {flexDirection: 'row', justifyContent: 'space-between'},
});
