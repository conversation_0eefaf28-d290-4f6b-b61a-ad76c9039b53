import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import SlimBanner from '../../../jiomart-main/src/components/SlimBanner';

interface GenericSectionRendererProps {
  section: any;
}

export const GenericSectionRenderer: React.FC<GenericSectionRendererProps> = ({
  section,
}) => {
  const details = section.section_details;
  return (
    // <SlimBanner
    //   images={[
    //     'https://www.jiomart.com/images/cms/wysiwyg/common/onion_656x120.jpg?im=Resize=(720,160)',
    //   ]}
    // />
    <></>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#e3e3e3',
    borderRadius: 8,
    margin: 8,
  },
  title: {
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 4,
  },
  identifier: {
    fontSize: 12,
    color: '#666',
  },
});
