import {JioI<PERSON>, JioText} from '@jio/rn_components';
import {JioTypography} from '@jio/rn_components/src/index.types';
import React from 'react';
import {View, Image, Text, StyleSheet, Pressable} from 'react-native';
import CustomMediaRendered from '../../../jiomart-general/src/ui/CustomMediaRendered';
import {getActionUrl} from './WidgetWithProducts';

interface LimitedProductsSectionProps {
  headerBanner: string;
  headerText: string;
  titleUrl: string;
  children: React.ReactNode;
  backgroundColor: string;
  onClick: any;
}

const LimitedProductsSection: React.FC<LimitedProductsSectionProps> = ({
  headerBanner,
  headerText,
  titleUrl,
  children,
  backgroundColor,
  onClick,
}) => (
  <View style={[styles.container, {backgroundColor: backgroundColor}]}>
    <View style={styles.header}>
      {headerBanner ? (
        // <Image source={{uri: headerBanner}} style={styles.banner} />
        <CustomMediaRendered
          mediaUrl={headerBanner}
          customStyles={[styles.banner, {backgroundColor: backgroundColor}]}
        />
      ) : null}
      {headerText ? (
        <JioText
          style={styles.headerText}
          text={headerText}
          appearance={JioTypography.BODY_M_BOLD}
        />
      ) : null}
    </View>
    {children}
    <View style={{padding: titleUrl ? 0 : 10}}>
      {titleUrl && (
        <Pressable
          style={styles.footer}
          onPress={() => {
            onClick({
              actionType: 'T003',
              destination: 'CommonWebViewScreen',
              headerVisibility: 2,
              navigationType: 'push',
              loginRequired: false,
              actionUrl: getActionUrl({
                title_url: titleUrl,
                title: '',
              }),
              headerType: 9,
              shouldShowDeliverToBar: true,
            });
          }}>
          <JioText
            text="See all items"
            appearance={JioTypography.BODY_S_BOLD}
            color="primary_60"
          />
          <JioIcon ic="IcArrowNext" />
        </Pressable>
      )}
    </View>
  </View>
);

const styles = StyleSheet.create({
  container: {
    marginVertical: 12,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 20,
    padding: 20,
  },
  banner: {
    width: 60,
    height: 60,
    resizeMode: 'cover',
    marginLeft: 5,
  },
  headerText: {
    // fontWeight: '900',
  },
  footer: {
    marginTop: 12,
    alignItems: 'center',
    backgroundColor: '#DBE6EC',
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 4,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});

export default LimitedProductsSection;
