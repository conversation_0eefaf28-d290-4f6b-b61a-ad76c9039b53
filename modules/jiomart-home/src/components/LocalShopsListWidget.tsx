import React from 'react';
import {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>} from '@jio/rn_components';
import {
  ColorToken,
  IconColor,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import {FlatList, Pressable, StyleSheet, View, ScrollView} from 'react-native';
import {useHorizontalProductListController} from '../../../jiomart-home/src/controller/HorizontalProductListController';
import JioAds from '../../../jiomart-general/src/JioAds/JioAds';
import {shouldShowQuickLogo} from '../../../jiomart-home/src/utils/HomeSectionUtils';
import ItemCard, {
  ShimmerCard,
} from '../../../jiomart-main/src/components/ItemCard';
import {useLocalShopsListController} from '../controller/LocalShopsListController';
import {Filter<PERSON><PERSON><PERSON><PERSON>} from '../controller/LocalShopsListController';
import FastImage from 'react-native-fast-image';

interface HorizontalProductListWithBackgroundProps {
  title?: string;
  titleColor?: keyof ColorToken | undefined;
  banner?: string;
  backgroundColor: string;
  saleDateTime?: string;
  paddingTop?: number;
  widgetData: any;
}

const LocalShopsListWidget = (
  props: HorizontalProductListWithBackgroundProps,
) => {
  const {
    products,
    loading,
    homeConfig,
    handleLoadMore,
    categories,
    categoriesLoading,
    selectedCategoryId,
    handleCategorySelect,
    localShops,
    shopsLoading,
    shopsBottomLoading,
    shopsHasMore,
    handleLoadMoreShops,
  } = useLocalShopsListController(props.widgetData);

  const renderItem = ({item}: {item: any}) => {
    switch (item?.display_name) {
      case 'Jio_Ad_Product':
        const adsData = {
          adHeight: 180,
          adWidth: 360,
          adSpotId: item?.product_code,
          adType: 5,
          adjHeight: 90,
        };
        return <JioAds {...adsData} />;
      default:
        return (
          <ItemCard
            product={item}
            onClick={() => {}}
            disableWishlist={false}
            discount={`${item.discount_pct}% OFF`}
            discountBackgroundColor={'#E5F7EE'}
            discountColor={'sparkle_60'}
            disableAddToCart={item.in_stock}
            addedToCart={false}
            padding={5}
            showQcLogo={shouldShowQuickLogo(
              item,
              homeConfig?.showQuickLogoInProductCardDetails
                ?.verticleAndSellerList ?? [],
            )}
            qcLogoUrl={
              homeConfig?.showQuickLogoInProductCardDetails?.quicklogoUrl ?? ''
            }
          />
        );
    }
  };

  const renderShimmer = () => {
    return Array.from({length: 5}).map((_, index) => (
      <ShimmerCard key={`shimmer-${index}`} />
    ));
  };

  const renderCategory = ({item}: {item: FilterCategory}) => {

    const isSelected = item.id === selectedCategoryId;
    
    const buttonStyles = [
      styles.categoryButton,
      isSelected
        ? styles.selectedCategoryButton
        : styles.unselectedCategoryButton,
      item.type === 'icon_button' ? styles.sortButton : {},
    ];

    const textStyles = [
      styles.categoryButtonText,
      isSelected
        ? styles.selectedCategoryButtonText
        : styles.unselectedCategoryButtonText,
    ];

    return (
      <Pressable
        key={item.id}
        onPress={() => handleCategorySelect(item.id)}
        style={buttonStyles}>
        {item.icon && (
          <FastImage source={{uri: item.icon}} style={styles.iconImage} />
        )}
        <JioText text={item.name} style={textStyles} />
      </Pressable>
    );
  };

  const renderCategoryShimmer = () => {
    return Array.from({length: 4}).map((_, index) => (
      <View key={`cat-shimmer-${index}`} style={styles.categoryShimmer} />
    ));
  };

  return (
    <>
      {loading && products.length === 0 ? (
        <View
          style={[
            styles.container,
            {
              paddingTop: props.paddingTop,
              backgroundColor: props.backgroundColor,
            },
          ]}>
          <View style={styles.shimmerContainer}>{renderShimmer()}</View>
        </View>
      ) : (
        <View>
          {true && (
            <View
              style={[
                styles.container,
                {
                  paddingTop: props.paddingTop,
                  backgroundColor: props.backgroundColor,
                },
              ]}>
              <View style={styles.header}>
                <JioText
                  text={props.title}
                  color={props.titleColor}
                  style={styles.title}
                />
              </View>

              <JioText
                text={'22 stores near you'}
                style={styles.subTitle}
                color={'primary_grey_60'}
              />

              {categoriesLoading ? (
                <View style={styles.categoryShimmerContainer}>
                  {renderCategoryShimmer()}
                </View>
              ) : (
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  contentContainerStyle={styles.categoryListContent}>
                  {categories.map(category => renderCategory({item: category}))}
                </ScrollView>
              )}

              <FlatList
                data={localShops}
                keyExtractor={item => item.id}
                renderItem={({item}) => (
                  <View style={styles.shopRow}>
                    <FastImage source={{uri: item.icon}} style={styles.shopIcon} />
                    <View style={styles.shopInfo}>
                      <JioText text={item.name} style={styles.shopName} />
                      <JioText text={item.area} style={styles.shopArea} />
                      <JioText text={item.deliveryTime} style={styles.shopTime} />
                    </View>
                  </View>
                )}
                ListEmptyComponent={
                  shopsLoading ? (
                    <View style={{padding: 16}}>
                      {[...Array(5)].map((_, idx) => (
                        <View key={idx} style={styles.shopShimmerRow} />
                      ))}
                    </View>
                  ) : null
                }
                onEndReached={handleLoadMoreShops}
                onEndReachedThreshold={0.5}
                ListFooterComponent={
                  shopsBottomLoading ? (
                    <View style={styles.bottomLoader}>
                      <JioText text="Loading more..." />
                    </View>
                  ) : null
                }
              />
            </View>
          )}
        </View>
      )}
    </>
  );
};

export default LocalShopsListWidget;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
    paddingBottom: 10,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignContent: 'center',
  },
  title: {
    paddingLeft: 16,
    paddingBottom: 8,
    marginTop: 16,
    fontFamily: 'JioType',
    fontSize: 16,
    fontStyle: 'normal',
    fontWeight: 900,
    lineHeight: 20,
    letterSpacing: -0.48,
  },
  subTitle: {
    paddingLeft: 16,
    paddingBottom: 12,
    fontFamily: 'JioType',
    fontSize: 12,
    fontStyle: 'normal',
    fontWeight: 500,
    lineHeight: 16,
    letterSpacing: -0.48,
  },
  seeAll: {
    paddingHorizontal: 16,
    paddingVertical: 4,
    gap: 4,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  seeAllText: {
    fontWeight: '700',
  },
  flatListContent: {
    paddingLeft: 24,
    gap: 12,
  },
  loaderContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  shimmerContainer: {
    flexDirection: 'row',
    paddingLeft: 24,
    gap: 12,
  },
  categoryListContent: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    gap: 8,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    borderWidth: 1,
  },
  selectedCategoryButton: {
    backgroundColor: '#0078A0',
    borderColor: '#0078A0',
  },
  unselectedCategoryButton: {
    backgroundColor: 'white',
    borderColor: '#E0E0E0',
  },
  categoryButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  selectedCategoryButtonText: {
    color: 'white',
  },
  unselectedCategoryButtonText: {
    color: 'black',
  },
  sortButton: {
    gap: 4,
  },
  buttonIcon: {
    marginRight: 6,
    fontSize: 16,
  },
  iconImage: {
    width: 20,
    height: 20,
    marginRight: 6,
  },
  categoryShimmerContainer: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 16,
    gap: 8,
  },
  categoryShimmer: {
    width: 100,
    height: 36,
    borderRadius: 20,
    backgroundColor: '#E0E0E0',
  },
  shopRow: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  shopIcon: {
    width: 56,
    height: 56,
    borderRadius: 12,
    backgroundColor: '#F5F5F5',
    marginRight: 16,
  },
  shopInfo: {
    flex: 1,
  },
  shopName: {
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 2,
  },
  shopArea: {
    color: '#888',
    fontSize: 14,
    marginBottom: 2,
  },
  shopTime: {
    color: '#888',
    fontSize: 13,
  },
  shopShimmerRow: {
    height: 56,
    borderRadius: 12,
    backgroundColor: '#EEE',
    marginBottom: 16,
  },
  bottomLoader: {
    padding: 16,
    alignItems: 'center',
  },
});
