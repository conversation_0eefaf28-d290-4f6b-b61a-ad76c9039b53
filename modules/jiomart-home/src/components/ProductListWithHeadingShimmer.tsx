import React from 'react';
import {View, StyleSheet} from 'react-native';
import JioMartShimmer from '../../../jiomart-general/src/ui/JioMartShimmer';
import {ShimmerKind} from '@jio/rn_components/src/index.types';

// Single product card shimmer (reference: ShimmerCard)
const ProductCardShimmer = () => (
  <View style={styles.shimmerCard}>
    <JioMartShimmer
      width={160}
      height={160}
      kind={ShimmerKind.RECTANGLE}
      style={styles.shimmerImage}
    />
    <View style={styles.shimmerContent}>
      <JioMartShimmer
        width="90%"
        height={16}
        kind={ShimmerKind.PARAGRAPH}
        style={styles.shimmerTitle}
      />
      <JioMartShimmer
        width="60%"
        height={14}
        kind={ShimmerKind.PARAGRAPH}
        style={styles.shimmerPrice}
      />
      <JioMartShimmer
        width="40%"
        height={14}
        kind={ShimmerKind.PARAGRAPH}
        style={styles.shimmerDiscount}
      />
    </View>
  </View>
);

const ProductListWithHeadingShimmer = (props: any) => {
  // Number of product shimmers per row
  const productCount = 4;
  const renderProductRow = (keyPrefix: string) => (
    <View style={styles.productRow}>
      {Array.from({length: productCount}).map((_, idx) => (
        <ProductCardShimmer key={`${keyPrefix}-product-${idx}`} />
      ))}
    </View>
  );

  return (
    <View style={styles.container}>
      {Array.from({length: props?.rowCount ?? 2}).map((_, idx) => (
        <>
          <JioMartShimmer
            width="100%"
            height={40}
            kind={ShimmerKind.HEADING}
            style={styles.headingShimmer}
          />
          {renderProductRow(`row${idx + 1}`)}
        </>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#fff',
  },
  headingShimmer: {
    marginVertical: 16,
    borderRadius: 8,
  },
  productRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  shimmerCard: {
    width: 160,
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    marginRight: 12,
    paddingBottom: 8,
  },
  shimmerImage: {
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  shimmerContent: {
    padding: 8,
  },
  shimmerTitle: {
    marginBottom: 8,
    borderRadius: 4,
  },
  shimmerPrice: {
    marginBottom: 6,
    borderRadius: 4,
  },
  shimmerDiscount: {
    borderRadius: 4,
  },
});

export default ProductListWithHeadingShimmer;
