import React, {useCallback} from 'react';
import {View, FlatList, StyleSheet} from 'react-native';
import {WidgetWithProducts} from './WidgetWithProducts';
import ProductListWithHeadingShimmer from './ProductListWithHeadingShimmer';
import {useIsRestoring} from '@tanstack/react-query';
import useJMSpecialSectionRendererController from '../controller/JMSpecialSectionRendererController';
import {SPECIAL_IDENTIFIERS} from '../utils/HomeUtils';
import isEqual from 'lodash.isequal';

interface JMSpecialSectionRendererProps {
  section: any;
  onWidgetLoadingChange?: (loading: boolean) => void;
  onClick: any;
  parentHeight?: number; // Add this prop to control height
}

const WIDGET_PAGE_SIZE = 2;

function areEqual(
  prevProps: JMSpecialSectionRendererProps,
  nextProps: JMSpecialSectionRendererProps,
) {
  return (
    isEqual(prevProps.section, nextProps.section) &&
    prevProps.onClick === nextProps.onClick &&
    prevProps.onWidgetLoadingChange === nextProps.onWidgetLoadingChange
  );
}

const JMSpecialSectionRendererComponent: React.FC<
  JMSpecialSectionRendererProps
> = ({section, onClick, onWidgetLoadingChange, parentHeight}) => {
  const identifier =
    SPECIAL_IDENTIFIERS?.[
      section?.section_details?.identifier as keyof typeof SPECIAL_IDENTIFIERS
    ];

  const {
    widgets,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    handleEndReached,
    refetch,
  } = useJMSpecialSectionRendererController(
    section?.currentSelectedSlug,
    identifier,
    WIDGET_PAGE_SIZE,
    onWidgetLoadingChange,
  );

  console.log('re-rendering JM special section renderer', {
    widgetsCount: widgets.length,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
  });

  const handleEndReachedEnhanced = useCallback(() => {
    // Only fetch if we have next page and aren't already fetching
    if (hasNextPage && !isFetchingNextPage && !isLoading) {
      handleEndReached();
    }
  }, [hasNextPage, isFetchingNextPage, isLoading, handleEndReached]);

  // Memoize renderWidget with useCallback
  // const renderWidget = useCallback(
  //   ({item: widget, index}: {item: any; index: number}) => {
  //     return ['top-picks-for-milk', 'top-picks-for-vegetables'].includes(
  //       widget?.title_url,
  //     ) ? (
  //       <View style={styles.widgetWrapper}>
  //         <WidgetWithProducts
  //           widget={widget}
  //           onClick={onClick}
  //           slug={section?.currentSelectedSlug}
  //         />
  //       </View>
  //     ) : null;
  //   },
  //   [onClick, section?.currentSelectedSlug],
  // );

  const renderWidget = useCallback(
    ({item: widget, index}: {item: any; index: number}) => {
      return (
        <View style={styles.widgetWrapper}>
          <WidgetWithProducts
            widget={widget}
            onClick={onClick}
            slug={section?.currentSelectedSlug}
          />
        </View>
      );
    },
    [onClick, section?.currentSelectedSlug],
  );

  if (useIsRestoring()) {
    return <ProductListWithHeadingShimmer rowCount={1} />;
  }

  if (isLoading && widgets.length === 0) {
    return <ProductListWithHeadingShimmer rowCount={1} />;
  }

  return (
    <View style={[styles.sectionContainer]}>
      <FlatList
        data={widgets}
        renderItem={renderWidget}
        keyExtractor={(item, index) => `${item.title_url}_${index}`}
        onEndReachedThreshold={0.5} // Better threshold for smoother loading
        onEndReached={handleEndReachedEnhanced}
        nestedScrollEnabled={true}
        initialNumToRender={1}
        // removeClippedSubviews={true}
        ListFooterComponent={
          // Show footer loader when fetching next page OR when we have next page but no current loading
          isFetchingNextPage || (hasNextPage && !isLoading) ? (
            <View style={styles.footerLoader}>
              <ProductListWithHeadingShimmer rowCount={1} />
            </View>
          ) : null
        }
        ListEmptyComponent={
          isLoading && widgets.length === 0 ? (
            <ProductListWithHeadingShimmer rowCount={2} />
          ) : null
        }
      />
    </View>
  );
};

export const JMSpecialSectionRenderer = React.memo(
  JMSpecialSectionRendererComponent,
  areEqual,
);

const styles = StyleSheet.create({
  sectionContainer: {},
  widgetWrapper: {},
  footerLoader: {},
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  widgetContainer: {
    marginBottom: 24,
  },
  widgetTitle: {
    fontWeight: 'bold',
    fontSize: 18,
    marginBottom: 8,
    marginLeft: 8,
  },
  productCard: {
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 12,
    marginHorizontal: 8,
    minWidth: 160,
    alignItems: 'center',
  },
  productName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
    textAlign: 'center',
  },
  productPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2e7d32',
  },
  productDiscount: {
    fontSize: 12,
    color: '#388e3c',
    marginTop: 2,
  },
  container: {
    flex: 1,
    paddingTop: 24,
    backgroundColor: 'white',
    paddingBottom: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignContent: 'center',
  },
  title: {
    paddingLeft: 16,
    paddingBottom: 12,
    fontWeight: '900',
  },
  seeAll: {
    paddingHorizontal: 16,
  },
  seeAllText: {
    fontWeight: '700',
  },
  flatListContent: {
    paddingLeft: 24,
    gap: 12,
  },
  loaderContainer: {
    paddingHorizontal: 16,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  shimmerContainer: {
    flexDirection: 'row',
    paddingLeft: 24,
    gap: 12,
  },
});
