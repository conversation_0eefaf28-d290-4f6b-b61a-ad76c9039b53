import {addStringPref} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {getBaseURL} from '../../../jiomart-networkmanager/src/JMEnvironmentConfig';
import {
  DataResponse,
  MyListOffersProduct,
  ConfigInfo,
} from '../../../jiomart-networkmanager/src/models/home/<USER>';
import {getPrefString} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '../../../jiomart-common/src/JMConstants';

export function extractLastPathSegment(path: string): string {
  // Remove leading and trailing slashes, then split by '/'
  const segments = path.replace(/^\/+|\/+$/g, '').split('/');

  // Return the last segment
  return segments[segments.length - 1];
}

export const DefaultHomeSlug = 'home';

export enum TOP_CATERORIES_STATES {
  LOADING,
  SHOW_TOPBAR,
}

export enum SECTION_DETAILS_IDENTIFIER {
  MY_LIST_OFFERS = 'my_list_offers_ds2.0',
  LOCAL_SHOPS = "local_shops_ds2.0",
}

export function trimJiomartUrl(url: string): string {
  const prefixToTrim = `${getBaseURL()}/images/product/original/`;
  return url.replace(prefixToTrim, '');
}

/**
 * Inserts products at specific slot positions within an existing products array
 * @param currentProducts - The existing products array
 * @param newProducts - The new products to be inserted
 * @param slots - Comma-separated slot positions (e.g., "1,2,3,4,5")
 * @returns Updated products array with products inserted at specified slots
 */
export const insertProductsAtSlots = (
  currentProducts: MyListOffersProduct[] | null | undefined,
  newProducts: MyListOffersProduct[] | null | undefined,
  slots: string | null | undefined,
): MyListOffersProduct[] => {
  try {
    // Handle null/undefined currentProducts
    const safeCurrentProducts = currentProducts || [];

    // Handle null/undefined/empty newProducts
    if (!newProducts || newProducts.length === 0) {
      return safeCurrentProducts;
    }

    // Handle null/undefined/empty slots
    if (!slots || typeof slots !== 'string' || slots.trim() === '') {
      // If no valid slots, append to end
      return [...safeCurrentProducts, ...newProducts];
    }

    // Parse slot positions and convert from 1-indexed to 0-indexed
    const slotPositions = slots
      .split(',')
      .map(slot => parseInt(slot.trim(), 10))
      .filter(pos => !isNaN(pos) && pos > 0) // Filter out invalid positions (must be > 0 for 1-indexed)
      .map(pos => pos - 1); // Convert from 1-indexed to 0-indexed

    if (slotPositions.length === 0) {
      // If no valid slots, append to end
      return [...safeCurrentProducts, ...newProducts];
    }

    // Create result array with enough space for all products
    const totalLength = safeCurrentProducts.length + newProducts.length;
    const result: (MyListOffersProduct | null)[] = new Array(totalLength).fill(
      null,
    );

    // First, place new products at their specified slot positions
    slotPositions.forEach((slotPosition: number, index: number) => {
      if (index < newProducts.length && slotPosition < totalLength) {
        result[slotPosition] = newProducts[index];
      }
    });

    // Then, fill remaining positions with existing products
    let currentProductIndex = 0;
    for (let i = 0; i < totalLength; i++) {
      // If this position is empty (not occupied by a new product)
      if (result[i] === null) {
        // Fill with next available current product
        if (currentProductIndex < safeCurrentProducts.length) {
          result[i] = safeCurrentProducts[currentProductIndex];
          currentProductIndex++;
        }
      }
    }

    // Filter out any remaining null values and return
    return result.filter(
      (product): product is MyListOffersProduct => product !== null,
    );
  } catch (error) {
    console.error('Error in insertProductsAtSlots:', error);
    // Return safe current products if slot insertion fails
    const safeCurrentProducts = currentProducts || [];
    const safeNewProducts = newProducts || [];
    return [...safeCurrentProducts, ...safeNewProducts];
  }
};

export function getProductImageUrl(imagePath?: string): string | undefined {
  if (!imagePath) return undefined;
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }
  return `${getBaseURL()}/images/product/original/${imagePath}`;
}

export interface QuickLogoConfig {
  verticalCode: string;
  sellerIds: string;
}

// Function to check if Quick logo should be shown
export const shouldShowQuickLogo = (
  product: any,
  config: QuickLogoConfig[],
  serviceabilityData?: DataResponse | null,
  hcatConfigData?: Record<string, ConfigInfo> | null,
): boolean => {
  try {
    // Check if product is null/undefined or missing required fields
    if (
      !product ||
      !product.vertical_code ||
      !product.seller_id ||
      product.vertical_code.trim() === '' ||
      product.seller_id.toString().trim() === ''
    ) {
      return false;
    }

    // Check if config is null/undefined or missing required structure
    if (!config || !Array.isArray(config) || config.length === 0) {
      return false;
    }

    const productVerticalCode = product.vertical_code.trim();
    const productSellerId = product.seller_id.toString().trim();

    if (productVerticalCode === VerticleTypes.GROCERIES) {
      // Find matching vertical code in config
      const matchingConfig = config.find(configItem => {
        // Check if config item is valid
        if (
          !configItem ||
          !configItem.verticalCode ||
          !configItem.sellerIds ||
          configItem.verticalCode.trim() === '' ||
          configItem.sellerIds.trim() === ''
        ) {
          return false;
        }
        return configItem.verticalCode.trim() === productVerticalCode;
      });

      // If no matching vertical code found, return false
      if (!matchingConfig) {
        return false;
      }

      // Check if seller_id exists in the comma-separated list
      const sellerIdsList = matchingConfig.sellerIds
        .split(',')
        .map(id => id.trim())
        .filter(id => id !== ''); // Remove empty strings

      return sellerIdsList.includes(productSellerId);
    } else {
      // New logic for other verticals (ELECTRONICS, FASHION, etc.)
      try {

        // Step 1: Check if productVerticalCode exists in serviceabilityData
        if (!serviceabilityData || !serviceabilityData.promise_info || !serviceabilityData.promise_info.vertical) {
          return false;
        }

        const verticalData = serviceabilityData.promise_info.vertical[productVerticalCode];
        
        // If vertical doesn't exist in serviceability data, return false
        if (!verticalData) {
          return false;
        }

        // Step 2: Check if qc === 1 for that vertical
        if (verticalData.qc !== 1) {
          return false;
        }

        // Step 3: Check if vertical exists in hcatConfigData
        if (!hcatConfigData || !hcatConfigData[productVerticalCode]) {
          return false;
        }

        const verticalConfig = hcatConfigData[productVerticalCode];
        
        // Step 4: Check the "categories" field
        const categories = verticalConfig.categories;
        
        // If categories is null/undefined/empty, return true
        if (!categories || (typeof categories === 'string' && categories.trim() === '')) {
          return true;
        }

        // Step 5: Compare categories with product's category_level_id values
        if (!product.category_level_id || typeof product.category_level_id !== 'object') {
          console.error('shouldShowQuickLogo - category_level_id not found: ', product?.category_level_id);
          return false;
        }

        // Extract all category values from product's category_level_id
        const productCategoryIds: string[] = [];
        
        Object.keys(product.category_level_id).forEach(levelKey => {
          const levelArray = product.category_level_id[levelKey];
          if (Array.isArray(levelArray)) {
            levelArray.forEach(id => {
              if (id !== null && id !== undefined) {
                productCategoryIds.push(id.toString());
              }
            });
          }
        });

        // If no valid category IDs found in product, return false
        if (productCategoryIds.length === 0) {
          return false;
        }

        // Parse categories from hcat config (comma-separated string)
        const hcatCategoryIds = categories
          .split(',')
          .map((id: string) => id.trim())
          .filter((id: string) => id !== '');

        // If no valid categories in hcat config, return false
        if (hcatCategoryIds.length === 0) {
          return false;
        }

        // Check if any product category matches hcat categories
        const hasMatch = productCategoryIds.some(productCatId => 
          hcatCategoryIds.includes(productCatId)
        );

        return hasMatch;
        
      } catch (error) {
        console.warn('Error in shouldShowQuickLogo new logic:', error);
        return false;
      }
    }
  } catch (error) {
    // In case of any unexpected error, return false
    console.warn('Error in shouldShowQuickLogo:', error);
    return false;
  }
};

export enum VerticleTypes {
  GROCERIES = 'GROCERIES',
  ELECTRONICS = 'ELECTRONICS',
}

export enum TabSelectionType {
  ALREADY_SELECTED = 'already_selected',
  FIRST_POSITION = 'first_position',
  SPECIFIC_SLUG = 'specific_slug',
  OPEN_WEB_PAGE_SLUG = 'open_new_page_slug',
}

/**
 * Updates GROCERIES store configuration in headerJsonRef using dynamic store code from fyndPromiseDataRef
 *
 * Flow:
 * 1. Extract store_code from fynd promise wrapper api => region_info where vertical_code = "GROCERIES"
 * 2. Update region_code.GROCERIES array: replace all non-"PANINDIAGROCERIES" values with store_code
 *    we dont update PANINDIAGROCERIES because its 3P seller, we only update for 1P sellers.
 * 3. Update store_code.GROCERIES.1P array: replace with [store_code]
 */
export function updateHeaderJsonForMoonXHomeApi(
  headerJsonRef: React.MutableRefObject<{}>,
  fyndPromiseDataRef: React.MutableRefObject<DataResponse | null>,
): {} {
  try {
    // Step 1: Check if fyndPromiseDataRef has required data structure
    const fyndData = fyndPromiseDataRef.current;

    if (
      !fyndData ||
      !fyndData.region_info ||
      !Array.isArray(fyndData.region_info)
    ) {
      return headerJsonRef.current;
    }

    // Find GROCERIES entry in region_info
    const groceriesEntry = fyndData.region_info.find(
      entry => entry.vertical_code === 'GROCERIES',
    );

    if (!groceriesEntry || !groceriesEntry.store_code) {
      return headerJsonRef.current;
    }

    const storeCode = groceriesEntry.store_code;

    // Create a deep copy of headerJsonRef.current to avoid mutating the original
    const updatedHeaderJson: any = JSON.parse(
      JSON.stringify(headerJsonRef.current),
    );

    // Step 2: Update region_code -> GROCERIES values
    try {
      if (
        updatedHeaderJson.region_code &&
        updatedHeaderJson.region_code.GROCERIES
      ) {
        const groceriesRegionCodes = updatedHeaderJson.region_code.GROCERIES;

        // Replace all values that are not 3P : "PANINDIAGROCERIES" with store_code
        updatedHeaderJson.region_code.GROCERIES = groceriesRegionCodes.map(
          (code: string) => (code === 'PANINDIAGROCERIES' ? code : storeCode),
        );
      }
    } catch (error) {
      // If step 2 fails, continue to step 3 without affecting the operation
      console.warn('Step 2 failed:', error);
    }

    // Step 3: Update store_code -> GROCERIES -> 1P values
    try {
      if (
        updatedHeaderJson.store_code &&
        updatedHeaderJson.store_code.GROCERIES &&
        updatedHeaderJson.store_code.GROCERIES['1P']
      ) {
        // Replace the entire 1P array with the store_code
        updatedHeaderJson.store_code.GROCERIES['1P'] = [storeCode];
      }
    } catch (error) {
      // If step 3 fails, log the error but don't affect the overall operation
      console.warn('Step 3 failed:', error);
    }

    return updatedHeaderJson;
  } catch (error) {
    // Step 5: If any unexpected error occurs, return original headerJsonRef
    console.error('Error in updateHeaderJson:', error);
    return headerJsonRef.current;
  }
}

const REFRESH_INTERVAL_IN_MIN_DEFAULT = 60; // Configurable refresh interval in minutes

/**
 * Parses flash_slot string and returns array of slot numbers
 * @param flashSlot - String like "7,9,11" or "7"
 * @returns Array of numbers
 */
export const parseFlashSlots = (flashSlot: string): number[] => {
  if (!flashSlot || flashSlot.trim() === '') {
    return [];
  }

  return flashSlot
    .split(',')
    .map(slot => parseInt(slot.trim(), 10))
    .filter(slot => !isNaN(slot));
};

/**
 * Calculates current cycle index based on configurable interval rotation
 * @param totalProducts - Total available flash products
 * @param slotsNeeded - Number of slots needed
 * @param refreshIntervalInMin - Refresh interval in minutes (optional, defaults to REFRESH_INTERVAL_IN_MIN)
 * @returns Current cycle index
 */
const calculateCurrentCycleIndex = async (
  totalProducts: number,
  slotsNeeded: number,
  refreshIntervalInMin: number,
): Promise<number> => {
  try {
    const currentTime = Date.now();
    const cachedData = await getPrefString(
      AsyncStorageKeys.FLASH_PRODUCTS_DATA_KEY,
    );

    // If no cached data exists, initialize
    if (!cachedData) {
      const initialData = {
        timestamp: currentTime,
        cycleIndex: 0,
      };
      await addStringPref(
        AsyncStorageKeys.FLASH_PRODUCTS_DATA_KEY,
        JSON.stringify(initialData),
      );
      return 0;
    }

    const parsedData = JSON.parse(cachedData);
    const lastTimestamp = parsedData.timestamp || 0;
    const lastCycleIndex = parsedData.cycleIndex || 0;

    // Calculate minutes passed since last update
    const minutesPassedSinceLastUpdate = Math.floor(
      (currentTime - lastTimestamp) / (1000 * 60),
    );

    if (minutesPassedSinceLastUpdate >= refreshIntervalInMin) {
      // Calculate how many complete cycles are possible
      const maxCycles = Math.ceil(totalProducts / slotsNeeded);

      // Calculate how many refresh cycles have passed
      const refreshCyclesPassed = Math.floor(
        minutesPassedSinceLastUpdate / refreshIntervalInMin,
      );

      // Calculate new cycle index
      const newCycleIndex = (lastCycleIndex + refreshCyclesPassed) % maxCycles;

      // Update cached values
      const updatedData = {
        timestamp: currentTime,
        cycleIndex: newCycleIndex,
      };
      await addStringPref(
        AsyncStorageKeys.FLASH_PRODUCTS_DATA_KEY,
        JSON.stringify(updatedData),
      );

      return newCycleIndex;
    }

    // If less than refresh interval has passed, return cached cycle index
    return lastCycleIndex;
  } catch (error) {
    console.error('Error calculating cycle index:', error);
    // Return 0 as fallback
    return 0;
  }
};

/**
 * Selects products for current interval based on cycle index
 * @param flashProducts - All available flash products
 * @param slotsNeeded - Number of products needed
 * @param refreshIntervalInMin - Refresh interval in minutes (optional, defaults to REFRESH_INTERVAL_IN_MIN)
 * @returns Selected products for current interval
 */
export const selectProductsForCurrentHour = async (
  flashProducts: any[],
  slotsNeeded: number,
  refreshIntervalInMin: number = REFRESH_INTERVAL_IN_MIN_DEFAULT,
): Promise<any[]> => {
  try {
    if (!flashProducts || flashProducts.length === 0) {
      return [];
    }

    // If we need more products than available, return all available
    if (slotsNeeded >= flashProducts.length) {
      return flashProducts;
    }

    const cycleIndex = await calculateCurrentCycleIndex(
      flashProducts.length,
      slotsNeeded,
      refreshIntervalInMin,
    );
    const startIndex = cycleIndex * slotsNeeded;

    // If start index exceeds array length, reset to beginning
    if (startIndex >= flashProducts.length) {
      // Reset cycle and start from beginning
      const resetData = {
        timestamp: Date.now(),
        cycleIndex: 0,
      };
      await addStringPref(
        AsyncStorageKeys.FLASH_PRODUCTS_DATA_KEY,
        JSON.stringify(resetData),
      );
      return flashProducts.slice(0, slotsNeeded);
    }

    // Get products for current cycle
    const endIndex = Math.min(startIndex + slotsNeeded, flashProducts.length);
    const selectedProducts = flashProducts.slice(startIndex, endIndex);

    // If we don't have enough products in current cycle, fill from beginning
    if (selectedProducts.length < slotsNeeded) {
      const remainingCount = slotsNeeded - selectedProducts.length;
      const additionalProducts = flashProducts.slice(0, remainingCount);
      selectedProducts.push(...additionalProducts);
    }

    return selectedProducts;
  } catch (error) {
    console.error('Error selecting products for current interval:', error);
    // Return first products as fallback
    return flashProducts.slice(0, slotsNeeded);
  }
};
