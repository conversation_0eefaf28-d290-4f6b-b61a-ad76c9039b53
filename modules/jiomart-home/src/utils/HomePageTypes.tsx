// Banner details interface
export interface BannerDetails {
  id: number;
  name: string;
  pause_time_between_transitions: number;
  slide_transition_speed: number;
  is_stop_animation_mouse_on_banner: boolean;
  display_arrows: boolean;
  display_bullets: boolean;
}

// Slider details interface
export interface SliderDetails {
  id: number;
  name: string;
  img_type: number;
  img_file: string;
  img_url: string;
  img_alt: string;
  img_title: string;
  url: string;
  is_open_url_in_new_window: number;
  is_add_nofollow_to_url: number;
  vertical: string;
  slide_type: number;
  slide_type_name: string;
  video_file: string | null;
  region_id: string;
  position: number;
  pause_time_between_transitions: number;
  image: string;
}

// Section details interface
export interface SectionDetails {
  section_id: number;
  section_name: string;
  section_title: string;
  section_heading: string;
  section_tagline: string;
  value: string;
  section_bg_image: string | null;
  section_bg_color: string;
  viewall_link: string;
  position: number;
  identifier: string;
  version: string;
  discount: string;
  section_text_color: string;
  banner_image: string | null;
  is_custom_image: boolean;
  hide_name: boolean;
  section_vertical: string;
  section_type_name: string;
  header_image: string | null;
  design_type: string;
  app_templateId: string;
}

// Data interface for banner/slider sections
export interface BannerSliderData {
  banner_details: BannerDetails;
  slider_details: SliderDetails[];
}

// Data interface for product sections
export interface ProductsData {
  products: string[];
}

// Union type for data field
export type SectionData = BannerSliderData | ProductsData;

// Main response item interface
export interface HomePageSection {
  data: SectionData;
  section_details: SectionDetails;
}

// Array type for the full response
export type HomePageSectionsResponse = HomePageSection[];
