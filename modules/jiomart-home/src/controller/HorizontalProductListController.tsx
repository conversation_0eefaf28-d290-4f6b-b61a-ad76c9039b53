import {useState, useMemo, useEffect, useRef} from 'react';
import {useConfigFile} from '../../../jiomart-general/src/hooks/useJMConfig';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {
  MyListOffersProduct,
  MyListOffersWidgetRenderParsedDataMap,
  DataResponse,
  ConfigInfo,
} from '../../../jiomart-networkmanager/src/models/home/<USER>';
import {
  insertProductsAtSlots,
  parseFlashSlots,
  selectProductsForCurrentHour,
} from '../../../jiomart-home/src/utils/HomeSectionUtils';
import {useAllProductQueries} from '../hooks/useProductQueries';
import JMServiceablityNetworkNewController from '../../../jiomart-networkmanager/src/JMNetworkController/JMServiceablityNetworkNewController';

const servicibilityControllerNew = new JMServiceablityNetworkNewController();

export interface HorizontalProductListControllerState {
  products: MyListOffersProduct[];
  loading: boolean;
  widgetDataSourceDetails: MyListOffersWidgetRenderParsedDataMap | undefined;
  homeConfig: any;
  serviceabilityData: DataResponse | null;
  hcatConfigData: Record<string, ConfigInfo> | null;
  isFetching: boolean;
  hasError: boolean;
}

/**
 * Enhanced horizontal product list controller with TanStack Query integration
 * Features:
 * - Session-based caching with dynamic query keys based on currentSelectedSlug and selectedFlow
 * - Consolidated product loading from multiple sources
 * - Optimized flash deal slot insertion
 * - Background refresh with stale-while-revalidate behavior
 */
export const useHorizontalProductListController = (
  widgetData: any,
): HorizontalProductListControllerState => {
  const homeConfig = useConfigFile(
    JMConfigFileName.JMHomeDashboardConfigurationFileName,
  );
  const serviceabilityData = useRef<DataResponse | null>(null);
  const hcatConfigData = useRef<Record<string, ConfigInfo> | null>(null);

  console.log(
    'useHorizontalProductListController data : ',
    JSON.stringify(widgetData, null, 2),
  );

  // Extract currentSelectedSlug and selectedFlow from widgetData for dynamic caching
  const currentSelectedSlug = widgetData?.currentSelectedSlug || 'default';
  const selectedFlow = widgetData?.selectedFlow || 'default';

  // Parse widget configuration
  const parsedWidgetData =
    useMemo((): MyListOffersWidgetRenderParsedDataMap => {
      try {
        if (!widgetData?.section_details?.value) return {};

        const parsedArray: any[] = JSON.parse(widgetData.section_details.value);
        const hashMap: MyListOffersWidgetRenderParsedDataMap = {};
        parsedArray.forEach(item => {
          Object.assign(hashMap, item);
        });
        return hashMap;
      } catch (error) {
        console.error('Error parsing widget data to HashMap:', error);
        return {};
      }
    }, [widgetData?.section_details?.value]);

  console.log('Parsed Widget Data:', parsedWidgetData);
  console.log('Cache Key Components:', {currentSelectedSlug, selectedFlow});

  // Use consolidated product queries with session-based caching
  const {
    buyAgainProducts,
    recommendedProducts,
    flashDealsProducts,
    topDealsProducts,
    cmsProducts,
    consolidatedProducts,
    isLoading,
    isFetching,
    hasError,
    queries,
  } = useAllProductQueries(
    currentSelectedSlug,
    selectedFlow,
    parsedWidgetData,
    true, // enabled
  );

  // Process flash deals with slot insertion logic
  const processedFlashDeals = useMemo(() => {
    if (!parsedWidgetData?.flashdeals || !flashDealsProducts?.length) {
      return [];
    }

    try {
      const flashSlots = parseFlashSlots(parsedWidgetData?.flash_slot || '');
      const slotsNeeded = flashSlots?.length ?? 0;

      if (slotsNeeded > 0) {
        const selectedFlashProducts = selectProductsForCurrentHour(
          flashDealsProducts,
          slotsNeeded,
          homeConfig?.homeConfig?.myListFlashProductsIntervalInMin,
        );

        // Ensure selectedFlashProducts is properly resolved
        const resolvedProducts = Array.isArray(selectedFlashProducts)
          ? selectedFlashProducts
          : [];
        console.log(
          'Flash Deals - Slots:',
          flashSlots,
          'Products:',
          resolvedProducts.length,
        );
        return resolvedProducts;
      }
    } catch (error) {
      console.error('Error processing flash deals:', error);
    }

    return [];
  }, [
    flashDealsProducts,
    parsedWidgetData?.flashdeals,
    parsedWidgetData?.flash_slot,
    homeConfig,
  ]);

  // Generate JioAds products (No API call needed) So no need to use tenStackQuery.
  const processedJioAds = useMemo((): MyListOffersProduct[] => {
    if (!parsedWidgetData?.jioads || parsedWidgetData.jioads === '') {
      return [];
    }

    try {
      const jioAdsArray: string[] = parsedWidgetData.jioads
        .split(',')
        .map(ads => ads.trim());

      const adsProducts: MyListOffersProduct[] = [];

      jioAdsArray.forEach((ads: string, index: number) => {
        // Create hardcoded product data for each ads item
        const hardcodedProduct: MyListOffersProduct = {
          product_code: `${ads}`,
          display_name: `Jio_Ad_Product`,
          selling_price: 999 + index * 100, // Varying prices
          mrp: 1299 + index * 100,
          discount_pct: Math.floor(Math.random() * 30) + 10, // Random discount 10-40%
          image_path: `https://example.com/images/jio-ad-${ads}-${index}.jpg`,
          url_path: `/products/jio-ad-${ads}-${index}`,
          vertical_code: 'JIO_ADS',
          seller_id: `SELLER_${ads}`,
          min_qty_in_order: 1,
          max_qty_in_order: 5,
        };

        adsProducts.push(hardcodedProduct);
      });

      console.log('JioAds Products Generated:', adsProducts.length);
      return adsProducts;
    } catch (error) {
      console.error('Error processing JioAds:', error);
      return [];
    }
  }, [parsedWidgetData?.jioads]);

  // Consolidate all products with proper slot insertion
  const finalProducts = useMemo(() => {
    let allProducts = [...consolidatedProducts];

    // Insert JioAds at specified slots first (if available)
    // if (processedJioAds.length > 0) {
    //   allProducts = insertProductsAtSlots(
    //     allProducts,
    //     processedJioAds,
    //     parsedWidgetData?.slot,
    //   );
    //   console.log('JioAds inserted at slots:', parsedWidgetData?.slot);
    // }

    // Insert flash deals at specified slots
    if (processedFlashDeals.length > 0) {
      allProducts = insertProductsAtSlots(
        allProducts,
        processedFlashDeals,
        parsedWidgetData?.flash_slot,
      );
      console.log('Flash Deals inserted at slots:', parsedWidgetData?.flash_slot);
    }

    console.log('Final consolidated products count:', allProducts.length);
    console.log('Product sources included:', {
      consolidatedProducts: consolidatedProducts.length,
      jioAds: processedJioAds.length,
      flashDeals: processedFlashDeals.length,
      totalFinal: allProducts.length,
    });
    
    return allProducts;
  }, [
    consolidatedProducts, 
    processedJioAds, 
    processedFlashDeals, 
    parsedWidgetData?.slot, 
    parsedWidgetData?.flash_slot
  ]);

  const fetchServiceabilityAndHcatConfigData = async () => {
    serviceabilityData.current =
      await servicibilityControllerNew.getCachedServiceabilityData();
    hcatConfigData.current =
      await servicibilityControllerNew.getCachedHcatConfigData();
  };

  // Load initial serviceability data
  useEffect(() => {
    fetchServiceabilityAndHcatConfigData();
  }, []);

  // Log query states for debugging
  useEffect(() => {
    console.log('Query States:', {
      buyAgain: {
        isLoading: queries.buyAgain.isLoading,
        dataLength: buyAgainProducts.length,
      },
      recommended: {
        isLoading: queries.recommended.isLoading,
        dataLength: recommendedProducts.length,
      },
      flashDeals: {
        isLoading: queries.flashDeals.isLoading,
        dataLength: flashDealsProducts.length,
      },
      topDeals: {
        isLoading: queries.topDeals.isLoading,
        dataLength: topDealsProducts.length,
      },
      cmsProducts: {
        isLoading: queries.cmsProducts.isLoading,
        dataLength: cmsProducts.length,
      },
      jioAds: {
        isLoading: false, // JioAds are generated synchronously
        dataLength: processedJioAds.length,
        enabled: Boolean(parsedWidgetData?.jioads),
      },
    });
  }, [
    queries,
    buyAgainProducts,
    recommendedProducts,
    flashDealsProducts,
    topDealsProducts,
    cmsProducts,
    processedJioAds,
    parsedWidgetData?.jioads,
  ]);

  return {
    // Products with consolidated single update
    products: finalProducts,
    loading: isLoading,

    // Widget configuration
    widgetDataSourceDetails: parsedWidgetData,
    homeConfig,

    // Additional data
    serviceabilityData: serviceabilityData.current,
    hcatConfigData: hcatConfigData.current,

    // Enhanced states
    isFetching,
    hasError,
  };
};
