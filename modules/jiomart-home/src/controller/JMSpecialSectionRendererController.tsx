import {useInfiniteQuery, useQueryClient} from '@tanstack/react-query';
import {getTopDealsWidgets} from '../utils/jmDashboardUtils';
import {useEffect, useState} from 'react';
import {getPersistString} from '../utils/HomeUtils';

const useJMSpecialSectionRendererController = (
  slug: string,
  identifier: string,
  pageSize: any,
  onWidgetLoadingChange: any,
) => {
  const [isAppRestarted, setIsAppRestarted] = useState(false);
  const queryClient = useQueryClient();

  // React Query infinite query for widgets
  const isFirstTab = getPersistString(slug) === 'persist';
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isFetching,
    status,
    refetch,
  } = useInfiniteQuery<{widgets: any[]; nextPage?: number}, Error>({
    queryKey: ['widgets', identifier, getPersistString(slug)],
    queryFn: async ({pageParam}) => {
      const page = typeof pageParam === 'number' ? pageParam : 1;
      // Dynamic page size: 2 for first page, 4 for subsequent pages
      const dynamicPageSize = page === 1 ? pageSize : 4;
      console.log(
        `[WidgetQuery] ${identifier} - Page: ${page}, PageSize: ${dynamicPageSize}`,
      );

      const widgets = await getTopDealsWidgets(
        page,
        dynamicPageSize,
        identifier,
      );
      return {
        widgets,
        nextPage: widgets.length === dynamicPageSize ? page + 1 : undefined,
      };
    },
    getNextPageParam: (lastPage: {widgets: any[]; nextPage?: number}) =>
      lastPage.nextPage,
    initialPageParam: 1,
    enabled: !!identifier,
    staleTime: 1000 * 60 * 0, // 5 minutes - much longer stale time for widgets to prevent re-fetching
    gcTime: 1000 * 60 * 10, // 15 minutes - keep widget data much longer
    retry: 0,
    // placeholderData: (previousData, previousQuery) => previousData, // Enable placeholder data for instant loading
  });

  // Flatten widgets from all pages
  const widgets = data?.pages?.flatMap(page => page.widgets) || [];

  // Debug logging to track widget count and page behavior
  console.log(
    `[WidgetQuery] ${identifier} - Total widgets: ${
      widgets.length
    }, Pages loaded: ${data?.pages?.length || 0}, HasNextPage: ${hasNextPage}`,
  );

  // Notify parent about widget loading (has more widgets)
  useEffect(() => {
    if (onWidgetLoadingChange) {
      onWidgetLoadingChange(!!hasNextPage);
    }
  }, [hasNextPage, onWidgetLoadingChange]);

  const handleEndReached = () => {
    console.log(
      `[WidgetQuery] ${identifier} - End reached. HasNextPage: ${hasNextPage}, IsFetching: ${isFetchingNextPage}, Current widgets: ${widgets.length}`,
    );
    if (hasNextPage && !isFetchingNextPage) {
      console.log(`[WidgetQuery] ${identifier} - Fetching next page...`);
      fetchNextPage();
    }
  };

  return {
    widgets,
    isLoading,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
    handleEndReached,
    refetch,
  };
};

export default useJMSpecialSectionRendererController;
