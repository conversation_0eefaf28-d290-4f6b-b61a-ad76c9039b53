import React, {useState} from 'react';
import {useInfiniteQuery, useQueryClient} from '@tanstack/react-query';
import JMCartNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMCartNetworkController';
import SearchService from '../../../jiomart-networkmanager/src/JMNetworkController/JMSearchAPINetworkController';
import {getWidgetProducts} from '../utils/jmDashboardUtils';
import useRecommendedProductsViewModel from '../../../jiomart-search/src/viewmodel/useRecommededProductsViewModel';
import {getPersistString} from '../utils/HomeUtils';
import {useConfigFile} from '../../../jiomart-general/src/hooks/useJMConfig';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import JMHomeNetworkController from '../../../jiomart-networkmanager/src/JMNetworkController/JMHomeNetworkController';
import JMServiceablityNetworkNewController from '../../../jiomart-networkmanager/src/JMNetworkController/JMServiceablityNetworkNewController';
import { useEffect, useRef } from 'react';
import { ConfigInfo, DataResponse } from '../../../jiomart-networkmanager/src/models/home/<USER>';

const PRODUCT_PAGE_SIZE = 4;

export const mapCartLineToProduct = (line: any) => {
  return {
    product_code: line.product_code,
    in_stock: line.availability_status === 'A' && !line.out_of_stock ? 1 : 0,
    brand: line.product_json_ext?.brand || '',
    url_path: line.url_path?.startsWith('/')
      ? line.url_path
      : `/${line.url_path}`,
    image_url: line.product_image_path
      ? `images/product/150x150/${line.product_image_path}`
      : '',
    display_name: line.display_name,
    brand_id: line.product_json_ext?.brand_id || '',
    availability_status: line.availability_status,
    image_path: line.product_image_path,
    category_level: line.product_json_ext?.category_level || {},
    seller_ids: [String(line.seller_id)],
    food_type: line.product_json_ext?.food_type,
    alternate_product_code:
      line.product_json_ext?.alternate_product_code ||
      String(line.product_code),
    available_at_3p_seller:
      line.product_json_ext?.available_at_3p_seller || false,
    seller_names: line.product_json_ext?.seller_name
      ? [line.product_json_ext.seller_name]
      : [],
    category_level_id: line.product_json_ext?.category_level_id || {},
    vertical_code: line.vertical_code,
    objectID: String(line.product_code),
    mrp: line.mrp,
    selling_price: line.selling_price,
    discount_pct:
      line.mrp && line.selling_price
        ? ((line.mrp - line.selling_price) / line.mrp) * 100
        : 0,
    seller_id: String(line.seller_id),
    min_qty_in_order: line.min_qty_in_order,
    max_qty_in_order: line.max_qty_in_order,
  };
};

export const mapAlgoliaResponse = (prod: any) => {
  if (!prod) return null;
  return {
    product_code: prod.uid || prod.item_code,
    in_stock: prod.sellable ? 1 : 0,
    brand: prod.brand || '',
    url_path: prod.url || '',
    image_path: prod.medias?.[0]?.url || '',
    display_name: prod.name || '',
    brand_id: prod.brand_id || '',
    availability_status: prod.availability_status,
    category_level: prod.category_level || {},
    seller_ids: prod.seller_id ? [String(prod.seller_id)] : [],
    food_type: prod.food_type || '',
    alternate_product_code:
      prod.alternate_product_code || String(prod.uid || prod.item_code),
    available_at_3p_seller: prod.available_at_3p_seller || false,
    seller_names: prod.seller_name ? [prod.seller_name] : [],
    category_level_id: prod.category_level_id || {},
    vertical_code: prod.attributes?.['vertical-code'] || '',
    objectID: String(prod.uid || prod.item_code),
    mrp: prod.price?.marked?.max || 0,
    selling_price: prod.price?.effective?.max || 0,
    discount_pct: prod.discount_pct ? prod.discount_pct : prod.discount,
    seller_id: prod.seller_id ? String(prod.seller_id) : '',
    min_qty_in_order: prod.min_qty_in_order || 1,
    max_qty_in_order: prod.max_qty_in_order || 1,
  };
};

const homeControllerInstance = new JMHomeNetworkController();

const serviciablityControllerNew = new JMServiceablityNetworkNewController();

const useJMWidgetsWithProductsController = (
  widget: any,
  slug: string,
  onSuccess?: any,
) => {
  const [isEmptyProducts, setIsEmptyProducts] = useState(false);
  const [isAppRestarted, setIsAppRestarted] = useState(false);
  const queryClient = useQueryClient();


  const serviceabilityData = useRef<DataResponse | null>(null);
  const hcatConfigData = useRef<Record<string, ConfigInfo> | null>(null);

  const {getRecommendedItems} = useRecommendedProductsViewModel({
    is_buy_again_api_enabled: true,
  });

  const config = useConfigFile(
    JMConfigFileName.JMHomeDashboardConfigurationFileName,
  );

  // Helper for serviceability fetch
  const getProductsServiceabilityItems = async (items_codes: Array<string>) => {
    try {
      const body = items_codes;
      return await homeControllerInstance
        .fetchProductDetailsFromAlgolia(body)
        .then(recommendedItemsData => recommendedItemsData);
    } catch (error) {
      console.error('getProductsServiceabilityItems API Failed', error);
      return {items: []};
    }
  };

  // Infinite query for products
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    refetch,
  } = useInfiniteQuery<
    {products: any[]; total_sku: number; nextPage?: number; maxPages?: number},
    Error
  >({
    queryKey: [
      'widget-products',
      widget?.title_url,
      widget?.title_type,
      widget?.title,
      getPersistString(slug),
    ],
    queryFn: async ({pageParam = 1}) => {
      const currentPage = typeof pageParam === 'number' ? pageParam : 1;
      console.log('pageParam --->', currentPage);

      // Cart items (no pagination)
      if (widget?.title_type === 'cart_items') {
        const cartController = new JMCartNetworkController();
        const items = await cartController.fetchCart(false);
        const cartLines = items.result.cart.lines;
        const products = cartLines.map(mapCartLineToProduct);
        return {
          products,
          total_sku: products.length,
          nextPage: undefined,
          maxPages: 1,
        };
      }

      // products_ds2.0 or limited_products_ds2.0 (fetch by codes, no pagination)
      if (
        ['products_ds2.0', 'limited_products_ds2.0'].includes(
          widget?.title_type,
        )
      ) {
        const allCodes = widget?.product_codes || [];
        const response = await getProductsServiceabilityItems(allCodes);
        const mappedProducts = response?.items.map(mapAlgoliaResponse) || [];
        if (onSuccess && typeof onSuccess === 'function') onSuccess(true);
        return {
          products: mappedProducts,
          total_sku: mappedProducts.length,
          nextPage: undefined,
          maxPages: 1,
        };
      }

      // buy_again_products_ds2.0 (no pagination)
      if (widget?.title_type === 'buy_again_products_ds2.0') {
        const response = await getRecommendedItems();
        const mappedProducts = response?.items.map(mapAlgoliaResponse) || [];
        return {
          products: mappedProducts,
          total_sku: mappedProducts.length,
          nextPage: undefined,
          maxPages: 1,
        };
      }

      // Default: paginated fetch with error handling
      try {
        const response = await getWidgetProducts(
          widget.title_url,
          currentPage,
          PRODUCT_PAGE_SIZE,
        );
        // response.products: array, response.total_sku: total count
        const products = response.products || [];
        const totalSku = response.total_sku || products.length;

        setIsEmptyProducts(products.length === 0);

        // Calculate maximum pages based on total SKUs and page size
        const maxPages = Math.ceil(totalSku / PRODUCT_PAGE_SIZE);

        console.log('🔧 QueryFn Debug - widget.title_url:', widget.title_url);
        console.log('🔧 QueryFn Debug - currentPage:', currentPage);
        console.log('🔧 QueryFn Debug - totalSku:', totalSku);
        console.log('🔧 QueryFn Debug - PRODUCT_PAGE_SIZE:', PRODUCT_PAGE_SIZE);
        console.log('🔧 QueryFn Debug - calculated maxPages:', maxPages);

        return {
          products,
          nextPage: currentPage + 1,
          total_sku: totalSku,
          maxPages: maxPages,
        };
      } catch (error) {
        console.error('🚨 getWidgetProducts API Failed:', error);
        console.error('🚨 Widget:', widget.title_url, 'Page:', currentPage);

        // Reset empty products state
        setIsEmptyProducts(true);

        // Return empty result to stop pagination
        return {
          products: [],
          total_sku: 0,
          nextPage: undefined, // This stops further pagination
          maxPages: 0, // No pages available
        };
      }
    },
    getNextPageParam: (lastPage, allPages) => {
      // For non-paginated types or error cases, return undefined
      if (!lastPage.nextPage || lastPage.maxPages === 0) {
        console.log(
          '🛑 Pagination stopped - no nextPage or maxPages is 0 (error case)',
        );
        return undefined;
      }

      console.log('🔄 Pagination Debug - widget.title_url:', widget.title_url);
      console.log(
        '🔄 Pagination Debug - widget.title_type:',
        widget.title_type,
      );
      console.log(
        '🔄 Pagination Debug - lastPage.nextPage:',
        lastPage.nextPage,
      );
      console.log(
        '🔄 Pagination Debug - lastPage.products.length:',
        lastPage.products?.length || 0,
      );
      console.log(
        '🔄 Pagination Debug - lastPage.maxPages:',
        lastPage.maxPages,
      );
      console.log('🔄 Pagination Debug - allPages.length:', allPages.length);

      // Calculate current page number (allPages.length represents pages loaded so far)
      const currentPage = allPages.length;

      // FIXED: Always use the CURRENT page's maxPages, not the first page's
      // This ensures each widget uses its own correct maxPages value
      const maxPages = lastPage?.maxPages || 1;

      console.log('🔄 Pagination Debug - currentPage:', currentPage);
      console.log('🔄 Pagination Debug - maxPages (from lastPage):', maxPages);
      console.log(
        '🔄 Pagination Debug - hasNextPage should be:',
        currentPage < maxPages,
      );

      // If we've reached the maximum number of pages, stop paginating
      if (currentPage >= maxPages) {
        console.log('🛑 Pagination stopped - reached maxPages limit');
        return undefined;
      }

      // Continue to next page even if current page has empty products
      // This fixes the issue where empty product arrays stop pagination prematurely
      console.log('✅ Pagination continuing to page:', lastPage.nextPage);
      return lastPage.nextPage;
    },
    initialPageParam: 1,
    enabled: true,
    staleTime: 1000 * 3 * 60, // 3 mins
    gcTime: 1000 * 60 * 10, // 4 minutes
    retry: 0, // Don't retry on error to prevent infinite loops
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    // placeholderData: (previousData, previousQuery) => previousData,
  });

  // Flatten products from all pages
  const products = data?.pages?.flatMap(page => page?.products) || [];

  const fetchServiceabilityAndHcatConfigData = async () => {
    serviceabilityData.current = await serviciablityControllerNew.getCachedServiceabilityData();
    hcatConfigData.current = await serviciablityControllerNew.getCachedHcatConfigData();
  };

  useEffect(() => {
    fetchServiceabilityAndHcatConfigData();
  }, []);

  // Auto-fetch next page if we have empty products but more pages available
  // This handles the case where empty pages cause FlatList onEndReached to not trigger
  React.useEffect(() => {
    if (
      !isLoading &&
      !isFetchingNextPage &&
      hasNextPage &&
      isEmptyProducts &&
      data?.pages &&
      data.pages.length > 0
    ) {
      console.log(
        '🚀 Auto-fetching next page due to empty products but hasNextPage=true',
      );
      const timeoutId = setTimeout(() => {
        fetchNextPage();
      }, 300); // Reduced delay for faster response

      return () => clearTimeout(timeoutId);
    }
  }, [
    isEmptyProducts,
    hasNextPage,
    isLoading,
    isFetchingNextPage,
    fetchNextPage,
    data?.pages,
  ]);

  return {
    products,
    isLoading,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
    refetch,
    config,
    serviceabilityData: serviceabilityData.current,
    hcatConfigData: hcatConfigData.current
  };
};

export default useJMWidgetsWithProductsController;
