import { Dimensions, StyleSheet } from "react-native";
import { rh, rw } from "../../../jiomart-common/src/JMResponsive";

const {width} = Dimensions.get('window');
export const styles = StyleSheet.create({
    closeBtn: {
      alignSelf: 'flex-end',
      padding: 10,
      marginTop: rh(6),
      marginRight: rw(6),
    },
    container: {
      flexDirection: 'column',
      alignItems: 'center',
      paddingTop: rh(0),
      paddingBottom: rh(24),
    },
    titleText: {
      marginBottom: rh(2), 
      marginTop: rh(-5)
    },
    subtitleContainer: {
      flexDirection: 'row', 
      gap: rw(6), 
      alignItems: 'center',
      justifyContent: "center"
    },
    quickIcon: {
      width: rw(63), 
      height: rh(15)
    },
    bannerImage: {
      width: width - (rw(24) * 2),
      height: rh(160),
      marginHorizontal: rw(24),
      alignSelf: "center"
    },
    actionBtn: {
      width: width - (rw(24) * 2),
      height: rh(40),
      backgroundColor: '#0078AD',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 200,
    }
  })
  