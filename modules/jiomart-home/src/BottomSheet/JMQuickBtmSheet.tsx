import {Jio<PERSON><PERSON>, JioText} from '@jio/rn_components';
import {
  IconColor,
  IconSize,
  JioTypography,
} from '@jio/rn_components/src/index.types';
import React, {useEffect} from 'react';
import {Image, ImageBackground, Pressable, View} from 'react-native';
import {useConfigFile} from '../../../jiomart-general/src/hooks/useJMConfig';
import {JMConfigFileName} from '../../../jiomart-networkmanager/src/JMConfigFileManager/JMFileName';
import {addStringPref} from '../../../jiomart-common/src/JMAsyncStorageHelper';
import {AsyncStorageKeys} from '../../../jiomart-common/src/JMConstants';
import {QuickBottomShetIdentifierBeanType} from '../types/QuickBottomSheetTypes';
import {styles} from '../styles/QuickBtmSheetStyles';
import type {BottomSheetChildren} from '../../../jiomart-general/src/ui/BottomSheet/types/BottomSheetType';
import {rh} from '../../../jiomart-common/src/JMResponsive';

interface JMQuickBtmSheetType extends BottomSheetChildren {
  quickBottomSheetIdentifierBean: QuickBottomShetIdentifierBeanType;
  onClose: () => void;
}

const JMQuickBtmSheet = (props: JMQuickBtmSheetType) => {
  const {quickBottomSheetIdentifierBean, onClose, close} = props;

  const commanConfigData = useConfigFile(
    JMConfigFileName.JMCommonContentFileName,
  );
  const quickBottomSheetConfig =
    commanConfigData?.quickCommerceConfig?.QuickBottomSheetViewDetails?.[0];

  useEffect(() => {
    const addToAsyncStorage = async () => {
      await addStringPref(AsyncStorageKeys.QUICK_BOTTOMSHEET_SHOWN, 'true');
    };
    addToAsyncStorage();
  }, []);

  return (
    <View>
      {quickBottomSheetConfig?.IsCloseIconVisible && (
        <Pressable
          onPress={() => {
            close?.(onClose);
          }}
          style={styles.closeBtn}>
          <JioIcon
            ic={quickBottomSheetConfig?.CloseButtonIconAsset ?? 'IcClose'}
            color={IconColor.PRIMARY60}
            size={IconSize.SMALL}
          />
        </Pressable>
      )}
      <View style={styles.container}>
        <JioText
          text={quickBottomSheetIdentifierBean?.TitleText}
          appearance={JioTypography.BODY_S_BOLD}
          color="primary_80"
          style={styles.titleText}
        />
        <ImageBackground
          source={{
            uri: quickBottomSheetConfig?.backgroundImage,
          }}
          style={{width: '100%', paddingBottom: rh(28)}}
          resizeMode="stretch">
          <View style={styles.subtitleContainer}>
            <JioText
              text={quickBottomSheetIdentifierBean?.SubTitleText}
              appearance={JioTypography.BODY_S}
              color="primary_grey_80"
            />
            {quickBottomSheetConfig?.ShowQuickIcon && (
              <Image
                source={{
                  uri: quickBottomSheetIdentifierBean?.SubTitleQuickIconAsset,
                }}
                style={styles.quickIcon}
                resizeMode="cover"
              />
            )}
          </View>
          <Image
            source={{
              uri: quickBottomSheetIdentifierBean?.BannerImage,
            }}
            style={styles.bannerImage}
            resizeMode="cover"
          />
        </ImageBackground>
        {quickBottomSheetConfig?.IsButtonVisible && (
          <Pressable
            style={styles.actionBtn}
            onPress={() => {
              close?.(onClose);
            }}>
            <JioText
              text={quickBottomSheetConfig?.ButtonText ?? 'Got it'}
              appearance={JioTypography.BODY_S_BOLD}
              color="white"
            />
          </Pressable>
        )}
      </View>
    </View>
  );
};

export default JMQuickBtmSheet;
