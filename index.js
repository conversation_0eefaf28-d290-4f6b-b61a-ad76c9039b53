/**
 * @format
 */

import {AppRegistry} from 'react-native';
import App from './App';
import {name as appName} from './app.json';
import JioCodePush from '@jio/codepush';

const BaseApp = props => {
  return <App props={props} />;
};

AppRegistry.registerComponent(appName, () => {
  return initialProps => {
    const AppWrapper = JioCodePush(initialProps)(BaseApp);
    return <AppWrapper {...initialProps} />;
  };
});
